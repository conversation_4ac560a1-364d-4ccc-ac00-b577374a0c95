{"UI.FormComponents.required": "påkrævet", "app.Admin.ManagementFeed.action": "Handling", "app.Admin.ManagementFeed.after": "<PERSON><PERSON>", "app.Admin.ManagementFeed.before": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.changed": "Modificeret", "app.Admin.ManagementFeed.created": "Oprettet", "app.Admin.ManagementFeed.date": "Da<PERSON>", "app.Admin.ManagementFeed.deleted": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.folder": "Mappe", "app.Admin.ManagementFeed.idea": "{tenantName, select, DeloitteDK {Indlæg} other {Idé}}", "app.Admin.ManagementFeed.in": "i projekt {project}", "app.Admin.ManagementFeed.item": "Vare", "app.Admin.ManagementFeed.key": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.managementFeedNudge": "Adgang til ledelsesfeed er ikke inkluderet i din nuværende licens. Kontakt Sø<PERSON> for at få mere at vide om det.", "app.Admin.ManagementFeed.noActivityFound": "Ingen aktivitet fundet", "app.Admin.ManagementFeed.phase": "Fase", "app.Admin.ManagementFeed.project": "Projekt", "app.Admin.ManagementFeed.projectReviewApproved": "Projekt godkendt", "app.Admin.ManagementFeed.projectReviewRequested": "Anmodning om projektgennemgang", "app.Admin.ManagementFeed.title": "<PERSON><PERSON> til led<PERSON>en", "app.Admin.ManagementFeed.user": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.userPlaceholder": "<PERSON><PERSON><PERSON><PERSON> en bruger", "app.Admin.ManagementFeed.value": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.viewDetails": "<PERSON> <PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.warning": "Eksperimentel funktion: En minimal liste over udvalgte handlinger udført af administratorer eller ledere i løbet af de sidste 30 dage. Ikke alle handlinger er inkluderet.", "app.Admin.Moderation.managementFeed": "<PERSON><PERSON> til led<PERSON>en", "app.Admin.Moderation.participationFeed": "Feed til deltagelse", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "<PERSON>r du sikker?", "app.components.Admin.Campaigns.clicked": "Klikede på", "app.components.Admin.Campaigns.deleteCampaignButton": "Slet kampagne", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Acceptere<PERSON>", "app.components.Admin.Campaigns.deliveryStatus_bounced": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Klikede på", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "<PERSON><PERSON> v<PERSON>, hvor mange modtagere der har klikket på et link i e-mailen. Bemærk, at nogle sikkerhedssystemer kan følge links automatisk for at scanne dem, hvilket kan resultere i falske klik.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Leveret", "app.components.Admin.Campaigns.deliveryStatus_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_opened": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "<PERSON><PERSON> v<PERSON>, hvor mange modtagere der har åbnet e-mailen. Bemærk, at nogle sikkerhedssystemer (som Microsoft Defender) kan forudindlæse indhold til scanning, hvilket kan resultere i falske åbninger.", "app.components.Admin.Campaigns.deliveryStatus_sent": "Send<PERSON>", "app.components.Admin.Campaigns.draft": "Udkast", "app.components.Admin.Campaigns.from": "<PERSON>a", "app.components.Admin.Campaigns.manageButtonLabel": "Administrere", "app.components.Admin.Campaigns.opened": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.project": "Projekt", "app.components.Admin.Campaigns.recipientsTitle": "Modtagere", "app.components.Admin.Campaigns.reply_to": "<PERSON><PERSON> til", "app.components.Admin.Campaigns.sent": "Send<PERSON>", "app.components.Admin.Campaigns.statsButton": "Statistik", "app.components.Admin.Campaigns.subject": "<PERSON><PERSON>", "app.components.Admin.Campaigns.to": "Til", "app.components.Admin.ImageCropper.cropFinalSentence": "Se også: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Hold det vigtigste indhold inden for de stiplede linjer for at sikre, at det altid er synligt.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 på mobil (kun området mellem de stiplede linjer vises)", "app.components.Admin.ImageCropper.cropSentenceOne": "Billedet beskæres automatisk:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} på skrivebordet (vist i fuld bredde)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "an<PERSON><PERSON><PERSON> forhold", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Overskriftstekst for registrerede brugere", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Advarsel: Den udvalgte farve har ikke tilstrækkelig høj kontrast. Dette kan resultere i tekst, der er svær at læse. Udvælg en mørkere farve for at optimere læsbarheden.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "<PERSON><PERSON><PERSON><PERSON><PERSON> til navigationslinjen", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "N<PERSON><PERSON> den er aktiveret, tilfø<PERSON>s et link til alle projektbegivenheder til navigationslinjen.", "app.components.AdminPage.SettingsPage.eventsSection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "H<PERSON>mmeside sektion, der kan tilpasses", "app.components.AnonymousPostingToggle.userAnonymity": "Brugernes anonymitet", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Brugere vil kunne skjule deres identitet for andre brugere, projektmoderatorer og administratorer. Disse bidrag kan stadig blive modereret.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "<PERSON><PERSON> brugerne mulighed for at deltage anonymt", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Brugere kan stadig vælge at deltage med deres rigtige navn, men de vil have muligheden for at indsende bidrag anonymt, hvis de vælger at gøre det. Alle brugere skal stadig overholde de krav, der er angivet i fanen Adgangsrettigheder, for at deres bidrag kan gå igennem. Brugerprofildata vil ikke være tilgængelige ved eksport af deltagelsesdata.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "<PERSON>æs mere om brugeranonymitet i vores {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "support artikel", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "<PERSON><PERSON><PERSON> der tilfø<PERSON> flere pladser, vil din fakturering blive forhøjet. Kontakt Søren <PERSON> på <EMAIL> for at få mere at vide om det.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "<PERSON><PERSON>, fordi du gennemførte undersøgelsen! Du er velkommen til at tage den igen næste kvartal.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Download som pdf", "app.components.FormSync.downloadExcelTemplate": "Download en Excel-skabelon", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel-skabeloner vil ikke indeholde rangordningsspørgsmål, matrixspørgsmål, spørgsmål om filupload og spørgsmål om kortlægningsinput (Drop Pin, Draw Route, Draw Area, ESRI-filupload), da disse ikke understøttes til bulkimport på nuværende tidspunkt.", "app.components.ProjectTemplatePreview.close": "Luk", "app.components.ProjectTemplatePreview.createProject": "Opret projekt", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Opret et projekt baseret på skabelonen ''{templateTitle}''.", "app.components.ProjectTemplatePreview.goBack": "<PERSON><PERSON>", "app.components.ProjectTemplatePreview.goBackTo": "Gå tilbage til {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "CitizenLab-ekspert", "app.components.ProjectTemplatePreview.infoboxLine1": "Ønsker du at bruge denne skabelon til din deltagelsesprojekt?", "app.components.ProjectTemplatePreview.infoboxLine2": "Kontakt den ansvarlige person i din byadministration, eller kontakt en {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Projektma<PERSON>", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Den udvalgtee dato er ugyldig. Giv venligst en dato i følgende format: ÅÅÅÅÅ-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Udvælg venligst en startdato for projektet", "app.components.ProjectTemplatePreview.projectStartDate": "Startdatoen for din projekt", "app.components.ProjectTemplatePreview.projectTitle": "Titlen på din projekt", "app.components.ProjectTemplatePreview.projectTitleError": "Skriv venligst en projekttitel", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Skriv venligst en projekttitel for alle sprog", "app.components.ProjectTemplatePreview.projectsOverviewPage": "side med oversigt over proje<PERSON><PERSON>", "app.components.ProjectTemplatePreview.responseError": "Ups, noget gik galt.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Se flere skabeloner", "app.components.ProjectTemplatePreview.successMessage": "Projektet blev oprettet med succes!", "app.components.ProjectTemplatePreview.typeProjectName": "Indtast navnet på projektet", "app.components.ProjectTemplatePreview.useTemplate": "Brug denne skabelon", "app.components.SeatInfo.additionalSeats": "Yderli<PERSON><PERSON> pladser", "app.components.SeatInfo.additionalSeatsToolTip": "Dette viser antallet af ekstra pladser, du har købt oven i \"Inkluderede pladser\".", "app.components.SeatInfo.adminSeats": "Administrator pladser", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} administrator pladser inkluderet", "app.components.SeatInfo.adminSeatsTooltip1": "Administratorer er ansvarlige for platformen og har administratorrettigheder til alle mapper og projekter. Du kan finde flere oplysninger om de forskellige roller på {visitHelpCenter} .", "app.components.SeatInfo.currentAdminSeatsTitle": "Nuværende administratorpladser", "app.components.SeatInfo.currentManagerSeatsTitle": "Nuværende projektlederpladser", "app.components.SeatInfo.includedAdminToolTip": "Dette viser antallet af ledige pladser for administratorer, der er inkluderet i den årlige kontrakt.", "app.components.SeatInfo.includedManagerToolTip": "Dette viser antallet af ledige pladser for projektledere, der er omfattet af den årlige kontrakt.", "app.components.SeatInfo.includedSeats": "<PERSON>k<PERSON><PERSON><PERSON> pladser", "app.components.SeatInfo.managerSeats": "Projektlederpladser", "app.components.SeatInfo.managerSeatsTooltip": "Mappe/projektledere kan administrere et ubegrænset antal mapper/projekter. Du kan finde flere oplysninger om de forskellige roller på {visitHelpCenter} .", "app.components.SeatInfo.managersIncludedText": "{managerSeats} projektlederpladser inkluderet", "app.components.SeatInfo.remainingSeats": "Resterende pladser", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Antal plad<PERSON> i alt", "app.components.SeatInfo.totalSeatsTooltip": "Her vises det samlede antal pladser i dit abonnement og de ekstra pladser, du har købt.", "app.components.SeatInfo.usedSeats": "Benyttede sæder", "app.components.SeatInfo.view": "Se", "app.components.SeatInfo.visitHelpCenter": "be<PERSON><PERSON><PERSON> vores hjælpecenter", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "<PERSON> plan har {adminSeatsIncluded}. <PERSON><PERSON><PERSON> du har brugt alle pladserne, vil der blive tilføjet ekstra pladser under \"Yderligere pladser\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Din plan har {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}, der er berettiget for mappeledere og projektledere. Når du har brugt alle pladserne, vil ekstra pladser blive tilføjet under \"Yderligere pladser\".", "app.components.UserSearch.addModerators": "Tilføj", "app.components.UserSearch.searchUsers": "Skriv for at søge brugere...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternativ fejlmeddelelse", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Som standard vises følgende fejlmeddelelse til brugerne:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "<PERSON><PERSON><PERSON> fej<PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Du kan overskrive denne medd<PERSON> for hvert sprog ved hjælp af tekstboksen \"Alternativ fejlmeddelelse\" nedenfor. Hvis du lader tekstfeltet være tomt, vises standardmeddelelsen.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "<PERSON><PERSON> <PERSON>, hvad deltagerne vil se, n<PERSON><PERSON> de ikke opfylder kravene til deltagelse.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "<PERSON><PERSON> f<PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Intet spørgsmål valgt. Vælg venligst et spørgsmål først.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "Intet svar", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} svar", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Spørgsmål til undersøgelsen", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} indtil nu", "app.components.admin.DatePhasePicker.Input.openEnded": "<PERSON><PERSON> for alle", "app.components.admin.DatePhasePicker.Input.selectDate": "<PERSON><PERSON><PERSON><PERSON> dato", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "<PERSON><PERSON> s<PERSON>", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "<PERSON><PERSON>", "app.components.admin.Graphs": "Ingen data tilgængelige med de nuværende filtre.", "app.components.admin.Graphs.noDataShort": "Ingen data tilgængelige.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "<PERSON><PERSON><PERSON><PERSON> over tid", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "<PERSON><PERSON><PERSON><PERSON> over tid", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "<PERSON><PERSON><PERSON> over tid", "app.components.admin.InputManager.onePost": "1 Input", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "<PERSON><PERSON> af offlinevalg", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Justering af offline-stemmer", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "<PERSON>ne mulighed giver dig mulighed for at inkludere deltagelsesdata fra andre kilder, f.eks. personlige stemmer eller papirstemmer:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Det vil være visuelt forskelligt fra digitale stemmer.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "Det vil påvirke de endelige afstemningsresultater.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Det vil ikke blive afspejlet i dashboards med deltagelsesdata.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Offline-stemmer for en mulighed kan kun indstilles én gang i et projekt og deles mellem alle faser i et projekt.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Du skal først indtaste det samlede antal offline-deltagere.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Offline-deltagere i alt", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "For at kunne udregne de korrekte resultater skal vi kende det <b>samlede antal offline-deltagere i denne fase.</b>", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "<PERSON><PERSON> venligst kun dem, der deltog offline.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "<PERSON><PERSON><PERSON> af {name}", "app.components.admin.PostManager.PostPreview.assignee": "Modtager", "app.components.admin.PostManager.PostPreview.cancelEdit": "<PERSON><PERSON><PERSON> redigering", "app.components.admin.PostManager.PostPreview.currentStatus": "Nuværende status", "app.components.admin.PostManager.PostPreview.delete": "Slet", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "<PERSON>r du sikker på, at du vil slette dette input? <PERSON><PERSON> handling kan ikke fortrydes.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Er du sikker på, at du ønsker at slette dette input? Det slettes fra alle projektfaser og kan ikke genoprettes.", "app.components.admin.PostManager.PostPreview.edit": "<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "<PERSON><PERSON><PERSON> til<PERSON>", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "<PERSON><PERSON><PERSON> af gange, hvor dette har været medtaget i andre deltageres deltagelsesbudgetter", "app.components.admin.PostManager.PostPreview.picks": "Plukker: {picks<PERSON><PERSON>ber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "<PERSON><PERSON><PERSON> tæller:", "app.components.admin.PostManager.PostPreview.save": "Gem", "app.components.admin.PostManager.PostPreview.submitError": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayer": "Tilføj funktionslag", "app.components.admin.PostManager.addFeatureLayerInstruction": "Kopier URL'en til funktionslaget, der er hostet på ArcGIS Online, og indsæt den i inputfeltet nedenfor:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Tilføj et nyt funktionslag til kortet", "app.components.admin.PostManager.addWebMap": "Til<PERSON><PERSON>j webkort", "app.components.admin.PostManager.addWebMapInstruction": "Kopier portal-ID'et for dit webkort fra ArcGIS Online, og indsæt det i feltet nedenfor:", "app.components.admin.PostManager.allPhases": "<PERSON>e faser", "app.components.admin.PostManager.allProjects": "Alle projekter", "app.components.admin.PostManager.allStatuses": "Alle status", "app.components.admin.PostManager.allTopics": "<PERSON><PERSON> mærker", "app.components.admin.PostManager.anyAssignment": "Alle administratorer", "app.components.admin.PostManager.assignedTo": "Tildelt til {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Tildelt til mig", "app.components.admin.PostManager.assignee": "Modtager", "app.components.admin.PostManager.authenticationError": "Der opstod en godkendelsesfejl under fors<PERSON><PERSON> på at hente dette lag. Tjek venligst URL'en, og at din Esri API-nøgle har adgang til dette lag.", "app.components.admin.PostManager.automatedStatusTooltipText": "Denne status opdateres automatisk, når betingelserne er opfyldt", "app.components.admin.PostManager.bodyTitle": "Beskrivelse", "app.components.admin.PostManager.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.co-sponsors": "Medstillere", "app.components.admin.PostManager.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "<PERSON> betyder, at du mister alle data, der er forbundet med disse input, såsom kommentarer, reaktioner og stemmer. <PERSON><PERSON> handling kan ikke fortrydes.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "<PERSON>r du sikker på, at du vil slette disse input?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "<PERSON> for<PERSON>øger at fjerne denne idé fra en fase, hvor den har fået stemmer. <PERSON><PERSON> du gør dette, vil disse stemmer gå tabt. <PERSON>r du sikker på, at du vil fjerne denne idé fra denne fase?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "<PERSON>, der er forbundet med denne id<PERSON>, vil gå tabt", "app.components.admin.PostManager.components.goToInputManager": "<PERSON><PERSON> til input manager", "app.components.admin.PostManager.components.goToProposalManager": "G<PERSON> til forslagshåndtering", "app.components.admin.PostManager.contributionFormTitle": "Rediger bidrag", "app.components.admin.PostManager.cost": "Omkostninger", "app.components.admin.PostManager.createInput": "Opret input", "app.components.admin.PostManager.createInputsDescription": "Opret et nyt sæt input fra et tidligere projekt", "app.components.admin.PostManager.currentLat": "<PERSON><PERSON>", "app.components.admin.PostManager.currentLng": "Midterste længdegrad", "app.components.admin.PostManager.currentZoomLevel": "Zoomniveau", "app.components.admin.PostManager.defaultEsriError": "Der opstod en fejl under for<PERSON><PERSON><PERSON> på at hente dette lag. Tjek venligst din netværksforbindelse, og at URL'en er korrekt.", "app.components.admin.PostManager.delete": "Slet", "app.components.admin.PostManager.deleteAllSelectedInputs": "Slet {count} indlæg", "app.components.admin.PostManager.deleteConfirmation": "Er du sikker på, at du ønsker at slette dette lag?", "app.components.admin.PostManager.dislikes": "Dislikes", "app.components.admin.PostManager.edit": "<PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "<PERSON>iger projekter", "app.components.admin.PostManager.editStatuses": "Rediger statusser", "app.components.admin.PostManager.editTags": "Rediger tags", "app.components.admin.PostManager.editedPostSave": "Gem", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Import af data fra Esri ArcGIS Online er en add-on-funktion. Tal med din GS-manager for at låse den op.", "app.components.admin.PostManager.esriSideError": "Der opstod en fejl i ArcGIS-applikationen. Vent venligst et par minutter og prøv igen senere.", "app.components.admin.PostManager.esriWebMap": "Esri-webkort", "app.components.admin.PostManager.exportAllInputs": "Eksporter alle indlæg (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Eksporter alle kommentarer (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Eksporter kommentarer til dette projekt (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Eksportér indlæg i dette projekt (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Eksportér de valgte input (.xlsx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Eksporter kommentarer fra de valgte input (.xlsx)", "app.components.admin.PostManager.exportVotesByInput": "Eksporter stemmer efter input (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Eksportér stemmer efter bruger (.xslx)", "app.components.admin.PostManager.exports": "Eksport", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Du må kun uploade kortdata som enten GeoJSON-lag eller import fra ArcGIS Online. Fjern venligst alle nuværende GeoJSON-lag, hvis du ø<PERSON> at tilføje et funktionslag.", "app.components.admin.PostManager.featureLayerTooltop": "Du kan finde URL'en til funktionslaget i højre side af elementets side på ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, h<PERSON><PERSON> deltagere vil se din navn", "app.components.admin.PostManager.feedbackBodyPlaceholder": "<PERSON><PERSON> denne <PERSON>", "app.components.admin.PostManager.fileUploadError": "En eller flere filer blev ikke uploadet. Kontroller venligst filstørrelsen og formatet og prøv igen.", "app.components.admin.PostManager.formTitle": "Rediger idé", "app.components.admin.PostManager.generalApiError2": "Der opstod en fejl under for<PERSON><PERSON><PERSON> på at hente dette element. Tjek venligst, at URL'en eller portal-ID'et er korrekt, og at du har adgang til dette element.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Du kan kun uploade kortdata som enten GeoJSON-lag eller import fra ArcGIS Online. Fjern venligst alle ArcGIS-data, hvis du ønsker at uploade et GeoJSON-lag.", "app.components.admin.PostManager.goToDefaultMapView": "Gå til standardkortcenter", "app.components.admin.PostManager.hiddenFieldsLink": "sk<PERSON><PERSON><PERSON> felter", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Tip: <PERSON><PERSON> du bruger <PERSON>form, kan du tilføje {hiddenFieldsLink} for at holde styr på, hvem der har svaret på din undersøgelse.", "app.components.admin.PostManager.import2": "Import", "app.components.admin.PostManager.importError": "Den udvalgte fil kunne ikke importeres, fordi det ikke er en gyldig GeoJSON-fil", "app.components.admin.PostManager.importEsriFeatureLayer": "Import af Esri-funktionslag", "app.components.admin.PostManager.importEsriWebMap": "Import af Esri-webkort", "app.components.admin.PostManager.importInputs": "Import af input", "app.components.admin.PostManager.imported": "Importeret", "app.components.admin.PostManager.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON> forslag", "app.components.admin.PostManager.inputCommentsExportFileName": "input_kommentarer", "app.components.admin.PostManager.inputImportProgress": "{importedCount} ud af {totalCount} {totalCount, plural, one {input har} other {inputs har}} været importeret. Importen er stadig i gang, så kom tilbage senere.", "app.components.admin.PostManager.inputManagerHeader": "Input", "app.components.admin.PostManager.inputs": "Indlæg", "app.components.admin.PostManager.inputsExportFileName": "input", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "<PERSON>is kun input, der har brug for feedback", "app.components.admin.PostManager.issueFormTitle": "<PERSON><PERSON> s<PERSON>ø<PERSON>", "app.components.admin.PostManager.latestFeedbackMode": "Brug den seneste eksisterende officielle opdatering som en forklaring", "app.components.admin.PostManager.layerAdded": "Lag tilføjet med succes", "app.components.admin.PostManager.likes": "<PERSON>s", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "<PERSON><PERSON> du flytter dette input væk fra det aktuelle projekt, vil oplysningerne om de tildelte faser gå tabt. Ønsker du at fortsætte?", "app.components.admin.PostManager.mapData": "Kortdata", "app.components.admin.PostManager.multipleInputs": "{ideaCount} input", "app.components.admin.PostManager.newFeedbackMode": "Skriv en ny opdatering for at forklare denne ændring", "app.components.admin.PostManager.noFilteredResults": "De valgte filtre returnerede ingen resultater", "app.components.admin.PostManager.noInputs": "Ingen input endnu", "app.components.admin.PostManager.noInputsDescription": "Du kan tilføje dit eget input eller tage udgangspunkt i et tidligere deltagelsesprojekt.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 inputs} one {1 input} other {# inputs}} vil blive importeret fra det valgte projekt og den valgte fase. Importen kører i baggrunden, og inputtene vises i inputmanageren, når den er færdig.", "app.components.admin.PostManager.noOne": "<PERSON><PERSON><PERSON> til<PERSON>", "app.components.admin.PostManager.noProject": "Intet projekt", "app.components.admin.PostManager.officialFeedbackModal.author": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, hvordan dit navn skal vises", "app.components.admin.PostManager.officialFeedbackModal.description": "At give officiel feedback hjælper med at holde processen gennemsigtig og opbygger tillid til platformen.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Forfatter er påkrævet", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Der er brug for feedback", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Officiel feedback", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Forklar årsagen til statusændringen", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Indsend feedback", "app.components.admin.PostManager.officialFeedbackModal.skip": "Spring over denne gang", "app.components.admin.PostManager.officialFeedbackModal.title": "<PERSON><PERSON> din beslutning", "app.components.admin.PostManager.officialUpdateAuthor": "<PERSON><PERSON><PERSON><PERSON>, h<PERSON><PERSON> deltagere vil se din navn", "app.components.admin.PostManager.officialUpdateBody": "<PERSON><PERSON> denne <PERSON>", "app.components.admin.PostManager.offlinePicks": "Offline-valg", "app.components.admin.PostManager.offlineVotes": "Offline-stemmer", "app.components.admin.PostManager.onlineVotes": "Online stemmer", "app.components.admin.PostManager.optionFormTitle": "<PERSON><PERSON> mulighed", "app.components.admin.PostManager.participants": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Online-valg", "app.components.admin.PostManager.pbItemCountTooltip": "Antal<PERSON> af gange dette er inkluderet i andre deltageres borgerbudgetter", "app.components.admin.PostManager.petitionFormTitle": "<PERSON><PERSON> begæring", "app.components.admin.PostManager.postedIn": "Posted i {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Rediger projekt", "app.components.admin.PostManager.projectsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.projectsTabTooltipContent": "Du kan copy/paste indlæg for at flytte dem fra et projekt til et andet. Bemærk, at du for tidslinjeprojekter stadig skal føje indlægget til en bestemt fase.", "app.components.admin.PostManager.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON> forslag", "app.components.admin.PostManager.proposedBudgetTitle": "Foreslået budget", "app.components.admin.PostManager.publication_date": "Offentliggjort den", "app.components.admin.PostManager.questionFormTitle": "<PERSON><PERSON> s<PERSON>ø<PERSON>", "app.components.admin.PostManager.reactions": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "Nulstil filtre", "app.components.admin.PostManager.resetInputFiltersDescription": "Nulstil filtrene for at se alle input.", "app.components.admin.PostManager.saved": "Gemte", "app.components.admin.PostManager.screeningTooltip": "Pre-screening er ikke inkluderet i din nuværende plan. Tal med din Government Success Manager eller administrator for at låse det op.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Screening er slået fra for denne fase. Gå til faseopsætning for at aktivere den", "app.components.admin.PostManager.selectAPhase": "Væ<PERSON>g en fase", "app.components.admin.PostManager.selectAProject": "Vælg et projekt", "app.components.admin.PostManager.setAsDefaultMapView": "Gem det aktuelle midtpunkt og zoomniveau som standardindstillinger for kortet", "app.components.admin.PostManager.startFromPastInputs": "Start med tidligere input", "app.components.admin.PostManager.statusChangeGenericError": "Der opstod en fejl, prøv igen senere, eller kontakt støtte.", "app.components.admin.PostManager.statusChangeSave": "Ændre status", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Skift status for et indlæg ved hjælp af træk og slip. Den oprindelige forfatter og andre bidragydere modtager en meddelelse om den ændrede status.", "app.components.admin.PostManager.submitApiError": "Der var et problem med at indsende formularen. Tjek venligst for eventuelle fejl og prøv igen.", "app.components.admin.PostManager.timelineTab": "Tidslinje", "app.components.admin.PostManager.timelineTabTooltipText": "Træk og slip input for at kopiere dem til forskellige projektfaser.", "app.components.admin.PostManager.title": "Titel", "app.components.admin.PostManager.topicsTab": "<PERSON><PERSON>", "app.components.admin.PostManager.topicsTabTooltipText": "<PERSON><PERSON><PERSON> emner til et input ved hjælp af træk og slip.", "app.components.admin.PostManager.view": "Se", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votesByInputExportFileName": "votes_by_input", "app.components.admin.PostManager.votesByUserExportFileName": "votes_by_user", "app.components.admin.PostManager.webMapAlreadyExists": "Du kan kun tilføje ét webkort ad gangen. Fjern det aktuelle for at importere et andet.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Du må kun uploade kortdata som enten GeoJSON-lag eller import fra ArcGIS Online. Fjern venligst alle nuværende GeoJSON-lag, hvis du ønsker at forbinde et webkort.", "app.components.admin.PostManager.webMapTooltip": "Du kan finde webkortportalens ID på din ArcGIS Online-emneside i højre side.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON> end en dag} one {En dag} other {# dage}} tilbage", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "Ja, slet undersøgelsesresultater", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "<PERSON>te kan ikke fortrydes", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Slet undersøgelsesresultater", "app.components.admin.ProjectEdit.survey.downloadResults2": "Hent undersøgelsesresultater", "app.components.admin.ReportExportMenu.FileName.fromFilter": "fra", "app.components.admin.ReportExportMenu.FileName.groupFilter": "gruppe", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projekt", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tag", "app.components.admin.ReportExportMenu.FileName.untilFilter": "indtil", "app.components.admin.ReportExportMenu.downloadPng": "Download som PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Download som SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Hent Excel", "app.components.admin.SlugInput.regexError": "Slug'en kan kun indeholde almindelige små bogstaver (a-z), tal (0-9) og bindestreger (-). Det første og sidste tegn kan ikke være bindestreger. Det er forbudt at bruge på hinanden følgende bindestreger (--).", "app.components.admin.TerminologyConfig.saveButton": "Gem", "app.components.admin.commonGroundInputManager.title": "Titel", "app.components.admin.seatSetSuccess.admin": "Administartor", "app.components.admin.seatSetSuccess.allDone": "Alt er gjort", "app.components.admin.seatSetSuccess.close": "Luk", "app.components.admin.seatSetSuccess.manager": "Projektleder", "app.components.admin.seatSetSuccess.orderCompleted": "Bestilling afsluttet", "app.components.admin.seatSetSuccess.reflectedMessage": "Ændringerne i dit abonnement vil blive afspejlet i næste gang i bliver faktureret.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} rettigheder er blevet tildelt den eller de valgte brugere.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Er du sikker på, at du ønsker at slette alle undersøgelsesresultater?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "<PERSON>ne deltagelsesmetode er i beta. <PERSON>i udr<PERSON><PERSON> den gradvist for at indsamle feedback og forbedre oplevelsen.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Indsamling af feedback på et dokument er en brugerdefineret funktion og er ikke inkluderet i din nuværende licens. Kontakt din GovSuccess Manager for at høre mere om det.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Bidrag", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Antal dage er påkrævet", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Antal dage til at nå minimumsantallet af stemmer", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Du kan finde flere oplysninger om, hvordan du indlejrer et link til Google Forms, i {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project#h_163e33df30", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "denne <PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "{tenantName, select, DeloitteDK {Indlæg} other {Idé}}", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Forslag", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Hvad skal et input hedde?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "<PERSON><PERSON> linket til dit Konveio-dokument her. Læs vores {supportArticleLink} for mere information om opsætning af Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "supportartikel", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Dette er ikke inkluderet i din nuværende plan. Kontakt din Government Success Manager eller administrator for at låse det op.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Der kræves et maksimumsbudget", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Det maksimale antal stemmer pr. mulighed skal være mindre end eller lig med det samlede antal stemmer.", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "<PERSON> kræves et maksimalt antal stemmer", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Minimumsbudgettet kan ikke være større end maksimumsbudgettet", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Der kræves et minimumsbudget", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Minimumsantallet af stemmer kan ikke være større end maksimumsantallet.", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Der kræves et minimum antal stemmer", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Manglende slutdato", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Manglende startdato", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "<PERSON><PERSON> Input Manager", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Konfigurer afstemningsmulighederne på fanen Input manager, n<PERSON><PERSON> du har oprettet en fase.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Konfigurer afstemningsmulighederne i {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Mu<PERSON>gheder for deltagelse", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Underskriftsindsamling", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administratorer og projektledere", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Annotering af dokument:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} kan deltage i denne fase.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Kommentar:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Fælles grundlag", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Slet fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, slet denne fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "<PERSON>r du sikker på, at du ønsker at slette denne fase?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Alle data vedrørende denne fase vil blive slettet. Dette kan ikke fortrydes.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Annoteringsfase for dokumenter", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Alle", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Ekstern undersøgelsesfase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "Integreret spørgeskema fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Informationsfasen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "<PERSON><PERSON><PERSON> re<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Ingen slutdato", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Afstemningsfase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Forslagsfasen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Reager:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>T<PERSON><PERSON><PERSON>e sig begivenheden:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "<PERSON><PERSON><PERSON><PERSON> brugere", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Indsend input:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Tag afstemning:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Brugere med bekræftet e-mail", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Frivilligt arbejde:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Frivilligheds fase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Afstemning:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Afstemningsfase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Hvem kan deltage?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Inputs vil ikke være synlige, før en administrator har gennemg<PERSON> og godkendt dem. Forfattere kan ikke redigere input, efter at de er blevet screenet eller reageret på.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "<PERSON>n <PERSON>er", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Alle med linket kan interagere med projektudkastet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Godkend", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Godkendelse giver proje<PERSON><PERSON><PERSON> mulighed for at offentliggøre projektet.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Godkendt af {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Arkiveret", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Udkast", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "<PERSON><PERSON> beskrivelse", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Alle", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Grupper", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Skjult", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Offline stemmer", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Kun administratorer{inFolder, select, true { eller mappeadministratorer} other {}} kan udgive projektet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 deltager} other {{participantsCount} deltagere}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Deltagere i indlejrede metoder (f.eks. eksterne undersøgelser)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Følgere af et projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Bemærk: Aktivering af anonyme eller åbne deltagelsestilladelser kan gøre det muligt for brugere at deltage flere gange, hvilket fører til misvisende eller ufuldstændige brugerdata.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "<PERSON>gere er <b>ikke inkluderet</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Deltagerne omfatter:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Tilmeldte til arrangementet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "<PERSON><PERSON><PERSON>, der interagerer med Go Vocal metoder", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Venter på godkendelse", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Projektets bedømmere har fået besked.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Offentlig", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Udgiv", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Udgivet - Aktiv", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Udgivet - Færdig", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Opdater link til forhåndsvisning af projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Genskab linket til forhåndsvisning af projektet. Dette vil ugyldiggøre det tidligere link.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "<PERSON><PERSON><PERSON> links holder op med at virke, men du kan til enhver tid generere et nyt.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Er du sikker på det? Dette vil deaktivere det aktuelle link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON>, opdater link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "<PERSON><PERSON><PERSON> om godken<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Projektet skal godkendes af en administrator{inFolder, select, true { eller en af Folder Managers} other {}} , før du kan udgive det. Klik på knappen nedenfor for at anmode om godkendelse.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Del", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Kopier link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON> k<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Deling af private links er ikke inkluderet i din nuværende plan. Tal med S<PERSON><PERSON> el<PERSON> administrator for at låse det op.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Del dette projekt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "<PERSON><PERSON>m har adgang", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "Se", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projekt", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Forslag", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Minimum antal stemmer for at komme i betragtning", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Minimum antal stemmer er påkrævet", "app.components.app.containers.AdminPage.ProjectEdit.report": "Rapport", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Kræv screening af input", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Tidslinje", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Trafik", "app.components.formBuilder.cancelMethodChange1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.changeMethodWarning1": "At skifte metode kan føre til at inputdata, der er genereret eller modtaget under brug af den tidligere metode skjules.", "app.components.formBuilder.changingMethod1": "Ændring af metode", "app.components.formBuilder.confirmMethodChange1": "Ja, fortsæt", "app.components.formBuilder.copySurveyModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.description": "Dette vil kopiere alle spørgsmålene og logikken uden svarene.", "app.components.formBuilder.copySurveyModal.duplicate": "Lav en kopi", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "Ingen passende faser fundet i dette projekt", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Ingen fase valgt. Vælg venligst en fase først.", "app.components.formBuilder.copySurveyModal.noProject": "Intet projekt", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Intet projekt valgt. Vælg venligst et projekt først.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Du har allerede gemt ændringer til denne survey. Hvis du duplikerer en anden survey, vil ændringerne gå tabt.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Survey fase", "app.components.formBuilder.copySurveyModal.title": "Vælg en survey, der skal kopieres", "app.components.formBuilder.editWarningModal.addOrReorder": "Tilføj eller omorganiser spørgsmål", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "<PERSON>e svardata kan være unøjagtige", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Rediger tekst", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Retter du en stavefejl? Det vil ikke påvirke dine svardata", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Du mister de svard<PERSON>, der er knyttet til det spørgsmål.", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Slet et spørgsmål", "app.components.formBuilder.editWarningModal.exportYouResponses2": "eksportér dine svar.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "<PERSON><PERSON><PERSON>: Du kan miste svardata for altid. <PERSON><PERSON><PERSON>,", "app.components.formBuilder.editWarningModal.noCancel": "<PERSON><PERSON>, afbryd", "app.components.formBuilder.editWarningModal.title4": "Rediger igangværende undersøgelse", "app.components.formBuilder.editWarningModal.yesContinue": "Ja, fortsæt", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "<PERSON><PERSON><PERSON><PERSON> for adgangsrettigheder til denne undersøgelse", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "\"Demografiske felter i surveyformularen\" er aktiveret. Når surveyformularen vises, vil alle konfigurerede demografiske spørgsmål blive tilføjet på en ny side umiddelbart før afslutningen af undersøgelsen. Disse spørgsmål kan ændres på {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "indstillinger for adgangsrettigheder til denne fase.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Respondenterne skal ikke tilmelde sig eller logge ind for at indsende svar, hvilket kan resultere i dobbelte indsendelser.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Ved at springe tilmeldings-/login-trinnet over, accepterer du ikke at indsamle demografiske oplysninger om respondenterne, hvilket kan påvirke dine dataanalysefunktioner.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "<PERSON><PERSON> undersøgelse er indstillet til at tillade adgang for \"Alle\" under <PERSON><PERSON>gangsrettigheder.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "<PERSON><PERSON> du <PERSON> at ændre dette, kan du gøre det på {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Du stiller følgende demografiske spørgsmål til respondenterne i undersøgelsen gennem tilmeldings-/login-trinnet.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "For at strømline indsamlingen af demografiske oplysninger og sikre, at de integreres i din brugerdatabase, anbefaler vi, at du indarbejder alle demografiske spørgsmål direkte i tilmeldings-/loginprocessen. For at gøre dette skal du bruge {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Bed brugerne om at følge områder eller emner", "app.components.onboarding.followHelperText": "<PERSON>te aktiverer et trin i registreringsprocessen, hvor brugerne vil kunne følge omr<PERSON>der eller emner, som du vælger nedenfor", "app.components.onboarding.followPreferences": "<PERSON><PERSON><PERSON><PERSON> præ<PERSON>r", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} inden for planen, {noOfAdditionalSeats} yderligere", "app.components.seatsWithinPlan.seatsWithinPlanText": "<PERSON><PERSON><PERSON> inden for planen", "app.containers.Admin.Campaigns.campaignFrom": "Fra:", "app.containers.Admin.Campaigns.campaignTo": "Til:", "app.containers.Admin.Campaigns.customEmails": "Brugerdefinerede e-mails", "app.containers.Admin.Campaigns.customEmailsDescription": "Send tilpassede e-mails og tjek statistik.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON> <PERSON>, men det ser ud til, at du ikke har adgang til e-mailsektionen", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automatiserede e-mails", "app.containers.Admin.Insights.tabReports": "Rapporter", "app.containers.Admin.Invitations.a11y_removeInvite": "<PERSON><PERSON><PERSON> invitation", "app.containers.Admin.Invitations.addToGroupLabel": "Tilføj disse deltagere til specifikke manuelle brugergrupper", "app.containers.Admin.Invitations.adminLabel1": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.adminLabelTooltip": "<PERSON><PERSON><PERSON> du <PERSON>d<PERSON><PERSON><PERSON> den<PERSON>, <PERSON><PERSON><PERSON>, du <PERSON><PERSON>, adgang til alle dine platformsindstillinger.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> invitationerne", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Der er ingen invitationer, der matcher din søgning", "app.containers.Admin.Invitations.deleteInvite": "Slet", "app.containers.Admin.Invitations.deleteInviteConfirmation": "<PERSON>r du sikker på, at du vil slette denne invitation?", "app.containers.Admin.Invitations.deleteInviteTooltip": "<PERSON><PERSON> du annullerer en invitation, kan du sende en invitation til denne person igen.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Download og udfyld skabelonen", "app.containers.Admin.Invitations.downloadTemplate": "Download skabelon", "app.containers.Admin.Invitations.email": "E-mail", "app.containers.Admin.Invitations.emailListLabel": "Indtast manuelt e-mailadresserne på de deltagere, du vil invitere. Adskil hver adresse med et komma.", "app.containers.Admin.Invitations.exportInvites": "Eksporter alle invitationer", "app.containers.Admin.Invitations.fileRequirements": "Vigtigt: For at kunne sende invitationerne korrekt kan ingen kolonne fjernes fra importskabelonen. Lad ubrugte kolonner være tomme.", "app.containers.Admin.Invitations.filetypeError": "Forkert filtype. Kun XLSX-filer understø<PERSON>.", "app.containers.Admin.Invitations.groupsPlaceholder": "Ingen gruppe udvalgte", "app.containers.Admin.Invitations.helmetDescription": "Inviter brugere til platformen", "app.containers.Admin.Invitations.helmetTitle": "Admin-invitationsdashboard", "app.containers.Admin.Invitations.importOptionsInfo": "Der tages kun hensyn til disse indstil<PERSON>, hvis de ikke er defineret i Excel-filen.\n      Besøg venligst {supportPageLink} for at få flere information.", "app.containers.Admin.Invitations.importTab": "Import af e-mail-adresser", "app.containers.Admin.Invitations.invitationExpirationWarning": "<PERSON><PERSON><PERSON> opmærksom på, at invitationer udl<PERSON>ber efter 30 dage. Efter denne periode kan du stadig gensende dem igen.", "app.containers.Admin.Invitations.invitationOptions": "<PERSON><PERSON><PERSON><PERSON> for invitationer", "app.containers.Admin.Invitations.invitationSubtitle": "Invitér deltagere til platformen på et hvilket som helst tidspunkt. De får en neutral invitationse-mail med din logo, hvori de bliver bedt om at registrere sig på platformen.", "app.containers.Admin.Invitations.invitePeople": "<PERSON><PERSON><PERSON><PERSON> delta<PERSON> via e-mail", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "Acceptere<PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteTextLabel": "Indtast eventuelt en besked, som vil blive tilføjet til invitationsmailen.", "app.containers.Admin.Invitations.invitedSince": "Inviteret", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "<PERSON><PERSON><PERSON><PERSON> sprog for invitationen", "app.containers.Admin.Invitations.moderatorLabel": "Giv disse deltagere projektstyringsrettigheder", "app.containers.Admin.Invitations.moderatorLabelTooltip": "<PERSON><PERSON><PERSON> du har valgt denne indstilling, til<PERSON><PERSON> den/de inviterede projektlederrettigheder til det/de udvalgte projekt(er). Flere information om projektlederrettigheder {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "her", "app.containers.Admin.Invitations.name": "Navn", "app.containers.Admin.Invitations.processing": "Udsendelse af invitationer. Vent venligst...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Ingen projekt(er) udvalgte", "app.containers.Admin.Invitations.save": "Send invitationer ud", "app.containers.Admin.Invitations.saveErrorMessage": "Der er opstået en eller flere fejl, og invitationerne blev ikke sendt ud. Ret venligst den eller de fejl, der er anført nedenfor, og prøv igen.", "app.containers.Admin.Invitations.saveSuccess": "Succes!", "app.containers.Admin.Invitations.saveSuccessMessage": "Invitationen er blevet sendt med succes.", "app.containers.Admin.Invitations.supportPage": "støtteside", "app.containers.Admin.Invitations.supportPageLinkText": "<PERSON><PERSON><PERSON><PERSON> stø<PERSON>", "app.containers.Admin.Invitations.tabAllInvitations": "Alle invitationer", "app.containers.Admin.Invitations.tabInviteUsers": "<PERSON><PERSON><PERSON> brugere", "app.containers.Admin.Invitations.textTab": "Indtast e-mailadresser manuelt", "app.containers.Admin.Invitations.unknownError": "Noget gik galt. Prøv venligst igen senere.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Upload din færdige skabelonfil", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink}, hvis du vil have flere information om alle understøttede kolonner i importskabelonen.", "app.containers.Admin.Moderation.all": "Alle", "app.containers.Admin.Moderation.belongsTo": "Tilhører til", "app.containers.Admin.Moderation.collapse": "se mindre", "app.containers.Admin.Moderation.comment": "Kommentar", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Slet", "app.containers.Admin.Moderation.confirmCommentDeletion": "Er du sikker på, at du vil slette denne kommentar? Dette er permanent og kan ikke fortrydes.", "app.containers.Admin.Moderation.content": "Indhold", "app.containers.Admin.Moderation.date": "Da<PERSON>", "app.containers.Admin.Moderation.deleteComment": "Slet kommentar", "app.containers.Admin.Moderation.goToComment": "Åbn denne kommentar i en ny fane", "app.containers.Admin.Moderation.goToPost": "Åbn dette indlæg i en ny fane", "app.containers.Admin.Moderation.goToProposal": "Åbn dette forslag i en ny fane", "app.containers.Admin.Moderation.markFlagsError": "Kunne ikke markere punkt(er). Prøv igen.", "app.containers.Admin.Moderation.markNotSeen": "<PERSON><PERSON><PERSON> {selectedItemsCount, plural, one {# genstand} other {# genstande}} som ikke set", "app.containers.Admin.Moderation.markSeen": "<PERSON><PERSON><PERSON> {selectedItemsCount, plural, one {# genstand} other {# genstande}} som set", "app.containers.Admin.Moderation.moderationsTooltip": "Denne side giver dig mulighed for hurtig<PERSON> at kontrollere alle nye indlæg, der er blevet offentliggjort på din platform, herunder idéer og kommentarer. Du kan markere indlæg som værende \"set\", så andre ved, hvad der stadig skal behandles.", "app.containers.Admin.Moderation.noUnviewedItems": "Der er ingen usete poster", "app.containers.Admin.Moderation.noViewedItems": "Der er ingen sete poster", "app.containers.Admin.Moderation.pageTitle1": "Feed", "app.containers.Admin.Moderation.post": "Indlæg", "app.containers.Admin.Moderation.profanityBlockerSetting": "Profanity blocker", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Bloker indl<PERSON>g, der indeholder de mest almindeligt rapporterede stødende ord.", "app.containers.Admin.Moderation.project": "{tenantName, select, DeloitteDK {Verdensmål} other {Projekt}}", "app.containers.Admin.Moderation.read": "Set", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON><PERSON> mere", "app.containers.Admin.Moderation.removeFlagsError": "<PERSON>nne ikke fjerne ad<PERSON>(er). Prøv igen.", "app.containers.Admin.Moderation.rowsPerPage": "<PERSON><PERSON><PERSON> pr. side", "app.containers.Admin.Moderation.settings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.settingsSavingError": "<PERSON>nne ikke gemme. Prøv at ændre indstillingen igen.", "app.containers.Admin.Moderation.show": "Vis", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Indstillingerne er blevet opdateret med succes.", "app.containers.Admin.Moderation.type": "Type", "app.containers.Admin.Moderation.unread": "Ikke set", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Denne side består af følgende afsnit. Du kan slå dem til/fra og redigere dem efter behov.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Sektioner", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Vis side", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Ikke vist på siden", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "<PERSON>ises på siden", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> filer", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> filer (maks. 50 MB), som vil være tilgængelige til download fra siden.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Nederste info-afsnit", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Til<PERSON><PERSON><PERSON> dit eget indhold til den tilpassede sektion nederst på siden.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Liste over <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "<PERSON>is begivenheder i forbindelse med projekterne.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Banner for helten", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Tilpas sidens bannerbillede og tekst.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Liste over projekter", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Vis projekterne baseret på dine sideindstillinger. Du kan få vist en forhåndsvisning af de projekter, der vil blive vist.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Afsnit for øverste info", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> dit eget indhold til den sektion, der kan tilpasses øverst på siden.", "app.containers.Admin.PagesAndMenu.addButton": "Tilføj til navigationslinjen", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Navn i navbar", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "<PERSON>r du sikker på, at du vil slette denne side? Dette kan ikke fortrydes.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Angiv en titel for alle sprog", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "<PERSON> sider", "app.containers.Admin.PagesAndMenu.components.savePage": "Gem side", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Side gemt med succes", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Vedhæftede filer (maks. 50 MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Succes", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Indhold", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "<PERSON>nne ikke gemme vedhæftede filer", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Filer bør ikke være større end 50 Mb. Tilføjede filer vil blive vist nederst på denne side", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Vedhæftede filer gemt", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Vedhæftede filer | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> filer", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Gem og aktiver vedhæftede filer", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Gem vedhæ<PERSON>e filer", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Tilvejebring indhold på alle sprog", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Succes", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Indhold", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Kunne ikke gemme nederste infosektion", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Nederste info-afsnit blev gemt", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Nederste info-afsnit", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Gem og aktiver nederste info-afsnit", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Gem nederste info-afsnit", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Oprettelse af brugerdefinerede sider er ikke inkluderet i din nuværende licens. Kontakt Sø<PERSON> for at få mere at vide om det.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "<PERSON><PERSON><PERSON>g venligst mindst ét tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Succes", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Efter tag(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Visning af projekter efter tag eller område er ikke en del af din nuværende licens. Kontakt Sø<PERSON> for at få mere at vide om det.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Indhold", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Rediger brugerdefineret side", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Forbundne projekter", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "<PERSON><PERSON><PERSON><PERSON>, hvilke projekter og relaterede begivenheder der kan vises på siden.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Siden er oprettet med succes", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Side gemt med succes", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Brugerdefineret side gemt", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Titel i navigationslinjen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Opret brugerdefineret side | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Opret brugerdefineret side", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Ingen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "<PERSON><PERSON><PERSON><PERSON> for siden", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Gem brugerdefineret side", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "<PERSON><PERSON><PERSON><PERSON> venligst et område", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Valgte tags", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "Slug'en kan kun indeholde almindelige små bogstaver (a-z), tal (0-9) og bindestreger (-). Det første og sidste tegn kan ikke være bindestreger. Det er forbudt at bruge på hinanden følgende bindestreger (--).", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Du skal indtaste en slug", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Titel", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Indtast en titel på alle sprog", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Indtast en titel", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Vis brugerdefineret side", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "Knap", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Rediger brugerdefineret side | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Sideindhold", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "<PERSON><PERSON> slutdatoen er tom og beskrivelsen ikke er udfyldt, vil der ikke blive vist en tidslinje på projektsiden for enkeltfaseprojekter.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Ingen tilgængelige projekter baseret på dit {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Dette projekt har intet tag- eller områdefilter, så der vises ingen projekter.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Liste over projekter | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "sideindstillinger", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Liste over projekter", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Følgende projekter vil blive vist på denne side baseret på din {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "STANDARD", "app.containers.Admin.PagesAndMenu.deleteButton": "Slet", "app.containers.Admin.PagesAndMenu.editButton": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Succes", "app.containers.Admin.PagesAndMenu.heroBannerError": "<PERSON><PERSON> ikke gemme heltebanner", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Helte-banner gemt", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Gem heltebanner", "app.containers.Admin.PagesAndMenu.homeTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Tilvejebringe indhold på mindst ét sprog", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Du kan kun tilføje op til 5 elementer til navigationslinjen", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Sider og menu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Fjern fra navigation<PERSON>linjen", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Gem og aktiver heltebanner", "app.containers.Admin.PagesAndMenu.title": "Sider og menu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Succes", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Indhold", "app.containers.Admin.PagesAndMenu.topInfoError": "Kunne ikke gemme øverste infosektion", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Øverste infosektion gemt", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Top info sektion | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Afsnit for øverste info", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Gem og aktiver øverste info-afsnit", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Gem øverste info-afsnit", "app.containers.Admin.PagesAndMenu.viewButton": "Se", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Alder", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Fællesskabet", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Sammenfatning", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Inklusionsindikatorer på højeste niveau", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "Følgende afsnit beskriver inklusionsindikatorer, der fremhæver vores fremskridt mod at fremme en mere inkluderende og repræsentativ inddragelsesplatform.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "deltagere", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "<PERSON><PERSON><PERSON><PERSON><PERSON> for deltagelse på højeste niveau", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "Følgende afsnit skitserer de vigtigste deltagelsesindikatorer for det valgte tidsrum og giver et overblik over engagementstendenser og resultatmålinger.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "offentliggjorte projekter", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Rapport om platformen", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "<PERSON><PERSON> proje<PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "Det følgende afsnit giver et overblik over offentligt synlige projekter, der overlapper med det valgte tidsrum, de mest anvendte metoder i disse projekter og målinger af den samlede deltagelse.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Tidslinje for registreringer", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Blokerede brugere", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Administrer blokerede brugere.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Slet gruppe", "app.containers.Admin.Users.GroupsHeader.editGroup": "Rediger gruppe", "app.containers.Admin.Users.GroupsPanel.admins": "Administratorer", "app.containers.Admin.Users.GroupsPanel.allUsers": "<PERSON><PERSON><PERSON><PERSON> brugere", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grupper", "app.containers.Admin.Users.GroupsPanel.managers": "Projektledere", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "<PERSON><PERSON><PERSON><PERSON> emner", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Få et overblik over alle de deltagere og organisationer, der har registreret sig på platformen. Tilføj et udvalg af brugere til manuelle grupper eller smarte grupper.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Invitation afventer", "app.containers.Admin.Users.admin": "Administrator", "app.containers.Admin.Users.assign": "Tilføj til mine udgifter ", "app.containers.Admin.Users.assignedItems": "Tildelte emner til {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "<PERSON><PERSON><PERSON> en ekstra plads", "app.containers.Admin.Users.changeUserRights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.confirm": "Bekræft", "app.containers.Admin.Users.confirmAdminQuestion": "<PERSON>r du sikker på, at du ønsker at give {name} platformen administratorrettigheder?", "app.containers.Admin.Users.confirmNormalUserQuestion": "<PERSON>r du sikker på, at du ønsker at indstille {name} som en normal bruger?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "<PERSON>r du sikker på, at du vil indstille {name} som en normal bruger? Vær opmærksom på, at de mister projektlederrettighederne til alle de projekter og mapper, som de er tildelt ved bekræftelsen.", "app.containers.Admin.Users.deleteUser": "Slet bruger", "app.containers.Admin.Users.email": "E-mail", "app.containers.Admin.Users.folder": "Mappe", "app.containers.Admin.Users.folderManager": "Mappeadministrator", "app.containers.Admin.Users.helmetDescription": "Brugerliste i admin", "app.containers.Admin.Users.helmetTitle": "Admin - brugernes dashboard", "app.containers.Admin.Users.inviteUsers": "<PERSON><PERSON><PERSON> brugere", "app.containers.Admin.Users.joined": "Kom med", "app.containers.Admin.Users.lastActive": "Sidste aktive", "app.containers.Admin.Users.name": "Navn", "app.containers.Admin.Users.noAssignedItems": "Ingen tildelte emner", "app.containers.Admin.Users.options": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.permissionToBuy": "<PERSON><PERSON> du vil give {name} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, skal du købe 1 ekstra plads.", "app.containers.Admin.Users.platformAdmin": "Platformsadministrator", "app.containers.Admin.Users.projectManager": "Projektleder", "app.containers.Admin.Users.reachedLimitMessage": "Du har nået grænsen for antal pladser i dit abonnement, 1 ekstra plads til {name} vil blive tilføjet.", "app.containers.Admin.Users.registeredUser": "<PERSON><PERSON><PERSON> bruger", "app.containers.Admin.Users.remove": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.removeModeratorFrom": "Brugeren modererer den mappe, som dette projekt hører til. Fjern i stedet opgaven fra \"{folderTitle}\".", "app.containers.Admin.Users.role": "<PERSON><PERSON>", "app.containers.Admin.Users.seeProfile": "Se profil", "app.containers.Admin.Users.selectPublications": "<PERSON><PERSON><PERSON><PERSON> projekter eller mapper", "app.containers.Admin.Users.selectPublicationsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> for at søge", "app.containers.Admin.Users.setAsAdmin": "Indstilles som admin", "app.containers.Admin.Users.setAsNormalUser": "Indstil som normal bruger", "app.containers.Admin.Users.setAsProjectModerator": "Indstillet som projektleder", "app.containers.Admin.Users.setUserAsProjectModerator": "Udpeg {name} som projektleder", "app.containers.Admin.Users.userBlockModal.allDone": "Alt er gjort", "app.containers.Admin.Users.userBlockModal.blockAction": "B<PERSON>kere bruger", "app.containers.Admin.Users.userBlockModal.blockInfo1": "<PERSON>ne brugers indhold vil ikke blive fjernet ved hjælp af denne handling. Glem ikke at moderere deres indhold, hvis det er nødvendigt.", "app.containers.Admin.Users.userBlockModal.blocked": "Blokeret", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "<PERSON>ne bruger har været blokeret siden {from}. Forbuddet varer indtil {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "<PERSON>r du sikker på, at du ønsker at deblokere {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} er blokeret indtil {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 dag} other {{numberOfDays} dage}}", "app.containers.Admin.Users.userBlockModal.header": "B<PERSON>kere bruger", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Begrundelse", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "<PERSON><PERSON> vil blive meddelt den blokerede bruger.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Den valgte bruger vil ikke kunne logge ind på platformen i {daysBlocked}. <PERSON><PERSON> du ø<PERSON> at ændre dette, kan du ophæve blokeringerne fra listen over blokerede brugere.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Fjern blokering af", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, jeg ø<PERSON><PERSON> at ophæve blokering af denne bruger", "app.containers.Admin.Users.userDeletionConfirmation": "<PERSON><PERSON><PERSON> denne bruger permanent?", "app.containers.Admin.Users.userDeletionFailed": "Der opstod en fejl under sletning af denne bruger, prøv venligst igen.", "app.containers.Admin.Users.userDeletionProposalVotes": "<PERSON><PERSON> vil også slette alle stemmer fra denne bruger på forslag, der stadig er åbne for afstemning.", "app.containers.Admin.Users.userExportFileName": "bruger_eksport", "app.containers.Admin.Users.userInsights": "Brugerindsigt", "app.containers.Admin.Users.youCantDeleteYourself": "Du kan ikke slette din egen konto via brugeradministrationssiden", "app.containers.Admin.Users.youCantUnadminYourself": "Du kan ikke opgive din rolle som administrator nu", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Borgerbarometer", "app.containers.Admin.communityMonitor.healthScore": "Sundhedsscore", "app.containers.Admin.communityMonitor.healthScoreDescription": "<PERSON><PERSON> score er gennemsnittet af alle spørgsmål på Sentiment-skalaen, som deltagerne har besvaret i den valgte periode.", "app.containers.Admin.communityMonitor.lastQuarter": "sids<PERSON> k<PERSON>tal", "app.containers.Admin.communityMonitor.liveMonitor": "Live-monitor", "app.containers.Admin.communityMonitor.noResults": "Ingen resultater for denne periode.", "app.containers.Admin.communityMonitor.noSurveyResponses": "<PERSON>gen svar på under<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.participants": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Rapporter", "app.containers.Admin.communityMonitor.settings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Borgerbarometer Survey accepterer svar.", "app.containers.Admin.communityMonitor.settings.accessRights2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "<PERSON><PERSON>r en bruger har tilmeldt sig et arrangement, afgivet en stemme eller er vendt tilbage til en projektside efter at have besvaret en undersøgelse.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Borgerbarometer Managers kan få adgang til og administrere alle Borgerbarometer-indstillinger og -data.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Administrator for Borgerbarometer", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Administratorer kan redigere Borgerbarometer-undersøgelsen og tilladelser, se svardata og oprette rapporter.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "Standardværdien for frekvensen er 100 %.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Popup-frekvens (0 til 100)", "app.containers.Admin.communityMonitor.settings.management2": "Forvaltning", "app.containers.Admin.communityMonitor.settings.popup": "Popup", "app.containers.Admin.communityMonitor.settings.popupDescription3": "En popup vises med jævne mellemrum for brugerne og opfordrer dem til at udfylde Borgerbarometer-undersøgelsen. Du kan justere frek<PERSON>sen, som bestemmer den procentdel af brugerne, der tilfældigt vil se popup'en, når betingelserne nedenfor er opfyldt.", "app.containers.Admin.communityMonitor.settings.popupSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.preview": "Eksempel", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Brugeren har ikke allerede udfyldt undersøgelsen inden for de seneste 3 måneder.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Brugeren har ikke allerede set popup'en inden for de seneste 3 måneder.", "app.containers.Admin.communityMonitor.settings.save": "Gem", "app.containers.Admin.communityMonitor.settings.saved": "Gemte", "app.containers.Admin.communityMonitor.settings.settings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.survey2": "Undersøgelse", "app.containers.Admin.communityMonitor.settings.surveySettings3": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Ved indlæ<PERSON>ning af hjemmesiden eller en brugerdefineret side.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonymisering af alle brugerdata", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Alle brugernes input i undersøgelsen vil blive anonymiseret inden de registreres", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Brugere skal stadig overholde deltagelseskravene under \"Adgangsrettigheder\". Brugerprofildata vil ikke være tilgængelige i eksporten af undersøgelsesdata.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Under hvilke betingelser kan pop op-vinduet vises for brugerne?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Hvem er administratorer?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Svar på undersøgelsen i alt", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI-opsummering", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Aktivér Borgerbarometeret", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Denne funktion er ikke inkluderet i din nuværende plan. Tal med S<PERSON><PERSON> eller jeres administrator for at låse den op.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Sundhedsscore", "app.containers.Admin.communityMonitor.upsell.learnMore": "<PERSON><PERSON><PERSON> mere ", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Score over tid", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "Borgerbarometeret hjæ<PERSON>per jer med at være på forkant ved at spore brugernes tillid, tilfred<PERSON> med tjenester og livet i lokalsamfundet - hele tiden.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Få klare resultater, stærke citater og en kvartalsrapport, som du kan dele med kolleger eller folkevalgte.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Letlæselige resultater, der udvikler sig over tid", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "<PERSON><PERSON><PERSON><PERSON> bruge<PERSON>r, opsummeret af AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Spørgsmål skræddersyet til jeres kommunes kontekst", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Brugere rekrutteret tilfældigt på platformen", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Kvartalsvise PDF-rapporter, klar til at dele", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Forstå, hvordan dit lokalsamfund har det, før <PERSON><PERSON> vokser", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "<PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "<PERSON><PERSON><PERSON><PERSON><PERSON> eller andet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobil", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Enhedstyper", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Enhedstype", "app.containers.Admin.earlyAccessLabel": "<PERSON><PERSON><PERSON><PERSON> adgang", "app.containers.Admin.earlyAccessLabelExplanation": "Dette er en nyudviklet funktion, der er tilgængelig i en begrænset periode.", "app.containers.Admin.emails.addCampaign": "Opret e-mail", "app.containers.Admin.emails.addCampaignTitle": "Opret en ny e-mail", "app.containers.Admin.emails.allParticipantsInProject": "Alle deltagere i projektet", "app.containers.Admin.emails.allUsers": "<PERSON><PERSON><PERSON><PERSON> brugere", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Automatiserede e-mails sendes automatisk og udløses af en brugers handlinger. Du kan slå nogle af dem fra for alle brugere af din platform. De andre automatiserede e-mails kan ikke slås fra, fordi de er nødvendige for, at din platform kan fungere korrekt.", "app.containers.Admin.emails.automatedEmails": "Automatiserede e-mails", "app.containers.Admin.emails.automatedEmailsDigest": "E-mailen vil kun blive sendt, hvis der er indhold", "app.containers.Admin.emails.automatedEmailsRecipients": "Brugere der vil modtage denne e-mail", "app.containers.Admin.emails.automatedEmailsTriggers": "Begivenhed der udløser denne e-mail", "app.containers.Admin.emails.changeRecipientsButton": "<PERSON><PERSON><PERSON> mod<PERSON>", "app.containers.Admin.emails.clickOnButtonForExamples": "Klik på knappen nedenfor for at se eksempler på denne e-mail på vores supportside.", "app.containers.Admin.emails.confirmSendHeader": "E-mail til alle brugere?", "app.containers.Admin.emails.deleteButtonLabel": "Slet", "app.containers.Admin.emails.draft": "Udkast", "app.containers.Admin.emails.editButtonLabel": "<PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON> kampagne", "app.containers.Admin.emails.editDisabledTooltip2": "Kommer snart: Denne e-mail kan ikke redigeres i øjeblikket.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Knaptekst", "app.containers.Admin.emails.editRegion_intro_multiloc": "Introduktion", "app.containers.Admin.emails.editRegion_subject_multiloc": "<PERSON><PERSON>", "app.containers.Admin.emails.editRegion_title_multiloc": "Titel", "app.containers.Admin.emails.emailCreated": "E-mail oprettet med succes i kladde", "app.containers.Admin.emails.emailUpdated": "E-mail opdateret med succes", "app.containers.Admin.emails.emptyCampaignsDescription": "Kom nemt i kontakt med dine deltagere ved at sende dem e-mails. Vælg, hvem du vil kontakte, og følg dit engagement.", "app.containers.Admin.emails.emptyCampaignsHeader": "Send din første e-mail", "app.containers.Admin.emails.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBody": "Besked", "app.containers.Admin.emails.fieldBodyError": "<PERSON>iv en e-mail-besked", "app.containers.Admin.emails.fieldGroupContent": "Indhold i e-mail", "app.containers.Admin.emails.fieldReplyTo": "<PERSON><PERSON> skal sendes til", "app.containers.Admin.emails.fieldReplyToEmailError": "Angiv en e-mail-adresse i det korrekte format, f.eks. <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Angiv en e-mail-adresse", "app.containers.Admin.emails.fieldReplyToTooltip": "<PERSON> kan ud<PERSON><PERSON>, hvor svarene på din e-mail skal sendes hen.", "app.containers.Admin.emails.fieldSender": "<PERSON>a", "app.containers.Admin.emails.fieldSenderError": "Angiv en afsender af e-mailen", "app.containers.Admin.emails.fieldSenderTooltip": "Du kan bestemme, hvem modtagerne skal se som afsender af e-mailen.", "app.containers.Admin.emails.fieldSubject": "E-mail-emne", "app.containers.Admin.emails.fieldSubjectError": "Angiv et emne for e-mailen", "app.containers.Admin.emails.fieldSubjectTooltip": "Dette vil blive vist i emnelinjen i e-mailen og i brugerens indbakkeoversigt. Gør det klart og engagerende.", "app.containers.Admin.emails.fieldTo": "Til", "app.containers.Admin.emails.fieldToTooltip": "Du kan udval<PERSON> de brugergrupper, der skal modtage din e-mail", "app.containers.Admin.emails.formSave": "Gem som udkast", "app.containers.Admin.emails.formSaveAsDraft": "Gem som udkast", "app.containers.Admin.emails.from": "Fra:", "app.containers.Admin.emails.groups": "Grupper", "app.containers.Admin.emails.helmetDescription": "Send manuelle e-mails til brugergrupper, og aktiver automatiserede kampagner", "app.containers.Admin.emails.nameVariablesInfo2": "Du kan tale direkte til borgerne ved hjælp af variablerne {firstName} {lastName}. F.eks. \"Kære {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "Der er blevet sendt en preview e-mail til din e-mailadresse", "app.containers.Admin.emails.previewTitle": "Eksempel", "app.containers.Admin.emails.regionMultilocError": "<PERSON><PERSON> venligst en værdi for alle sprog", "app.containers.Admin.emails.seeEmailHereText": "<PERSON><PERSON> snart en e-mail af denne type er sendt, vil du kunne se den her.", "app.containers.Admin.emails.send": "Send", "app.containers.Admin.emails.sendNowButton": "Send nu", "app.containers.Admin.emails.sendTestEmailButton": "Send mig en test-e-mail", "app.containers.Admin.emails.sendTestEmailTooltip2": "<PERSON><PERSON><PERSON> du klikker på dette link, bliver der sendt en testmail til din egen e-mailadresse. Det giver dig mulighed for at tjekke, hvordan e-mailen ser ud i \"virkeligheden\".", "app.containers.Admin.emails.senderRecipients": "Afsender og modtagere", "app.containers.Admin.emails.sending": "Afsendelse", "app.containers.Admin.emails.sent": "Send<PERSON>", "app.containers.Admin.emails.sentToUsers": "Dette er e-mails, der sendes til brugerne", "app.containers.Admin.emails.subject": "Emne:", "app.containers.Admin.emails.supportButtonLabel": "Se eksempler på vores supportside", "app.containers.Admin.emails.supportButtonLink2": "{tenant<PERSON><PERSON>, select,  {https://matomo.org/} other {https://support.govocal.com/da/articles/7042664-aendring-af-indstillingerne-for-de-automatiske-e-mailnotifikationer}}", "app.containers.Admin.emails.to": "Til:", "app.containers.Admin.emails.toAllUsers": "<PERSON><PERSON><PERSON> du at sende denne e-mail til alle registrerede brugere?", "app.containers.Admin.emails.viewExample": "Se", "app.containers.Admin.ideas.import": "Import", "app.containers.Admin.inspirationHub.AllProjects": "Alle projekter", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Borgerbarometer", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Dokument-annotering", "app.containers.Admin.inspirationHub.ExternalSurvey": "Ekstern undersøgelse", "app.containers.Admin.inspirationHub.Filters.Country": "Land", "app.containers.Admin.inspirationHub.Filters.Method": "Metode", "app.containers.Admin.inspirationHub.Filters.Search": "<PERSON><PERSON><PERSON> på", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.population": "Befolkning", "app.containers.Admin.inspirationHub.Highlighted": "Fremhævet", "app.containers.Admin.inspirationHub.Ideation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Information": "information", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Vælg et land for at se udvalgte projekter", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Land", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Ingen fastgjorte projekter fundet for dette land. Skift land for at se udvalgte projekter for andre lande", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Skift land for at se flere udvalgte projekter", "app.containers.Admin.inspirationHub.Poll": "Afstemning", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Ingen slutdato", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "<PERSON>æs mere...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Fase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "Hvis du ikke ønsker, at dit projekt skal indgå i inspirationscentret, skal du kontakte Søren <PERSON>t på <EMAIL>", "app.containers.Admin.inspirationHub.Proposals": "Borgerforslag", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Sorter efter", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Deltagere (laveste først)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Deltagere (højeste først)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Startdato (ældste først)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Startdato (nyeste først)", "app.containers.Admin.inspirationHub.Survey": "Undersøgelse", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "<PERSON><PERSON><PERSON> liste over de bedste projekter i hele verden.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Tal med og lær af andre praktikere.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtrer efter metode, bystørrelse og land.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Aktiver Inspiration Hub", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Denne funktion er ikke inkluderet i din nuværende plan. Tal med S<PERSON><PERSON> eller jeres administrator for at låse den op.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "{tenant<PERSON><PERSON>, select, {https://matomo.org/} other {https://support.govocal.com/da/articles/11093736-brug-af-inspirationsknudepunktet}}", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "<PERSON><PERSON><PERSON> mere ", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Inspiration <PERSON><PERSON> forbinder dig med et kurateret feed af inspirerende borgerinddragelsesprojekter på Go Vocal-platforme i hele verden. <PERSON><PERSON><PERSON>, h<PERSON>dan andre byer gennemfører vellykkede projekter, og tal med andre praktikere.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Bliv en del af et netværk af banebrydende demokratipraktikere", "app.containers.Admin.inspirationHub.Volunteering": "Frivilligt arbejde", "app.containers.Admin.inspirationHub.Voting": "Afstemning", "app.containers.Admin.inspirationHub.commonGround": "Fælles grundlag", "app.containers.Admin.inspirationHub.filters": "filtre", "app.containers.Admin.inspirationHub.resetFilters": "Nulstil filtre", "app.containers.Admin.inspirationHub.seemsLike": "Det ser ud til, at der ikke er flere projekter. Prøv at ændre {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "<PERSON><PERSON> kampagnefelter", "app.containers.Admin.messaging.automated.variablesToolTip": "Du kan bruge følgende variabler i din besked:", "app.containers.Admin.messaging.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "<PERSON>ne widget viser hver bruger projekter <b>baseret på deres 'følge' præferencer</b>. <PERSON><PERSON> inkluderer projekter, som de følger samt projekter, hvor de følger input og projekter, der er relateret til emner eller områder, som de er interesserede i.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Denne widget vil kun blive vist til brugeren, hvis der er projekter, hvor de kan deltage. Hvis du ser denne besked, bet<PERSON><PERSON> det, at du (administratoren) ikke kan deltage i nogen projekter i øjeblikket. Denne besked vil ikke være synlig på den rigtige hjemmeside.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Åben for deltagelse", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "<PERSON><PERSON> widget viser projekter, hvor brugeren i øjeblikket kan foretage <b>en handling for at deltage.</b>", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Titel", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "For dig", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "Denne widget vil kun blive vist til brugeren, hvis der er projekter, der er relevante for dem baseret på deres følgepræferencer. Hvis du ser denne besked, bet<PERSON><PERSON> det, at du (administratoren) ikke følger noget i øjeblikket. Denne besked vil ikke være synlig på den rigtige hjemmeside.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "<PERSON>l<PERSON><PERSON> elementer", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Arkiveret", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filtrere efter", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Afsluttet", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Færdig og arkiveret", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "Ingen data tilgængelige", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "<PERSON>ne widget viser <b>proje<PERSON><PERSON>, der er afsluttede og/eller arkiverede</b>. \"Afsluttet\" omfatter også projekter, der er i den sidste fase, og hvor den sidste fase er en rapport.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "<PERSON><PERSON><PERSON><PERSON> projekter", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "<PERSON>g<PERSON>, vi gjorde...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Giv et navn til alle sprog", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Projektet må ikke være tomt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Navn", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Resulterende URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Gem", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Tilføj projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "Navigationslinjen viser kun projekter, som brugerne har adgang til.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "<PERSON>ne widget vil kun være synlig på hjemmesiden, når Community Monitor accepterer svar.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Borgerbarometer", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Beskrivelse", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "Knap", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Vigtigt:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Eksempel på et spørgsmål til en stemningsundersøgelse", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "Ingen slutdato", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "<PERSON><PERSON> p<PERSON> escape for at springe karrusellen over", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "<PERSON>jekt<PERSON> og mapper (ældre)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Projektets titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} arbejder i øjeblikket med", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Knaptekst", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Deltag nu!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Beskrivelse", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "mappe", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "<PERSON>æ<PERSON>g et projekt eller en mappe", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "<PERSON>æ<PERSON>g projekt eller mappe", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Vis avatarer", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Spotlight", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Starter om {days} dage", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Starter om {weeks} uger", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} dage siden", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} uger siden", "app.containers.Admin.project.Campaigns.campaignFrom": "Fra:", "app.containers.Admin.project.Campaigns.campaignTo": "Til:", "app.containers.Admin.project.Campaigns.customEmails": "Brugerdefinerede e-mails", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Send tilpassede e-mails og tjek statistik.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON> <PERSON>, men det ser ud til, at du ikke har adgang til e-mailsektionen", "app.containers.Admin.project.emails.addCampaign": "Opret e-mail", "app.containers.Admin.project.emails.addCampaignTitle": "<PERSON><PERSON>", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Alle {participants} og følgere fra projektet", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Dette omfatter registrerede brugere, der har udført en handling i projektet. Ikke registrerede eller anonymiserede brugere er ikke inkluderet.", "app.containers.Admin.project.emails.dateSent": "Dato for afsendelse", "app.containers.Admin.project.emails.deleteButtonLabel": "Slet", "app.containers.Admin.project.emails.draft": "Udkast", "app.containers.Admin.project.emails.editButtonLabel": "<PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "<PERSON><PERSON> kampagne", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Kom nemt i kontakt med dine deltagere ved at sende dem e-mails. Vælg, hvem du vil kontakte, og følg dit engagement.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Send din første e-mail", "app.containers.Admin.project.emails.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.fieldBody": "E-mail Besked", "app.containers.Admin.project.emails.fieldBodyError": "<PERSON>iv en e-mail-besked", "app.containers.Admin.project.emails.fieldReplyTo": "<PERSON><PERSON> skal sendes til", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Angiv en e-mail-adresse i det korrekte format, f.eks. <EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Angiv en e-mail-adresse", "app.containers.Admin.project.emails.fieldReplyToTooltip": "<PERSON><PERSON><PERSON><PERSON>, hvilken e-mailadresse der skal modtage direkte svar fra brugere på din e-mail.", "app.containers.Admin.project.emails.fieldSender": "<PERSON>a", "app.containers.Admin.project.emails.fieldSenderError": "Angiv en afsender af e-mailen", "app.containers.Admin.project.emails.fieldSenderTooltip": "<PERSON><PERSON><PERSON><PERSON>, hvem brugerne skal se som afsender af e-mailen.", "app.containers.Admin.project.emails.fieldSubject": "E-mail-emne", "app.containers.Admin.project.emails.fieldSubjectError": "Angiv et emne for e-mailen", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Dette vil blive vist i emnelinjen i e-mailen og i brugerens indbakkeoversigt. Gør det klart og engagerende.", "app.containers.Admin.project.emails.fieldTo": "Til", "app.containers.Admin.project.emails.formSave": "Gem som udkast", "app.containers.Admin.project.emails.from": "Fra:", "app.containers.Admin.project.emails.helmetDescription": "Send manuelle e-mails ud til projektdeltagerne", "app.containers.Admin.project.emails.infoboxAdminText": "Fra fanen Project Messaging kan du kun sende e-mails til alle projektdeltagere.  Hvis du vil sende e-mails til andre deltagere eller undergrupper af brugere, skal du gå til fanen {link} .", "app.containers.Admin.project.emails.infoboxLinkText": "Meddelelser på platformen", "app.containers.Admin.project.emails.infoboxModeratorText": "Fra fanen Project Messaging kan du kun sende e-mails til alle projektdeltagere. Administratorer kan sende e-mails til andre deltagere eller undergrupper af brugere via fanen Platform Messaging.", "app.containers.Admin.project.emails.message": "Besked", "app.containers.Admin.project.emails.nameVariablesInfo2": "Du kan tale direkte til borgerne ved hjælp af variablerne {firstName} {lastName}. F.eks. \"Kære {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "deltagere", "app.containers.Admin.project.emails.previewSentConfirmation": "Der er blevet sendt en preview e-mail til din e-mailadresse", "app.containers.Admin.project.emails.previewTitle": "Eksempel", "app.containers.Admin.project.emails.projectParticipants": "Deltagere i projektet", "app.containers.Admin.project.emails.recipients": "Modtagere", "app.containers.Admin.project.emails.send": "Send", "app.containers.Admin.project.emails.sendTestEmailButton": "Send en forhåndsvisning", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Send denne kladde-e-mail til den e-mailadresse, du er logget ind med, for at se, hvordan den ser ud i \"virkeligheden\".", "app.containers.Admin.project.emails.senderRecipients": "Afsender og modtagere", "app.containers.Admin.project.emails.sending": "Afsendelse", "app.containers.Admin.project.emails.sent": "Send<PERSON>", "app.containers.Admin.project.emails.sentToUsers": "Dette er e-mails, der sendes til brugerne", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Emne:", "app.containers.Admin.project.emails.to": "Til:", "app.containers.Admin.project.messaging.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Dette billede er en del af mappekortet; det kort, der opsummerer mappen, og som f.eks. vises på hjemmesiden. Du kan få flere oplysninger om anbefalede billedopløsninger på {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Dette billede vises øverst på mappesiden. Du kan få flere oplysninger om anbefalede billedopløsninger ved at klikke på {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "besøg vores supportcenter", "app.containers.Admin.projects.all.askPersonalData3": "<PERSON><PERSON><PERSON><PERSON><PERSON> felter til navn og e-mail", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "Denne funktion er ikke inkluderet i jeres nuværende plan. Tal med S<PERSON><PERSON> eller jeres administrator for at låse den op.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "<PERSON><PERSON><PERSON> mere ", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "{tenant<PERSON><PERSON>, select, {https://matomo.org/} other {https://support.govocal.com/da/articles/7034763-oprettelse-og-tilpasning-af-dine-projekter}}", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Få et visuelt overblik over dine projekttidslinjer i vores kalendervisning. Identificer hurtigt, hvilke projekter og faser der snart starter eller slutter og kræver handling.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "Forstå, hvad der sker og hvornår", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Alle spørgsmål vises på PDF'en. Følgende understøttes dog ikke i øjeblikket til import via FormSync: Billeder, tags og filupload.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Alle spørgsmål vises på PDF'en. Følgende understøttes dog ikke i øjeblikket til import via FormSync: Kort-spørgsmål (drop pin, draw route og draw area), rangordningsspørgsmål, matrix-spørgsmål og filupload-spørgsmål.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Slut på formularen", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Start af formularen", "app.containers.Admin.projects.all.components.archived": "Arkiveret", "app.containers.Admin.projects.all.components.draft": "Udkast", "app.containers.Admin.projects.all.components.manageButtonLabel": "<PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectButton": "<PERSON><PERSON><PERSON> projekt", "app.containers.Admin.projects.all.copyProjectError": "Der var en fejl ved kopiering af dette projekt, prøv igen senere.", "app.containers.Admin.projects.all.customiseEnd": "Tilpas afslutningen af formularen.", "app.containers.Admin.projects.all.customiseStart": "Tilpas starten af formularen.", "app.containers.Admin.projects.all.deleteFolderButton1": "Slet mappe", "app.containers.Admin.projects.all.deleteFolderConfirm": "Er du sikker på, at du ønsker at slette denne mappe? Alle projekter i mappen vil også blive slettet. Denne handling kan ikke fortrydes.", "app.containers.Admin.projects.all.deleteFolderError": "Der var et problem med at fjerne denne mappe. Prøv venligst igen.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Slet projekt", "app.containers.Admin.projects.all.deleteProjectConfirmation": "<PERSON>r du sikker på, at du ønsker at slette dette projekt? Dette kan ikke fortrydes.", "app.containers.Admin.projects.all.deleteProjectError": "Der var en fejl ved sletning af dette projekt, prøv venligst igen senere.", "app.containers.Admin.projects.all.exportAsPDF1": "Download PDF-formular", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Du kan kombinere online- og offline-svar. For at uploade offline-svar skal du gå til fanen \"Input manager\" i dette projekt og klikke på \"Import\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Du kan kombinere online- og offlinebesvarelser. For at uploade offline-besvarelser skal du gå til fanen \"Survey\" i dette projekt og klikke på \"Import\".", "app.containers.Admin.projects.all.logicNotInPDF": "Undersøgelsens logik vil ikke blive afspejlet i den downloadede PDF. Papirrespondenter vil se alle undersøgelsens spørgsmål.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "Søg i mapper", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Alle mapper er blevet indlæst", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Mappe", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Projektledere", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# projekt} other {# projekter}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Projektets startdato", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "<PERSON><PERSON><PERSON>gh<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Skjult", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Offentlig", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filtrer efter den aktuelle inddragelsesmetode", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "information", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Metode til inddragelse", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Dokument-annotering", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Fælles grundlag", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Undersøgelse", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Afstemning", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Forslag", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Frivilligt arbejde", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Afstemning", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Indsamling af data", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informerer", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Ikke <PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Status for deltagelse", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Fortid", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "Søg i projekter", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alfabetisk (a-å)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alfabetisk (a-å)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Projektleder", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Fase starter eller slutter snart", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc1": "<PERSON><PERSON><PERSON>t oprettet (gammel-nye)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc1": "<PERSON>yl<PERSON>t oprettet (ny-gammel)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Set for nylig", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Administratorer", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Grupper", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "<PERSON><PERSON><PERSON>gh<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Offentlig", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "T<PERSON><PERSON><PERSON><PERSON> filter", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "<PERSON>kke flere filtre at tilføje", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Administratorer", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Alle projekter er blevet indlæst", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Nogen", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Arkiveret", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Igangværende fase", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d tilbage", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d til at starte", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Synlighed:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Udkast", "app.containers.Admin.projects.all.new.Projects.Table.end": "Slut", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Afsluttet", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "Slutter i dag", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Grupper", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Skjult", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Indlæser mere…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Projektleder", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo tilbage", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo til start", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Næste fase:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "<PERSON><PERSON><PERSON> til<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Fase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projekt", "app.containers.Admin.projects.all.new.Projects.Table.public": "Offentlig", "app.containers.Admin.projects.all.new.Projects.Table.published": "Publiceret idé", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "<PERSON><PERSON> ned for at læse mere", "app.containers.Admin.projects.all.new.Projects.Table.start": "Start", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "Denne kolonne bruger cachelagrede deltagerdata. For at se de seneste tal skal du tjekke fanen \"Deltagere\" i projektet.", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "<PERSON><PERSON><PERSON>gh<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Synlighed:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} grupper", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} ledere", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y tilbage", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y til start", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Nuværende fase: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} dage tilbage", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Folder: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "Ingen igangværende fase", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Ingen slutdato", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "Ingen faser", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Fase {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Faser:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projekt", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Startdato: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "<PERSON><PERSON><PERSON> projekter", "app.containers.Admin.projects.all.new.calendar": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.folders": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.projects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Kunne ikke indlæse tidslinjen.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Projektet har ingen slutdato", "app.containers.Admin.projects.all.new.timeline.project": "Projekt", "app.containers.Admin.projects.all.notes": "Noter", "app.containers.Admin.projects.all.personalDataExplanation5": "<PERSON><PERSON> mulighed til<PERSON><PERSON><PERSON> felterne fornavn, efternavn og e-mail til den eksporterede PDF. Når vi uploader papirformularen, bruger vi disse data til automatisk at generere en konto til offline-respondenten.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "Sammenfatning af AI", "app.containers.Admin.projects.project.analysis.Comments.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Sammenfatning af kommentarer er tilgængelig, når der er 5 eller flere kommentarer.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Sammen<PERSON>t kommentarer", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON>} =1 {1 ny kommentar} other {# nye kommentarer}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI-opsummering", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Dette er AI-genereret indhold. Det er måske ikke 100% nøjagtigt. Gennemgå og krydsreferer med de faktiske input for nøjagtighed. Vær opmærksom på, at nøjagtigheden sandsynligvis forbedres, hvis antallet af valgte input reduceres.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "E-mail meddelelser sendes kun til deltagere", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Skjult", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Ikke indekseret af søgemaskiner", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Ikke synlig på hjemmesiden eller i widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Kun tilgængelig via direkte URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Offentlig", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "<PERSON><PERSON><PERSON><PERSON>, hvor tilgæ<PERSON>gt dette projekt er.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Dette projekt er synligt for alle, der har adgang og vil blive vist på hjemmesiden og i widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Dette projekt vil være skjult for den brede offentlighed og vil kun være synligt for dem, der har linket.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Hvem kan finde dette projekt?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Åben AI-analyse", "app.containers.Admin.projects.project.ideas.analysisText2": "Udforsk AI-drevne resuméer og se individuelle indsendelser.", "app.containers.Admin.projects.project.ideas.importInputs": "Import", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "<PERSON><PERSON><PERSON> du har oprettet en rapport, kan du vælge at dele den med offentligheden, n<PERSON>r fasen starter.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Opret en mere kompleks side til informationsdeling", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Opret en rapport til:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Opret en rapport", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "<PERSON>ret en rapport for en tidligere fase, eller start forfra.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "Denne rapport er ikke offentlig. For at gøre den offentlig skal du aktivere \"Synlig\"-knappen.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "<PERSON>ne fase er startet, men rapporten er ikke offentlig endnu. For at gøre den offentlig skal du aktivere \"Synlig\"-knappen.", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Start med en faseskabelon", "app.containers.Admin.projects.project.information.ReportTab.report": "Rapport", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Del resultaterne af en tidligere survey eller idéfase", "app.containers.Admin.projects.project.information.ReportTab.visible": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Denne rapport vil være offentlig, så snart fasen starter. <PERSON>vis du ikke vil offentliggøre den, skal du deaktivere \"Synlig\"-knap<PERSON>.", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "Denne rapport er i øjeblikket offentlig. Hvis du ikke vil offentliggøre den, skal du slå \"Synlig\" fra.", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Er du sikker på, at du vil slette denne rapport? Handligen kan ikke fortrydes.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Tilføj til fase", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "<PERSON> skal give dit samtykke, før du kan fortsætte.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "<PERSON><PERSON> kan downloades her.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Den uploadede formular blev oprettet med afsnittet \"Personlige data\".", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Formsprog", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Jeg giver hermed mit samtykke til at behandle denne fil ved hjælp af Google Cloud Form Parser.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Importer Excel-fil", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Upload venligst en fil for at fortsætte", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "<PERSON><PERSON><PERSON><PERSON><PERSON> kan downloades her.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Upload", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Upload en udfyldt <b>Excel-fil</b> (.xlsx). Den skal bruge den skabelon, der er angivet til dette projekt. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Upload en <b>PDF-fil med scannede formularer</b>. Den skal bruge en formular udskrevet fra denne fase. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Brug denne e-mail til den nye bruger", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Indtast en gyldig e-mail for at oprette en ny konto", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Der oprettes en ny konto til forfatteren med disse oplysninger. Dette input vil blive tilføjet til den.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "Fornavn", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Efternavn", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Indtast en e-mailadresse og/eller et fornavn og efternavn for at tildele dette input til en forfatter. Eller fjern markeringen i samtykkefeltet.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Der er allerede en konto tilknyttet denne e-mail. <PERSON><PERSON> input vil blive tilføjet til den.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Brugersamtykke (opret brugerkonto)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Godkend", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Godkend alle input", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Forfatter:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "E-mail:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Der opstod fejl under importen, og nogle input er ikke blevet importeret. Ret venligst fejlene, og genimporter eventuelle manglende input.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Ugyldige formulardata. Tjek formularen ovenfor for fejl.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Importer Excel-fil (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Import", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Importer scannede formularer (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Importer scannede formularer", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Importerede input", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importerer. <PERSON>ne proces kan tage et par minutter.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "Dette input blev importeret anonymt.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} input er blevet importeret og kræver godkendelse.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} input kunne ikke godken<PERSON>. Tjek venligst hvert input for valideringsproblemer og bekræft individuelt.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Lokalitet:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Der er ikke noget at gennemse endnu. Klik på \"{importFile}\" for at importere en PDF-fil med scannede inputformularer eller en Excel-fil med input.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Der er ikke noget at gennemse endnu. Klik på \"{importFile}\" for at importere en Excel-fil med input.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Input", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Side", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Kan ikke vise den importerede fil. Visning af importerede filer er kun tilgængelig for PDF-import.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Fase:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Den valgte fase kan ikke indeholde inputs. Vælg venligst en anden.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Dette projekt indeholder ikke nogen faser, der kan indeholde ideer.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Væ<PERSON>g venligst hvilken fase du vil tilføje disse input til.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Import af input", "app.containers.Admin.projects.project.participation.comments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.inputs": "Input", "app.containers.Admin.projects.project.participation.participantsTimeline": "Deltagernes tidslinje", "app.containers.Admin.projects.project.participation.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "<PERSON><PERSON><PERSON><PERSON> periode", "app.containers.Admin.projects.project.participation.usersByAge": "Brugere efter alder", "app.containers.Admin.projects.project.participation.usersByGender": "Brugere efter køn", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Påkrævet", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Tilfø<PERSON> et spørgsmål", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON> for at tilføje eller redigere brugerfelter på faseniveau er ikke inkluderet i din nuværende licens. Kontakt Sø<PERSON> for at få mere at vide om det.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} muligheder", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Disse spørgsmål vil blive tilføjet som den sidste side i undersøgelsesformularen, fordi \"Vis felter i undersøgelsen?\" er valgt i faseindstillingerne.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Der vil ikke blive stillet ekstra spørgsmål.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Valg<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Valgfrit - altid aktiveret, fordi der henvises til gruppen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "<PERSON><PERSON><PERSON> feltet", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Påkrævet - altid aktiveret, fordi der henvises til gruppen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Godkend med {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Udfyld de ekstra spørgsmål nedenfor", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Bekræft din e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Data returneret fra verifikationsmetoden:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Indtast fornavn, efternavn, e-mail og adgangskode", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Indtast din e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "Hvor ofte skal brugere verificeres?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Identitetsbekræftelse med {verificationMethod} (baseret på brugergruppe)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "<PERSON> kræves ingen handlinger for at deltage", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Brug smarte grupper til at begrænse deltagelse baseret på de verificerede data, der er anført ovenfor.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Brugere skal være blevet verificeret inden for de sidste 30 minutter.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Brugere skal være blevet verificeret inden for de seneste {days} dage.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "I løbet af de sidste 30 dage", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "I de sidste 30 minutter", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "I løbet af de sidste 7 dage", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "En gang er nok", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> felter:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} Verifikation", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Oprettelse af konto", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Deltagerne skal oprette en fuld konto med deres navn, bekræftede e-mail og adgangskode.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Deltagerne skal oprette en fuld konto med deres navn, e-mail og adgangskode.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Autentificering", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Bekræftelse via e-mail", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Deltagerne skal bekræfte deres e-mail med en engangskode.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Avanceret registrering af spam", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "<PERSON>ne funktion hjælper med at forhindre dobbeltbesvarelser fra udloggede brugere ved at analysere IP-adresser og enhedsdata. Selvom det ikke er så præcist som at kræve login, kan det hjælpe med at reducere antallet af dobbelte svar.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Bemærk: <PERSON><PERSON> delte netværk (f.eks. kontorer eller offentlig Wi-Fi) er der en lille chance for, at forskellige brugere kan blive markeret som dubletter.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Aktivér avanceret registrering af spam", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Ekstra spørgsmål til deltagerne", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "Nogen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Alle kan deltage uden at tilmelde sig eller logge ind.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Nulstil ekstra spørgsmål og grupper", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Begræns deltagelse til brugergruppe(r)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO-verifikation", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Deltagere skal bekræfte deres identitet med {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Åben AI-analyse", "app.containers.Admin.projects.project.survey.allFiles": "Alle filer", "app.containers.Admin.projects.project.survey.allResponses": "<PERSON>e svar", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Nøjagtighed: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Der opstod en fejl ved genereringen af AI-oversigten. Prøv at genskabe det nedenfor.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Åben AI-analyse", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "<PERSON><PERSON><PERSON><PERSON> opsummer<PERSON> for dette spørgsmål", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "valgte input", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "<PERSON><PERSON><PERSON> analyser", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} nye svar", "app.containers.Admin.projects.project.survey.analysis.regenerate": "Regenerere", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Vis AI-indsigt", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Du kan opsummere maksimalt 30 input ad gangen med din nuværende plan. Tal med jeres GovSuccess Manager (<PERSON><PERSON><PERSON>) eller interne administrator for at låse mere op.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "<PERSON><PERSON><PERSON><PERSON> relaterede spørgsmål til analyse", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "<PERSON>ns<PERSON> du at inkludere andre relaterede spørgsmål i din analyse af {question}?", "app.containers.Admin.projects.project.survey.cancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalButton": "Fortsæt", "app.containers.Admin.projects.project.survey.consentModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "<PERSON><PERSON> accepterer at bruge OpenAI som databehandler til dette projekt.", "app.containers.Admin.projects.project.survey.consentModalText1": "Ved at fortsætte accepterer du at bruge OpenAI som databehandler for dette projekt.", "app.containers.Admin.projects.project.survey.consentModalText2": "OpenAI API'erne driver de automatiserede tekstresuméer og dele af den automatiserede tagging-oplevelse.", "app.containers.Admin.projects.project.survey.consentModalText3": "Vi sender kun, hvad brugerne har skrevet i deres unders<PERSON>, ideer og kommentarer til OpenAI API'erne, aldrig nogen oplysninger fra deres profil.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI vil ikke bruge disse data til yderligere træning af sine modeller. Flere oplysninger om, hvordan OpenAI håndterer databeskyttelse, kan findes på {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "<PERSON><PERSON><PERSON>, h<PERSON>dan du opretter en workshop", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Du kan ikke gå ind i analysen, før du har redigeret formularen", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Slet", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "<PERSON>r du sikker på, at du vil slette denne analyse? <PERSON><PERSON> handling kan ikke fortrydes.", "app.containers.Admin.projects.project.survey.explore": "Udforsk", "app.containers.Admin.projects.project.survey.followUpResponses": "<PERSON>ølg op på svarene", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> Gennemsnit", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Eksporter som GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Eksporter svarene på dette spørgsmål som en GeoJSON-fil. For hver GeoJSON-funktion vil alle de relaterede respondenters undersøgelsesbesvarelser blive opført i funktionens \"egenskaber\"-objekt.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondenter} one {{respondentCount} respondent} other {{respondentCount} respondenter}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "<PERSON> <PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} valg} one {{numberChoices} valg} other {{numberChoices} valg}}", "app.containers.Admin.projects.project.survey.heatMap": "Varmekort", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "{tenantN<PERSON>, select,  {https://matomo.org/} other {https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13}}", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "<PERSON><PERSON>r mere om varmekort genereret ved hjælp af Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Varmekortet er genereret ved hjælp af Esri Smart Mapping. Varmekort er nyttige, når der er en stor mængde datapunkter. Hvis der er færre punkter, kan det være bedre kun at se direkte på placeringspunkterne. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Visning af varmekort", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Skjult af logik", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "<PERSON><PERSON>r en bruger væ<PERSON><PERSON>, springer logikken alle sider over indtil side {pageNumber} ({numQuestionsSkipped} sp<PERSON><PERSON><PERSON><PERSON><PERSON> sprunget over). <PERSON><PERSON> for at skjule eller vise de oversprungne sider og spørgsmål.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "<PERSON><PERSON>r en bruger væ<PERSON><PERSON>, springer logikken til slutningen af undersøgelsen ({numQuestionsSkipped} spø<PERSON>sm<PERSON><PERSON> sprunget over). <PERSON><PERSON> for at skjule eller vise de oversprungne sider og spørgsmål.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logikken på denne side springer alle sider over indtil side {pageNumber} ({numQuestionsSkipped} sp<PERSON><PERSON><PERSON><PERSON><PERSON> sprunget over). <PERSON><PERSON> for at skjule eller vise de oversprungne sider og spørgsmål.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Logikken på denne side springer til slutningen af undersøgelsen ({numQuestionsSkipped} sp<PERSON><PERSON><PERSON><PERSON><PERSON> sprunget over). <PERSON><PERSON> for at skjule eller vise de oversprungne sider og spørgsmål.", "app.containers.Admin.projects.project.survey.newAnalysis": "Ny analyse", "app.containers.Admin.projects.project.survey.nextInsight": "<PERSON><PERSON><PERSON> in<PERSON>", "app.containers.Admin.projects.project.survey.openAnalysis": "Åben AI-analyse", "app.containers.Admin.projects.project.survey.otherResponses": "<PERSON>", "app.containers.Admin.projects.project.survey.page": "Side", "app.containers.Admin.projects.project.survey.previousInsight": "Tid<PERSON>ger<PERSON> inds<PERSON>t", "app.containers.Admin.projects.project.survey.responses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "<PERSON><PERSON><PERSON> af svar for denne side er lavere end det samlede antal svar på undersøgelsen, fordi nogle respondenter ikke har set denne side på grund af logikken i undersøgelsen.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Antallet af svar på dette spørgsmål er lavere end det samlede antal svar på under<PERSON><PERSON><PERSON><PERSON>, fordi nogle respondenter ikke vil have set dette spørgsmål på grund af logik i unders<PERSON><PERSON><PERSON>.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Sentiment-skala", "app.containers.Admin.projects.project.survey.upsell.bullet1": "<PERSON><PERSON><PERSON>t alle dine svar med det samme.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Tal med dine data på et naturligt sprog.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Få referencer til individuelle svar fra AI-genererede resuméer.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "<PERSON><PERSON><PERSON> vores {link} for en komplet oversigt.", "app.containers.Admin.projects.project.survey.upsell.button": "<PERSON><PERSON><PERSON> op for AI-analyse", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "supportartikel", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "{tenant<PERSON><PERSON>, select, Odense {https://matomo.org/} other {https://support.govocal.com/en/articles/8316692-ai-analysis}}", "app.containers.Admin.projects.project.survey.upsell.title": "Analysér data hurtigere med AI", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "<PERSON>ne funktion er ikke inkluderet i din nuværende plan. Tal med jeres administrator for at låse den op.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Se", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Udforsk AI-drevne resuméer og se individuelle indsendelser.", "app.containers.Admin.projects.project.traffic.selectPeriod": "<PERSON><PERSON><PERSON><PERSON> periode", "app.containers.Admin.projects.project.traffic.trafficSources": "Trafik<PERSON>lder", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Vi har æ<PERSON><PERSON> den må<PERSON>, vi indsamler og viser besøgsdata på. <PERSON> betyder, at besøgsdataene er mere nøjagtige, og at flere typer data er tilgængelige, samtidig med at de stadig overholder GDPR. Vi begyndte først at indsamle disse nye data i november 2024, så før det, er der ingen data tilgængelige.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "T<PERSON>lin<PERSON> for besøgende", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Fase-rapport", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> lidt tekst om fasen", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Dette er noget tekst. Du kan redigere og formatere den ved at bruge editoren i panelet til højre.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Projektets resultater", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Resumé af rapporten", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Tilføj projektets mål, de anvendte deltagelsesmetoder og resultatet", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Besøgende", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Denne rapport indeholder æ<PERSON>, der ikke er gemt. Gem venligst før udskrivning.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Titlen er allerede taget", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Sammenlignet med tidligere {days} dage", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Skjul statistik", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Deltagelsesprocent", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Vis sammenligning med sidste periode", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Du skal først vælge et datointerval.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Input", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Deltagelse", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "<PERSON><PERSON> kommentarer", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Vis input", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Vis stemmer", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Afstemninger", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Periode for registreringsdato", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Registreringsfelt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Ukendt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Brugere: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Stræk", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Aktiv", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Arkiveret", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Afsluttet", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Status for offentliggørelse", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Offentliggjort", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "<PERSON>ene for denne widget mangler. Rekonfigurer eller slet den for at kunne gemme rapporten.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Borgerbarometer Health Score", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "Ingen passende faser fundet i dette projekt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Ingen fase valgt. Vælg venligst en fase først.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Intet projekt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Intet projekt valgt. Vælg venligst et projekt først.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Du kan ikke kopiere denne rapport, fordi den indeholder data, som du ikke har adgang til.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Du kan ikke redigere denne rapport, fordi den indeholder data, som du ikke har adgang til.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "<PERSON>r du sikker på, at du vil slette \"{reportName}\"? <PERSON><PERSON> handling kan ikke fortrydes.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Er du sikker på, at du vil slette denne rapport? Handligen kan ikke fortrydes.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Slet", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Lav en kopi", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "<PERSON>ndret {days, plural, no {# dage} one {# dag} other {# dage}} siden", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Der opstod en fejl, da du forsøgte at oprette denne rapport. Prøv venligst igen senere.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Start med en tom side", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Start med en Borgerbarometer-skabelon", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Rapporttitel", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Opret en rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Tilpas din rapport, og del den med interne interessenter eller lokalsamfundet som en PDF-fil.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Opret en rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "<PERSON>ret din første rapport", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Intet projekt valgt", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Start med en platformsskabelon", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Udskriv til PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Start med en projektskabelon", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "<PERSON><PERSON><PERSON> {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Rapportskabelon", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Der findes allerede en rapport med denne titel. Vælg venligst en anden titel.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Del som PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "<PERSON><PERSON> du vil dele rapporten med alle, kan du udskrive den som en PDF-fil.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Del som weblink", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Dette weblink er kun tilgængeligt for administratorbrugere.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Del", "app.containers.Admin.reporting.contactToAccess": "Oprettelse af en brugerdefineret rapport er en del af premium-licensen. Kontakt din GovSuccess Manager for at få mere at vide om det.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Alle rapporter", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Rapporter fra Borgerbarometer", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Disse rapporter er <PERSON>ret til Borgerbarometer. Rapporterne genereres automatisk hvert kvartal.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Opret en rapport", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Tilpas din rapport, og del den med interne interessenter eller et fællesskab med et weblink.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Dine rapporter vil blive vist her.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Søg i rapporter", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Rapporter om fremskridt", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Dette er rapporter, der er oprettet af din Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Dine rapporter", "app.containers.Admin.reporting.deprecated": "DEPRECERET", "app.containers.Admin.reporting.helmetDescription": "Admin-rapporteringsside", "app.containers.Admin.reporting.helmetTitle": "Indsigt", "app.containers.Admin.reporting.printPrepare": "Forberedelse til udskrivning...", "app.containers.Admin.reporting.reportBuilder": "Rapportbygger", "app.containers.Admin.reporting.reportHeader": "Rapports overskrift", "app.containers.Admin.reporting.warningBanner3": "Grafer og tal i denne rapport opdateres kun automatisk på denne side. Gem rapporten for at opdatere dem på andre sider.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Fælles grundlag", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideation", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "information", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "<PERSON><PERSON><PERSON><PERSON> metoder", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Undersøgelse", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Afstemning", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Forrige {days} dage: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Borgerforslag", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "Ekstern undersøgelse", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Frivilligt arbejde", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Afstemning", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Diagram", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "Se", "app.containers.Admin.surveyFormTab.downloads": "Downloads", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Kopier en anden survey", "app.containers.Admin.surveyFormTab.editSurveyForm": "Rediger surveyformular", "app.containers.Admin.surveyFormTab.inputFormDescription": "Angiv hvilke oplysninger der skal angives, tilfø<PERSON> korte beskrivelser eller instruktioner for at vejlede deltagerne og angiv, om et felt er valgfrit eller obligatorisk.", "app.containers.Admin.surveyFormTab.surveyForm": "S<PERSON><PERSON>rgeskema", "app.containers.Admin.tools.apiTokens.createTokenButton": "Opret nyt token", "app.containers.Admin.tools.apiTokens.createTokenCancel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Dit token er blevet oprettet. Kopier venligst {secret} nedenfor og opbevar den sikkert.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Opret et nyt token til brug med vores offentlige API.", "app.containers.Admin.tools.apiTokens.createTokenError": "Giv et navn til dit token", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Opret token", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>Det er vigtigt!</b> Du kan kun kopiere denne {secret} én gang. Hvis du lukker dette vindue, vil du ikke kunne se det igen.", "app.containers.Admin.tools.apiTokens.createTokenName": "Navn", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Giv dit token et navn", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Dit token er blevet oprettet", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Luk", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "<PERSON><PERSON><PERSON>!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Opret et nyt token", "app.containers.Admin.tools.apiTokens.createdAt": "Oprettet", "app.containers.Admin.tools.apiTokens.delete": "Slet token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Er du sikker på, at du vil slette dette token?", "app.containers.Admin.tools.apiTokens.description": "Administrer dine API-tokens til vores offentlige API. For mere information, se vores {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "<PERSON>st brugt", "app.containers.Admin.tools.apiTokens.link": "API-dokumentation", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Navn", "app.containers.Admin.tools.apiTokens.noTokens": "Du har ikke nogen tokens endnu.", "app.containers.Admin.tools.apiTokens.title": "Offentlige API-tokens", "app.containers.Admin.tools.esriDisabled": "Esri-integrationen er en add-on-funktion. Kontakt Sø<PERSON> (<EMAIL>), hvis du vil have flere oplysninger om dette.", "app.containers.Admin.tools.esriIntegration2": "Esri-integration", "app.containers.Admin.tools.esriIntegrationButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Forbind din Esri-konto, og importer data fra ArcGIS Online direkte til dine kortlægningsprojekter.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri-logo", "app.containers.Admin.tools.esriKeyInputDescription": "Tilføj din Esri API-nøgle for at tillade import af dine kortlag fra ArcGIS Online i kortfanerne i projekter.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API-nøgle", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Indsæt API-nøgle her", "app.containers.Admin.tools.esriMaps": "Esri-kort", "app.containers.Admin.tools.esriSaveButtonError": "Der opstod en fejl, da du gemte din nøgle, prøv venligst igen.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API-nøgle gemt", "app.containers.Admin.tools.esriSaveButtonText": "<PERSON><PERSON>", "app.containers.Admin.tools.learnMore": "Få mere at vide", "app.containers.Admin.tools.managePublicAPIKeys": "Administrer API-nøgler", "app.containers.Admin.tools.manageWidget": "Administrer widget", "app.containers.Admin.tools.manageWorkshops": "Administrer workshops", "app.containers.Admin.tools.powerBIAPIImage": "Power BI-billede", "app.containers.Admin.tools.powerBIDescription": "Brug vores plug & play Power BI-skabeloner til at få adgang til Go Vocal-data i dit Microsoft Power BI-arbejdsområde.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI er ikke en del af din licens. Kontakt Søren <PERSON>, hvis du vil have mere information om dette.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Download skabeloner", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "<PERSON><PERSON> du har til hensigt at bruge dine Go Vocal-data i et Power BI-dataflow, vil denne skabelon give dig mulighed for at oprette et nyt dataflow, der forbinder til dine Go Vocal-data. Når du har downloadet denne skabelon, skal du først finde og erstatte de følgende strenge ##CLIENT_ID## og ##CLIENT_SECRET## i skabelonen med dine offentlige API-legitimationsoplysninger, før du uploader til PowerBI.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Download skabelon til dataflow", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Dataflow-skabelon", "app.containers.Admin.tools.powerBITemplates.intro": "Bemærk: For at bruge en af disse Power BI-skabeloner, skal du først {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "oprette et sæt legitimationsoplysninger til vores offentlige API", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Denne skabelon opretter en Power BI-rapport baseret på dine Go Vocal-data. Den opsætter alle dataforbindelser til din Go Vocal-platform, opretter datamodellen og nogle standarddashboards. Når du åbner skabelonen i Power BI, vil du blive bedt om at indtaste dine offentlige API-legitimationsoplysninger. Du skal også indtaste Base Url for din platform, som er: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Download skabelon til rapportering", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Rapportskabelon", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Yderligere oplysninger om brug af dine Go Vocal-data i Power BI kan findes på vores {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "support artikel", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "{tenant<PERSON><PERSON>, select,  {https://matomo.org/} other {https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi}}", "app.containers.Admin.tools.powerBITemplates.title": "Power BI-skabeloner", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Administrer legitimationsoplysningerne til at oprette brugerdefinerede integrationer på vores offentlige API.", "app.containers.Admin.tools.publicAPIDisabled1": "Den offentlige API er ikke en del af din nuværende licens. Kontakt S<PERSON><PERSON>, hvis du vil have mere information om dette.", "app.containers.Admin.tools.publicAPIImage": "Offentligt API-billede", "app.containers.Admin.tools.publicAPITitle": "Offentlig API-adgang", "app.containers.Admin.tools.toolsLabel": "Værktøj", "app.containers.Admin.tools.widgetDescription": "Du kan oprette en widget, tilpasse den og tilføje den til din eget websted for at tiltrække deltagere til denne platform.", "app.containers.Admin.tools.widgetImage": "Widget-<PERSON><PERSON>", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Hold live videomøder, faciliter break-out gruppediskussioner og debatter. Saml input, stem og opnå konsensus præcis som du ville gøre offline.", "app.containers.Admin.tools.workshopsImage": "Workshop billede", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Deliberative online workshops", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "antal brugere på platformen i alt", "app.containers.AdminPage.DashboardPage._blank": "ukendt", "app.containers.AdminPage.DashboardPage.allGroups": "Alle grupper", "app.containers.AdminPage.DashboardPage.allProjects": "Alle projekter", "app.containers.AdminPage.DashboardPage.allTime": "Al tid", "app.containers.AdminPage.DashboardPage.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Der kræves et basisdatasæt for at måle platformbrugernes repræsentation.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "<PERSON><PERSON> snart", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Vi arbejder i øjeblikket på {fieldName} dashboardet, det vil snart være tilgængeligt", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# emne er} other {# emner er}} skjult i denne graf. Skift til {tableViewLink} for at få vist alle data.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} for brugerregistrering", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} ud af {total} brugere inkluderet ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Vis {numberOfHiddenItems} mere", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Valg<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Angiv venligst et basisdatasæt.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Repræsentativitetsscore:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "<PERSON><PERSON> score a<PERSON><PERSON><PERSON><PERSON>, hvor nø<PERSON>t platformens brugerdata afspejler den samlede befolkning. Få mere at vide om {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Påkrævet", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Indsend basisdata", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "tabelvisning", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "<PERSON><PERSON> befolkning", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Brugere", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Tilføj en aldersgruppe", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} og derover", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Aldersgruppe", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Aldersgruppe(r) af {upperBound} og derover er ikke inkluderet.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Aldersgruppe {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Aldersgrupper", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "og over", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "<PERSON><PERSON><PERSON> e<PERSON><PERSON> gruppering", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Ryd alt", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "<PERSON>a", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, s<PERSON> de stemmer overens med dit basisdatasæt.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "<PERSON><PERSON>r<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Gem", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "Til", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Rediger aldersgrupper", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Dette element vil ikke blive beregnet.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "Se mindre", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Se {numberOfHiddenItems} mere...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Grundmåned (valgfri)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Aldersgrupper (fødselsår)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "<PERSON><PERSON> snart", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Komplet", "app.containers.AdminPage.DashboardPage.components.Field.default": "Standard", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Udfyld alle akt<PERSON><PERSON> inds<PERSON>linger, el<PERSON> de<PERSON><PERSON><PERSON> de indstillinger, som du ø<PERSON><PERSON> at udelade fra grafen. Mindst én indstilling skal udfyldes.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "<PERSON><PERSON><PERSON>tændig", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "<PERSON>tal in<PERSON> i alt", "app.containers.AdminPage.DashboardPage.components.Field.options": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.save": "Gem", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Gemte", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "<PERSON><PERSON><PERSON> venlig at {setAgeGroupsLink} først for at begynde at indtaste basisdata.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "Indstil aldersgrupper", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Gennemsnitlig svartid: {days} dage", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Gennemsnitlig antal dage til at reagere", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "feedback givet", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Indtastningsstatus", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Input efter status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Antal input", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Officiel opdatering", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Procentdel af input", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status ændret", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "I alt", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Rediger basisdata", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "hvordan vi beregner repræsentativitetsscorer", "app.containers.AdminPage.DashboardPage.continuousType": "Uden en tidslinje", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Kumulativ total", "app.containers.AdminPage.DashboardPage.customDateRange": "Brugerdefineret", "app.containers.AdminPage.DashboardPage.day": "dag", "app.containers.AdminPage.DashboardPage.false": "falsk", "app.containers.AdminPage.DashboardPage.female": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Top 5 input efter antal stemmer", "app.containers.AdminPage.DashboardPage.fromTo": "fra {from} til {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Dashboard for aktiviteter på platformen", "app.containers.AdminPage.DashboardPage.helmetTitle": "Admin dashboard-side", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "<PERSON><PERSON><PERSON><PERSON> ressource for at vise efter projekt", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "<PERSON><PERSON><PERSON><PERSON> ressource for at vise efter emne", "app.containers.AdminPage.DashboardPage.inputs1": "Input", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Input efter status", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "<PERSON><PERSON><PERSON>g brugergruppe", "app.containers.AdminPage.DashboardPage.male": "mandlige", "app.containers.AdminPage.DashboardPage.month": "må<PERSON>", "app.containers.AdminPage.DashboardPage.noData": "Der er ingen data, der skal vises.", "app.containers.AdminPage.DashboardPage.noPhase": "Der er ikke oprettet nogen fase for dette projekt", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Antal<PERSON> af deltagere, der har sendt input, stemt eller kommenteret.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Dislikes", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON>s", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Stemmer i alt", "app.containers.AdminPage.DashboardPage.overview.management": "Forvaltning", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projekter og deltagelse", "app.containers.AdminPage.DashboardPage.overview.showLess": "Vis mindre", "app.containers.AdminPage.DashboardPage.overview.showMore": "Vis mere ", "app.containers.AdminPage.DashboardPage.participants": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.participationPerProject": "Deltagelse pr. projekt", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Deltagelse pr. emne", "app.containers.AdminPage.DashboardPage.perPeriod": "Pr. {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "<PERSON> foregående 30 dage", "app.containers.AdminPage.DashboardPage.previous90Days": "<PERSON> foregående 90 dage", "app.containers.AdminPage.DashboardPage.previousWeek": "<PERSON><PERSON>e uge", "app.containers.AdminPage.DashboardPage.previousYear": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.projectType": "Projekt type : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "<PERSON><PERSON> g<PERSON>t er nødvendigt for at beregne platformbrugernes repræsentativitet i forhold til den samlede befolkning.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Angiv venligst et basisdatasæt.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Se, hvor repræsentative dine platformsbrugere er i forhold til den samlede befolkning - baseret på data, der indsamles under brugerregistreringen. Få mere at vide om {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Se, hvor repræsentative dine platformsbrugere er i forhold til den samlede befolkning - baseret på data indsamlet under brugerregistreringen.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Repræsentation af Fællesskabet", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Tilbage til instrumentbrættet", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Ingen af de aktiverede registreringsfelter er understøttet i øjeblikket.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Her kan du vise/skjule elementer på dashboardet og indtaste basisdata. <PERSON>n de aktiverede felter for {userRegistrationLink} vises her.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Rediger basisdata", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "brugerregistrering", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Indsend basisdata", "app.containers.AdminPage.DashboardPage.resolutionday": "i dage", "app.containers.AdminPage.DashboardPage.resolutionmonth": "i m<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.resolutionweek": "i uger", "app.containers.AdminPage.DashboardPage.selectProject": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.AdminPage.DashboardPage.selectedProject": "nuværende projektfilter", "app.containers.AdminPage.DashboardPage.selectedTopic": "aktuelt tag-filter", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Find ud af, hvad der sker på din platform.", "app.containers.AdminPage.DashboardPage.tabOverview": "Oversigt", "app.containers.AdminPage.DashboardPage.tabReports": "Rapporter", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Repræsentation", "app.containers.AdminPage.DashboardPage.tabUsers": "Brugere", "app.containers.AdminPage.DashboardPage.timelineType": "Tidslinje", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboard", "app.containers.AdminPage.DashboardPage.total": "I alt", "app.containers.AdminPage.DashboardPage.totalForPeriod": "<PERSON><PERSON> {period}", "app.containers.AdminPage.DashboardPage.true": "sandt", "app.containers.AdminPage.DashboardPage.unspecified": "uspecificeret", "app.containers.AdminPage.DashboardPage.users": "Brugere", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Brugere efter alder", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Brugere efter geografisk område", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Brugere efter køn", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.week": "uge", "app.containers.AdminPage.FaviconPage.favicon": "Favorikon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Tips til valg af favicon-billede: ud<PERSON><PERSON> et enkelt billede, da den viste billedstørrelse er meget lille. Billedet skal gemmes som et PNG-billede og skal være kvadratisk med en gennemsigtig baggrund (el<PERSON> en hvid baggrund, hvis det er nødvendigt). Din favicon bør kun indstilles én gang, da ændringer vil kræve teknisk støtte.", "app.containers.AdminPage.FaviconPage.save": "Gem", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "<PERSON>get gik galt, prøv venligst igen senere.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Succes!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "<PERSON>e ændringer er blevet gemt.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Tilføj", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Slet", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Mappeadministratorer", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Mappeansvarlige kan redigere mappebeskrivelsen, oprette nye projekter i mappen og have projektstyringsrettigheder over alle projekter i mappen. De kan ikke slette projekter, og de har ikke adgang til projekter, der ikke befinder sig i deres mappe. Du kan {projectManagementInfoCenterLink} finde flere information om projektstyringsrettigheder.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Ingen match fundet", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "<PERSON><PERSON><PERSON><PERSON> vores H<PERSON>p-center", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "<PERSON><PERSON><PERSON> brugere", "app.containers.AdminPage.FoldersEdit.addToFolder": "Tilføj til mappe", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Arkiveret", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "<PERSON><PERSON> denne mappe", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Beskrivelse", "app.containers.AdminPage.FoldersEdit.draftStatus": "Udkast", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> filer til denne mappe", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Filerne bør ikke være større end 50 Mb. Tilføjede filer vil blive vist på mappesiden.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Besk<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Der er ingen projekter i denne mappe. Gå tilbage til hovedfanen Projekter for at oprette og tilføje projekter.", "app.containers.AdminPage.FoldersEdit.folderName": "Mappenavn", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Overskriftsbillede", "app.containers.AdminPage.FoldersEdit.multilocError": "Alle tekstfelter skal udfyldes for hvert sprog.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Der er ingen projekter, som du kan tilføje til denne mappe.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Projektkort billede", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "<PERSON><PERSON> proje<PERSON>", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "<PERSON><PERSON><PERSON><PERSON>, der er tilføjet til denne mappe", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "<PERSON><PERSON><PERSON><PERSON>, du kan tilføje til denne mappe", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "<PERSON><PERSON><PERSON><PERSON>, om denne mappe er \"udkast\", \"offentliggjort\" eller \"arkiveret\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Offentliggjort", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Fjernes fra mappe", "app.containers.AdminPage.FoldersEdit.save": "Gem", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "<PERSON>get gik galt, prøv venligst igen senere.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Succes!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "<PERSON>e ændringer er blevet gemt.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "vises på hjemmesiden", "app.containers.AdminPage.FoldersEdit.statusLabel": "Status for offentliggørelse", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "<PERSON><PERSON>, hvor<PERSON>r projekt<PERSON> hø<PERSON>mmen, definer en visuel identitet og deler information.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "<PERSON><PERSON>, hvor<PERSON>r projekt<PERSON> hø<PERSON>mmen, definer en visuel identitet og deler information.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Alle tekstfelter skal udfyldes.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Titel", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Opret en ny mappe", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Se mappe", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Tilpas billedet og teksten til heltebanneret.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Banner for helten", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Gem heltebanner", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub er et sted, hvor du kan finde inspiration til dine projekter ved at gennemse projekter fra andre platforme.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Rediger din platforms vilkår og betingelser og privatlivspolitik. <PERSON> sider, herunder siderne Om og FAQ, kan redigeres under fanen {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Politikker for platformen", "app.containers.AdminPage.PagesEdition.privacy-policy": "Fortrolighedspolitik", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Vilkår", "app.containers.AdminPage.Project.confirmation.description": "Denne handling kan ikke fortrydes.", "app.containers.AdminPage.Project.confirmation.no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.title": "Er du sikker på, at du vil nulstille alle deltagelsesdata?", "app.containers.AdminPage.Project.confirmation.yes": "Nulstil alle deltagelsesdata", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON><PERSON>, kom<PERSON><PERSON><PERSON>, stemmer, re<PERSON><PERSON><PERSON>, svar på under<PERSON><PERSON>, svar på afstemninger, frivillige og tilmeldte til arrangementer. I tilfælde af afstemningsfaser vil denne handling rydde stemmerne, men ikke valgmulighederne.", "app.containers.AdminPage.Project.data.title": "Slet alle deltagelsesdata fra dette projekt", "app.containers.AdminPage.Project.resetParticipationData": "Nulstil alle deltagelsesdata", "app.containers.AdminPage.Project.settings.accessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.back": "Tilbage", "app.containers.AdminPage.Project.settings.data": "Data", "app.containers.AdminPage.Project.settings.description": "Beskrivelse", "app.containers.AdminPage.Project.settings.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.general": "Generelt", "app.containers.AdminPage.Project.settings.projectTags": "Tilladte input-tags", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Liste over projekter på platformen", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Dashboard for projekter", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Opret nye projekter eller administrer eksisterende projekter.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDashboard.published": "Offentliggjort", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Luk indstillingspanelet", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Center", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "<PERSON><PERSON> bredde", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "<PERSON><PERSON> af knapper", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Knaptekst", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Indtast tekst til knappen", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Knaptype", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Se<PERSON>nd<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Knap-URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Indtast en URL for knappen", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Opsætning af kolonner", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Beskrivelse", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Beskrivelse af hjemmesiden", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Vises på projektkortet på startsiden.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Vises på projektsiden. Beskriv klart og tydeligt, hvad projektet handler om, hvad du forventer af dine brugere, og hvad de kan forvente af dig.", "app.containers.AdminPage.ProjectDescription.errorMessage": "<PERSON>get gik galt, prøv venligst igen senere", "app.containers.AdminPage.ProjectDescription.preview": "Eksempel", "app.containers.AdminPage.ProjectDescription.save": "Gem", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "<PERSON>e ændringer er blevet gemt.", "app.containers.AdminPage.ProjectDescription.saved": "Gemt!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "<PERSON><PERSON><PERSON> dig for, hvilket budskab du ønsker at give din publikum. Rediger din projekt, og berig det med billeder, videoer, vedhæftede filer,... Disse information hjælper de besøgende til at forstå, hvad din projekt handler om.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Projektbeskrivelse", "app.containers.AdminPage.ProjectDescription.whiteSpace": "<PERSON><PERSON> plads", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Me<PERSON><PERSON> kant", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON><PERSON> hø<PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "St<PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Middel", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Lille", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON><PERSON> redigering", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Standardbreddegraden for kortets midtpunkt. Accepterer en værdi mellem -90 og 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Standardlængde for kortets midtpunkt. Accepterer en værdi mellem -90 og 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Rediger kortlag", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Rediger lag", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "<PERSON>get gik galt, prøv venligst igen senere", "app.containers.AdminPage.ProjectEdit.MapTab.here": "her", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Importere GeoJSON-fil", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Standardbreddegrad", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Lagfarve", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Alle elementer i laget vil blive stylet med denne farve. Denne farve vil også overskrive enhver eksisterende styling i din GeoJSON-fil.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Markerings-<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Udvalgt eventuelt et ikon, der vises i markørerne. <PERSON><PERSON> på {url} for at se listen over de i<PERSON><PERSON>, du kan udvalgt.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Lagnavn", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Dette lagnavn vises i kortlegenden", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Lag tooltip", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "<PERSON><PERSON> tekst vises som et væ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, når du holder musen over lagfunktionerne på kortet", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Kortlag", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Vi understøtter i øjeblikket GeoJSON-filer. <PERSON><PERSON><PERSON> {supportArticle} for at få tips om, hvordan du konverterer og stiliserer kortlag.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Standardlængde", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Kortets standardcenter og -zoom", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Kortets standardcenterpunkt og zoomniveau. Juster værdierne nedenfor manuelt, eller klik på knappen {button} toppen til højre på kortet for at gemme kortets aktuelle centerpunkt og zoomniveau som standardværdier.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Tilpas kortvisningen, herunder upload og styling af kortlag og indstilling af kortets centrum og zoomniveau.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Konfiguration af kortet", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Kortkonfigurationen deles i øjeblikket på tværs af faser, og du kan ikke oprette forskellige kortkonfigurationer pr. fase.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Fjern lag", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Gem", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Gem zoom", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "stø<PERSON>artikel", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "{tenant<PERSON><PERSON>, select,  {https://matomo.org/} other {https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view}}", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Ikke navngivet lag", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Kortets zoomniveau", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Standardzoomniveauet for kortet. Accepterer en værdi mellem 1 og 17, hvor 1 er fuldt udzoomet (hele verden er synlig) og 17 er fuldt indzoomet (blokke og bygninger er synlige)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonymisering af alle brugerdata", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Alle brugernes input i undersøgelsen vil blive anonymiseret, inden de registreres", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Brugerne skal stadig opfylde kravene til deltagelse under fanen \"Adgangsrettigheder\". Brugerprofildata vil ikke være tilgængelige i eksport af undersøgelsesdata.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "Hvis du aktiverer denne mulighed, vises brugerregistreringsfelterne som den sidste side i undersøgelsen i stedet for som en del af registreringssprocessen.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demografiske felter i spørgeskemaet", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Vis demografiske felter i undersøgelsen?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "<PERSON><PERSON><PERSON> mere om, hvordan autodeling fungerer i denne artikel.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "{tenant<PERSON><PERSON>, select,  {https://matomo.org/} other {https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64}}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Automatisk deling af resultater", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Afstemningsresultaterne deles på platformen og via e-mail til deltagerne, når fasen slutter. Dette sikrer gennemsigtighed.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Deling af resultater", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Tilføj en svarmulighed", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Tilføj et afstemningsspørgsmål", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Slet", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Slet", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "<PERSON><PERSON> svarmulighed", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "<PERSON><PERSON> s<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "<PERSON><PERSON> s<PERSON>ø<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Eksporter afstemningsresultaterne", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Det maksimale antal valgmuligheder er større end antallet af valgmuligheder", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Flere valg", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "<PERSON><PERSON> muligheder", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Alle spørgsmål skal have svarmuligheder", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Kun <PERSON>n valgmulighed", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "De adspurgte har kun ét valg", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Administ<PERSON> svar<PERSON><PERSON>gh<PERSON>r for: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "afstemning_eksport", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Her kan du oprette meningsmålings<PERSON><PERSON><PERSON><PERSON>, angi<PERSON>, som deltagerne skal udvalgt mellem for hvert spø<PERSON>sm<PERSON><PERSON>, bestemme, om deltagerne kun skal kunne udvalgt ét svarvalg (enkelt valg) eller flere svarmuligheder (flere valg), og eksportere meningsmålingens resultater. Du kan oprette flere afstemningsspørgsmål i én afstemning.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Gem", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Gem", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "Gem", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Enkelt valg", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "<PERSON><PERSON><PERSON><PERSON> for afstemninger og resultater", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON> <PERSON>, til<PERSON><PERSON><PERSON> <PERSON>inger eller kopier indlæg til den næste projektfase.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Administrer forslag, giv <PERSON> og tildel emner.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Idéoverblik", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Deling af resultater er slået fra.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Afstemningsresultaterne bliver ikke delt i slutningen af fasen, med mindre du ændrer det i faseopsætningen.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Disse resultater vil automatisk blive delt, n<PERSON>r fasen er slut. <PERSON><PERSON><PERSON> slut<PERSON><PERSON><PERSON> for denne fase for at ændre, hvornår resultaterne deles.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Eksportér undersøgelsesresultaterne (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Resultater", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Her kan du downloade resultaterne af Typeform-undersøgelsen/undersøgelserne i dette projekt som en Excel-fil.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "S<PERSON><PERSON>rgeskema", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Resultater af under<PERSON><PERSON><PERSON>sen", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Spørgeundersøgelse", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Se svarene i undersøgelsen", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "<PERSON>r du sikker?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Beskrivelse", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Brug dette til at forklare, hvad der kræves af de frivillige, og hvad de kan forvente.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "<PERSON><PERSON> ikke gemme, fordi formularen indeholder fejl.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Titel", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Slet", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "En sag er en handling eller aktivitet, som deltagerne kan melde sig frivilligt til.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Rediger årsag", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Tilføj en beskrivelse", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Tilføj en titel", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Eksporter frivillige", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "En sag er en handling eller aktivitet, som deltagerne kan melde sig frivilligt til.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Gem", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Her kan du indstille de projekter, som brugerne kan melde sig frivilligt til, og hente de frivillige.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Frivilligt arbejde", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {ingen frivillige} one {# frivillig} other {# frivillige}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "tildeling af budget", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Tildel et budget til valgmulighederne, og bed deltagerne om at vælge deres foretrukne valgmuligheder, der passer inden for det samlede budget.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Tildeling af budget", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "At give brugerne mulighed for at kommentere kan forvride afstemningsprocessen.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "Kredit", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Standardvisning af muligheder", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Handlinger for brugere", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "<PERSON><PERSON><PERSON><PERSON>, hvilke yderligere handlinger brugerne kan udføre.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Fast beløb", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "<PERSON><PERSON> den er tom, vil den som standard være \"stem\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "<PERSON><PERSON><PERSON> mere om, h<PERSON><PERSON><PERSON><PERSON> du skal bruge <b> {voteTypeDescription} </b> i vores {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "<PERSON><PERSON><PERSON><PERSON> stemmer pr. valg<PERSON>lighed", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "<PERSON><PERSON><PERSON><PERSON> antal stemmer", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Du kan begrænse antallet af stemmer, en bruger kan afgive i alt (med et maksimum på én stemme pr. mulighed).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "flere stemmer pr. valgmulighed", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Brugerne får en mængde tokens, som de kan fordele mellem mulighederne", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Flere stemmer pr. mulighed", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Antal stemmer pr. bruger", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Oversigt over analyse af muligh<PERSON>r", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> at stemme om", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "én stemme pr. valg<PERSON>lighed", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Brugere kan vælge at godkende en af mulighederne", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "En stemme pr. mulighed", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Ubegrænset", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Hvad skal en stemme hedde?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole<PERSON>, point, CO2-kreditter...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole<PERSON>, point, Co2-kreditter...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Hver afstemningsmetode har forskellige forhåndsindstillinger", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Afstemningsmetode", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Afstemningsmetoden bestemmer reglerne for, hvordan brugerne stemmer.", "app.containers.AdminPage.ProjectEdit.addNewInput": "T<PERSON><PERSON><PERSON><PERSON> et input", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Du kan tilføje dit projekt til en mappe nu, eller gøre det senere i projektindstillingerne", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Tilladte input-tags", "app.containers.AdminPage.ProjectEdit.altText": "Alternativ tekst", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonym menings<PERSON>", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "N<PERSON><PERSON> den er aktiveret, er det umuligt at se, hvem der har stemt om hvad. Brugere skal stadig have en konto og kan kun stemme én gang.", "app.containers.AdminPage.ProjectEdit.approved": "Godkendt", "app.containers.AdminPage.ProjectEdit.archived": "Arkiveret", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Arkiverede projekter er stadig synlige, men tillader ikke yderligere deltagelse", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Arkiveret", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "<PERSON>te område kan ikke slettes, fordi det bruges til at vise projekter på følgende mere tilpassede side(r). Du skal fjerne forbindelsen mellem området og siden eller slette siden, før du kan slette området.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Alle områder", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Projektet vil blive vist på alle områdefiltre.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Områdefilter", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projekter kan filtreres på hjemmesiden ved hjælp af områder. Områder kan indstilles {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "her", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Intet specifikt område", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Projektet vises ikke, når der filtreres efter område.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Projektet vises på de(t) valgte områdefilter(e).", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Liste", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Mest diskuteret", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Tilføj indhold til unders<PERSON>gelsen", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Besvarelserne til denne undersøgelse er begyndt at komme ind. Ændringer i undersøgelsen kan resultere i tab af data og ufuldstændige data i de eksporterede filer.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "<PERSON><PERSON><PERSON><PERSON><PERSON> er gemt med succes", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "{tenant<PERSON><PERSON>, select,  {https://matomo.org/} other {https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey}}", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Undersøgelse", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Vælg en afstemningsmetode, og lad brugerne prioritere mellem et par forskellige muligheder.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Gennemfør en afstemning eller en prioriteringsøvelse", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Opret et projekt ud fra en skabelon", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Opret en ekstern undersøgelse", "app.containers.AdminPage.ProjectEdit.createInput": "Tilføj nyt input", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Opret en undersøgelse på platformen", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Opret en undersøgelse uden at forlade vores platform.", "app.containers.AdminPage.ProjectEdit.createPoll": "Opret en afstemning", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Opsæt et spørgeskema med flere valgmuligheder.", "app.containers.AdminPage.ProjectEdit.createProject": "Nyt projekt", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Integrer en Typeform, Google Form, surveyXact, Qualtrics eller Enalyzer undersøgelse.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Du kan indstille standardrækkefølgen for de indlæg, der skal vises på projektets hovedside.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sortering", "app.containers.AdminPage.ProjectEdit.departments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Beskrivelse", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "<PERSON>te vil aktivere eller deaktivere disliking, men liking vil stadig være aktiveret. V<PERSON> an<PERSON><PERSON><PERSON>, at du lader den være deaktiveret, medmindre du foretager en analyse af muligheder.", "app.containers.AdminPage.ProjectEdit.disabled": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "<PERSON><PERSON> dislikes pr. deltager", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Aktiver dislike", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Indsaml feedback på et dokument", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Indlejr en interaktiv PDF, og indsaml kommentarer og feedback med Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Aktiveret", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Projektudkast er skjult for alle andre end administratorer og tilknyttede projektledere.", "app.containers.AdminPage.ProjectEdit.draft": "Udkast", "app.containers.AdminPage.ProjectEdit.draftStatus": "Udkast", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabled": "Aktiveret", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "<PERSON><PERSON><PERSON><PERSON>, hvilke deltagende handlinger brugerne kan foretage.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalysator", "app.containers.AdminPage.ProjectEdit.eventsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Vedhæftede filer (maks. 50 MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Filer bør ikke være større end 50 Mb. Tilføjede filer vil blive vist på projektinformationssiden.", "app.containers.AdminPage.ProjectEdit.filesTab": "Filer", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Find frivillige", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Bed deltagerne om at melde sig frivilligt til aktiviteter og projekter.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "<PERSON>m mappeadministrator kan du vælge en mappe, når du opretter projektet, men kun en administrator kan ændre den bagefter.", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Mappekortbillede med alternativ tekst", "app.containers.AdminPage.ProjectEdit.folderSelectError": "<PERSON><PERSON><PERSON><PERSON> en mappe, som du vil tilføje dette projekt til.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Brugerdefineret indhold", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Der er begyndt at komme bidrag til denne formular. Ændringer i formularen kan medføre tab af data og ufuldstændige data i de eksporterede filer.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "<PERSON>ren er gemt med succes", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "<PERSON><PERSON><PERSON><PERSON>sens afslutning", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Fra en skabelon", "app.containers.AdminPage.ProjectEdit.generalTab": "Generelt", "app.containers.AdminPage.ProjectEdit.google_forms": "Google-formularer", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Alternativ tekst til overskriftsbillede", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Overskriftsbillede", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NY", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Giv information til brugerne, eller brug rapportbyggeren til at dele resultater fra tidligere faser.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Del information eller resultater", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Indsaml input og feedback", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "<PERSON>ret eller indsaml input, stemmer og/eller kommentarer. Vælg mellem forskellige typer input: idéindsamling, mulighedsanalyse, spørgsmål og svar, problemidentifikation og mere.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Hvem er ansvarlig for at behandle indlæggene?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Alle nye input i dette projekt vil blive tildelt denne person. Den tildelte person kan ændres i {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Kommentarer til indlæg", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Indtastningsformular", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "idéoverblik", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Idéoverblik", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Indsendelse af indlæg", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reagerer på input", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Standardvisning", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Udvalgt standardvisningen til at vise input: liste i en gittervisning eller pins på et kort. Deltagerne kan manuelt skifte mellem de to visninger.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Inspirationscenter", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Indlejr Konveio URL", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Maks. antal input en bruger kan 'like' (en idé) eller stemme (på et forslag)", "app.containers.AdminPage.ProjectEdit.limited": "Begrænset", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "<PERSON><PERSON><PERSON>s flere skabeloner", "app.containers.AdminPage.ProjectEdit.mapDisplay": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.mapTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxDislikes": "<PERSON><PERSON><PERSON><PERSON> antal dislikes", "app.containers.AdminPage.ProjectEdit.maxLikes": "<PERSON><PERSON><PERSON><PERSON> antal likes", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Det maksimale antal stemmer pr. mulighed skal være mindre end eller lig med det samlede antal stemmer.", "app.containers.AdminPage.ProjectEdit.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Deltagerne må ikke overskride dette budget, når de indsender deres valg.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "<PERSON><PERSON><PERSON><PERSON>, at deltagerne skal opfylde et minimumsbudget for at indsende deres valg (angiv \"0\", hvis du ikke ønsker at fastsætte et minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "<PERSON><PERSON><PERSON><PERSON> vores H<PERSON>p-center", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Hvem er projektlederne?", "app.containers.AdminPage.ProjectEdit.moreDetails": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.containers.AdminPage.ProjectEdit.needInspiration": "Har du brug for inspiration? Udforsk lignende projekter fra andre byer på {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Kom med dit bidrag", "app.containers.AdminPage.ProjectEdit.newIdea": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newInitiative": "Tilføj et initiativ", "app.containers.AdminPage.ProjectEdit.newIssue": "Tilføj nyt input", "app.containers.AdminPage.ProjectEdit.newOption": "<PERSON><PERSON><PERSON><PERSON><PERSON> muli<PERSON>", "app.containers.AdminPage.ProjectEdit.newPetition": "Tilføj en underskriftsindsamling", "app.containers.AdminPage.ProjectEdit.newProject": "Nyt projekt", "app.containers.AdminPage.ProjectEdit.newProposal": "Tilføj et forslag", "app.containers.AdminPage.ProjectEdit.newQuestion": "Tilfø<PERSON> et spørgsmål", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Nyeste", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "<PERSON><PERSON><PERSON> et gyldigt beløb", "app.containers.AdminPage.ProjectEdit.noFolder": "Ingen mappe", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— Ingen mappe —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Ingen skabeloner fundet", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Indtast venligst en projekttitel", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "<PERSON><PERSON><PERSON> et gyldigt antal", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "<PERSON><PERSON> synlig for admin", "app.containers.AdminPage.ProjectEdit.optionNo": "Ingen", "app.containers.AdminPage.ProjectEdit.optionYes": "Ja (udvalgt mappe)", "app.containers.AdminPage.ProjectEdit.participationLevels": "<PERSON><PERSON><PERSON> for deltagelse", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "<PERSON><PERSON>d ønsker du at gøre?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "<PERSON><PERSON><PERSON><PERSON>, hvordan brugerne kan deltage.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Du kan angive, hvem der kan udføre hver enkelt handling, og du kan stille yderligere spørgsmål til deltagerne for at indsamle flere oplysninger.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Krav til deltagere og spørgsmål", "app.containers.AdminPage.ProjectEdit.pendingReview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.permissionsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.phaseEmails": "Notifikationer", "app.containers.AdminPage.ProjectEdit.pollTab": "Afstemning", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "<PERSON><PERSON>t stemmer", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Billede af projektkort", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Dette billede er en del af projektkortet; det kort, der opsummerer projektet og f.eks. vises på hjemmesiden.\n\n    Du kan finde flere oplysninger om anbefalede billedopløsninger på {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Mappe", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Dette billede vises øverst på projektsiden.\n\n    For yderligere oplysninger om anbefalede billedopløsninger, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Alternativ tekst til projektkortbillede", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Giv en kort beskrivelse af billedet til synshandicappede brugere. Det hjælper skærmlæsere med at formidle, hvad billedet handler om.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Projektstyring", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Projektledere kan redigere projekter, administrere indlæg og sende e-mails til deltagere. Du kan {moderationInfoCenterLink} for at finde flere information om de rettigheder, der er tildelt projektledere.", "app.containers.AdminPage.ProjectEdit.projectName": "Projektnavn", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Projekttype", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Projekter med en tidslinje har en klar begyndelse og slutning og kan have forskellige faser. Projekter uden en tidslinje er kontinuerlige.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Projekttypen kan ikke ændres senere.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Du kan indstille projektet til at være usynligt for visse brugere.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Projektets synlighed", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Leder du efter projektets status? Nu kan du til enhver tid ændre den direkte fra projektsidens overskrift.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Offentliggjorte projekter er synlige for alle eller en del af gruppen, hvis de er valgt.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Offentliggjort", "app.containers.AdminPage.ProjectEdit.purposes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Tilfældige", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Nulstil deltagelsesdata", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Der opstod en fejl ved forsøg på at gemme dine data. Prøv venligst igen.", "app.containers.AdminPage.ProjectEdit.saveProject": "Gem", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Succes!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Din formular er blevet gemt!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Søg i skabelonerne", "app.containers.AdminPage.ProjectEdit.selectGroups": "<PERSON><PERSON><PERSON><PERSON> gruppe(r)", "app.containers.AdminPage.ProjectEdit.setup": "Opsætning", "app.containers.AdminPage.ProjectEdit.shareInformation": "Del information", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap-undersøgelse", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Opsæt og personliggør din projekt.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "besøg vores supportcenter", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Tilføj indhold til unders<PERSON>gelsen", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# valgmuligheder} one {# valg} other {# valg}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, jeg vil gerne logge af", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Besvarelserne til denne undersøgelse er begyndt at komme ind. Ændringer i undersøgelsen kan resultere i tab af data og ufuldstændige data i de eksporterede filer.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Upload af fil", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Import", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI-resuméer af korte svar, lange svar og opfølgningsspørgsmål på sentimentskalaen kan tilgås fra AI-fanen i venstre sidepanel.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "<PERSON><PERSON><PERSON> skala", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrix", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "<PERSON><PERSON> svar", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Multiple choice - vælg mange", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Billedvalg - vælg mange", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Ny indsendelse", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Ingen besvarelser i undersøgelsen endnu", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "<PERSON><PERSON> for besvarelser", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "<PERSON><PERSON> for besvarelser", "app.containers.AdminPage.ProjectEdit.survey.optional2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "<PERSON>vis der ikke tilføjes logik, vil undersø<PERSON>sen følge sit normale flow. Hvis både siden og dens spørgsmål har logik, vil spørgsmålslogikken have forrang. <PERSON><PERSON><PERSON> for, at dette stemmer overens med dit planlagte undersøgelsesflow. For mere information, besøg {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Placering", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "<PERSON>r du sikker på, at du vil rejse?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Dine nuværende ændringer bliver ikke gemt.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Rangering", "app.containers.AdminPage.ProjectEdit.survey.rating": "Bed<PERSON><PERSON>lse", "app.containers.AdminPage.ProjectEdit.survey.required2": "Krævet", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {svar} one {svar} other {svar}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# svar} one {# svar} other {# svar}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Flere valg - vælg et", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Lineær skala for følelser", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Esri shapefile upload", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "<PERSON><PERSON><PERSON><PERSON><PERSON> er gemt med succes", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Undersøgelse", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "<PERSON><PERSON> fra <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.text2": "<PERSON><PERSON> svar", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "<PERSON><PERSON><PERSON> {count} svar", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "Se", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Indlejre URL", "app.containers.AdminPage.ProjectEdit.surveyService": "Service", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Du kan finde flere information om, hvordan du indlejrer en undersøgelse {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/da/articles/7025887-oprettelse-af-et-eksternt-undersogelsesprojekt", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "her", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "<PERSON>te tag kan ikke slettes, fordi det bruges til at vise projekter på følgende flere brugerdefinerede sider. ", "app.containers.AdminPage.ProjectEdit.titleGeneral": "<PERSON><PERSON><PERSON> for projektet", "app.containers.AdminPage.ProjectEdit.titleLabel": "Titel", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "<PERSON><PERSON><PERSON><PERSON> en titel, der er kort, fængende og tydelig. Den vil blive vist i dropdown-oversigten og på projekt liste på startsiden.", "app.containers.AdminPage.ProjectEdit.topicLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Vælg {topicsCopy} for dette projekt. Brugere kan bruge disse til at filtrere projekter efter.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Samlet budget", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Populære", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "<PERSON><PERSON><PERSON> til<PERSON>", "app.containers.AdminPage.ProjectEdit.unlimited": "Ubegrænset", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Brug skabelon", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "<PERSON>is projekt", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Frivilligt arbejde", "app.containers.AdminPage.ProjectEdit.voteTermError": "Afstemningsbetingelser skal angives for alle lokaliteter", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# grupper kan se} one {# gruppe kan se} other {# grupper kan se}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> en begivenhed", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Yderligere oply<PERSON>ninger", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adresse 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "<PERSON><PERSON><PERSON> hvor <PERSON><PERSON><PERSON> foregår", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adresse 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "<PERSON><PERSON>eks. le<PERSON>, etage, bygning", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "<PERSON>der<PERSON><PERSON><PERSON> ad<PERSON><PERSON><PERSON>, der kan hjælpe med at identificere stedet, f.eks. bygningens navn, etagenummer osv.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Se supportartiklen", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Eksternt link", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Tilføj et link til en ekstern URL (f.eks. en begivenhedstjeneste eller en billetside). Indstilling af dette vil tilsidesætte standardadfærden for tilstedeværelsesknappen.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Brugerdefineret knaptekst", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Indstil knapteksten til en anden værdi end \"Registrér\", når en ekstern URL er indstillet.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Start", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Slut", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Slet", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Er du sikker på, at du ønsker at slette denne begivenhed? Der er ingen måde at fortryde dette på!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Beskrivelse af begivenheden", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "<PERSON><PERSON> begivenhed", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "For at sende e-mails til deltagere direkte fra platformen, skal platformens administratorer oprette en brugergruppe under fanen {userTabLink}. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "<PERSON><PERSON><PERSON> for begivenheder", "app.containers.AdminPage.ProjectEvents.eventImage": "<PERSON><PERSON> af beg<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Alternativ tekst til begivenhedsbillede", "app.containers.AdminPage.ProjectEvents.eventLocation": "Arrangementets placering", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Eksportér deltagere", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Vedhæftede filer (maks. 50 MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Vedhæftede filer vises under beskrivelsen af begivenheden.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Beliggenhed", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "<PERSON><PERSON><PERSON><PERSON> antal deltagere", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Opret en ny begivenhed", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Link til online begivenhed", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "<PERSON><PERSON> din begivenhed er online, skal du tilføje et link til den her.", "app.containers.AdminPage.ProjectEvents.preview": "Eksempel", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Forfin kortets placering", "app.containers.AdminPage.ProjectEvents.refineOnMap": "<PERSON><PERSON> placering på kort", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "<PERSON> kan præ<PERSON><PERSON>, h<PERSON> markø<PERSON> for din begivenhed vises, ved at klikke på kortet nedenfor.", "app.containers.AdminPage.ProjectEvents.register": "Registrer dig", "app.containers.AdminPage.ProjectEvents.registerButton": "Knap til registrering", "app.containers.AdminPage.ProjectEvents.registrant": "deltager", "app.containers.AdminPage.ProjectEvents.registrants": "deltagere", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Registreringsgrænse", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Gem", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "<PERSON>i kunne ikke gemme dine æ<PERSON>, prøv venligst igen.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Succes!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "<PERSON>e ændringer er blevet gemt.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "<PERSON><PERSON><PERSON> efter en placering", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Link kommende begivenheder til dette projekt, og vis dem på projektets begivenhedsside.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Titel og datoer", "app.containers.AdminPage.ProjectEvents.titleEvents": "Projektets begivenheder", "app.containers.AdminPage.ProjectEvents.titleLabel": "Navn på beg<PERSON>nhed", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Link knappen til en ekstern URL", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Som standard vises knappen til registrering af begivenheder på platformen, så brugerne kan registrere sig til en begivenhed. Du kan ændre dette til at linke til en ekstern URL i stedet.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Begr<PERSON><PERSON> antallet af tilmeldte til arrangementet", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Indstil et maksimalt antal deltagere til arrangementet. <PERSON><PERSON> grænsen er nået, accepteres der ikke flere tilmeldinger.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Brugere", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "<PERSON><PERSON><PERSON><PERSON><PERSON> filer til dit projekt", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Vedhæft filer fra denne liste til dit projekt, dine faser og begivenheder.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "<PERSON><PERSON><PERSON><PERSON><PERSON> filer som kontekst til Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> filer til dit Sensemaking-projekt for at give kontekst og indsigt.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "<PERSON><PERSON> snart", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Synkroniser under<PERSON><PERSON><PERSON>ser, upload interviews og lad AI forbinde punkterne på tværs af dine data.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Upload enhver fil", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Brug AI til at analysere filer", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Behandle udskrifter osv.", "app.containers.AdminPage.ProjectFiles.addFiles": "<PERSON><PERSON><PERSON><PERSON><PERSON> filer", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "AI-dre<PERSON> in<PERSON>t", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> uploadede filer for at finde vigtige emner.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "<PERSON>ad avanceret analyse af disse filer ved hjælp af AI-behandling.", "app.containers.AdminPage.ProjectFiles.askButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.categoryLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.chooseFiles": "<PERSON><PERSON><PERSON><PERSON> filer", "app.containers.AdminPage.ProjectFiles.close": "Luk", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Bekræft og upload", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Er du sikker på, at du vil slette denne fil?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "<PERSON>nne ikke indlæse markdown-fil.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Kunne ikke indlæse CSV-forhåndsvisning.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "Der vises maksimalt 50 rækker i CSV-forhåndsvisninger.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV-filen er for stor til at blive vist.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Slet filen", "app.containers.AdminPage.ProjectFiles.description": "Beskrivelse", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "Download fil", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Download hele filen", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "<PERSON>r<PERSON><PERSON> og slip alle filer her eller", "app.containers.AdminPage.ProjectFiles.editFile": "Rediger fil", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Beskrivelse", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Filnavnet må ikke indeholde et punktum.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Filnavn", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "Filnavn er påkrævet.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Download fil", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Eksempel", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "<PERSON>ne fil vil ikke blive uploadet, da den overskrider den maksimale grænse på 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Alle filer er uploadet med succes", "app.containers.AdminPage.ProjectFiles.generatingPreview": "<PERSON><PERSON>er forhåndsvisning...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Information", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "F.eks. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Lydinterviews, optagelser fra borgermøder", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "F.eks. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "<PERSON><PERSON>er, informationsdokumenter", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "F.eks. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.interview": "Interview", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Du kan højst uploade {maxFiles} filer ad gangen.", "app.containers.AdminPage.ProjectFiles.meeting": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.noFilesFound": "Ingen filer fundet.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Politik", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Forhåndsvisning understøttes endnu ikke for denne filtype.", "app.containers.AdminPage.ProjectFiles.report": "Rapport", "app.containers.AdminPage.ProjectFiles.retryUpload": "<PERSON><PERSON><PERSON><PERSON> at uploade igen", "app.containers.AdminPage.ProjectFiles.save": "Gem", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "Filen er blevet opdateret.", "app.containers.AdminPage.ProjectFiles.searchFiles": "Søg i filer", "app.containers.AdminPage.ProjectFiles.selectFileType": "Filtype", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strategisk plan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Du kan højst uploade {maxFiles} filer ad gangen.", "app.containers.AdminPage.ProjectFiles.unknown": "Ukendt", "app.containers.AdminPage.ProjectFiles.upload": "Upload", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# file} other {# files}} uploaded successfully, {numberOfErrors, plural, one {# error} other {# errors}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "Vis fil", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "<PERSON>k alle felter", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Beskrivelse af feltet", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Rediger input-formularen", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Aktiveret", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Medtag dette felt.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "<PERSON>get gik galt, prøv venligst igen senere", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "<PERSON><PERSON><PERSON> alle felter", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Input formular", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "<PERSON><PERSON>, hvilke oplysninger der skal angives, til<PERSON><PERSON><PERSON> korte beskrivelser eller instruktioner for at vejlede deltagernes svar, og angiv, om hvert felt er valgfrit eller obligatorisk.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "<PERSON><PERSON>, hvilke information der skal angives, tilføj korte beskrivelser eller instruktioner til at vejlede deltagernes svar, og angiv, om hvert felt er valgfrit eller obligatorisk", "app.containers.AdminPage.ProjectIdeaForm.required": "Påkrævet", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "<PERSON><PERSON><PERSON><PERSON>, at dette felt skal udfyldes.", "app.containers.AdminPage.ProjectIdeaForm.save": "Gem", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "<PERSON>e ændringer er blevet gemt.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Gemt!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "Vis formular", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automatiske e-mails", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Du kan konfigurere e-mails, som udløses af et faseniveau", "app.containers.AdminPage.ProjectTimeline.datesLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "<PERSON><PERSON><PERSON> spørgeskema<PERSON> ", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Undersøgelse", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "<PERSON>r du sikker på, at du ønsker at slette denne fase?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Beskrivelse af fase", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "<PERSON>iger fase", "app.containers.AdminPage.ProjectTimeline.endDate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Vedhæftede filer (maks. 50 MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Opret en ny fase", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Denne fase har ikke en foruddefineret slutdato.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Nogle metoders resultatdeling (såsom afstemningsresultater) vil ikke blive udlø<PERSON>, før der er valgt en slutdato.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "<PERSON><PERSON> snart du tilføjer en fase efter denne, vil den tilføje en slutdato til denne fase.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "<PERSON><PERSON><PERSON> at vælge en slutdato for dette indebærer, at:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Eksempel", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Der opstod en fejl ved indsendelse af formularen, prøv venligst igen.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Gemt!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "<PERSON>e ændringer er blevet gemt.", "app.containers.AdminPage.ProjectTimeline.startDate": "Startdato", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Startdato", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "Knap", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>s titel", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Navn på fase", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Upload vedhæftede filer", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_omfordeling", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologi (forsidefilter)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Hvordan skal tags i forsidefilteret kaldes? F. eks. tags, kate<PERSON><PERSON>, af<PERSON><PERSON>, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Tags kan konfigureres {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "her", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Betegnelse for et tag (ental)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tag", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Betegnelse for flere tags (flertal)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tags", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> felt", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Tilføj et nyt registreringsfelt", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "<PERSON><PERSON><PERSON><PERSON><PERSON> muli<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Svarformat", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Angiv et svarformat", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Giv en svarmulighed for alle sprog", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gemt med succes", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "<PERSON><PERSON><PERSON><PERSON> og slip felterne for at bestemme den rækkefølge, de skal vises i tilmeldingsformularen.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Standardfelt", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Slet", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "<PERSON><PERSON><PERSON><PERSON>, der vises under feltnavnet på tilmeldingsformularen.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for bopæl kan indstilles i {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON> svarmulighed", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Beskrivelse", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Feltnavn", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Angiv et feltnavn for alle sprog", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Feltindstillinger", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Ja-nej (afkrydsningsfelt)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Da<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "<PERSON><PERSON> svar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "<PERSON><PERSON>e valg (udval<PERSON> flere)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numerisk værdi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "<PERSON><PERSON>e valg (udvalgt én)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "<PERSON><PERSON> svar", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Fanen geografiske områder", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON><PERSON><PERSON><PERSON> felt", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Skal udfyldning af dette felt være betinget?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Brugerdefiner<PERSON> felter", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Slet", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "<PERSON>r du sikker på, at du vil slette denne svarmulighed for registreringsspørgsmål? <PERSON>e registreringer, som bestemte brugere har besvaret med denne mulighed, slettes permanent. <PERSON><PERSON> handling kan ikke fortrydes.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Er du sikker på, at du vil slette dette registreringsspørgsmål? <PERSON>e svar, som brugere har givet på dette spørgsmål, vil blive slettet permanent, og det vil ikke længere blive stillet i projekter eller forslag. <PERSON><PERSON> handling kan ikke fortrydes.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Påkrævet", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Gem feltet", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Feltet er gemt med succes", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "To kolonner", "app.containers.AdminPage.SettingsPage.addAreaButton": "Tilføj et geografisk område", "app.containers.AdminPage.SettingsPage.addTopicButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Dyr - f. eks. elefant eller kat", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Bruger - f.eks. bruger 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Vælg hvilke administratorer som skal modtage meddelelser om godkendelse af projekter. Mappeadministratorer godkender som standard alle projekter i deres mapper.", "app.containers.AdminPage.SettingsPage.approvalSave": "Gem", "app.containers.AdminPage.SettingsPage.approvalTitle": "<PERSON>ds<PERSON>linger for projektgodkendelse", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "<PERSON>r du sikker på, at du ønsker at slette dette område?", "app.containers.AdminPage.SettingsPage.areaTerm": "Betegnelse for et område (ental)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "område", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Slet", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "Betegnelse for flere områder (flertal)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "<PERSON><PERSON>r<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "<PERSON><PERSON><PERSON><PERSON> <PERSON>t ét sprog.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Ava<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Vis avatarer", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Vis profilbilleder af deltagere og antallet af deltagere til ikke-registrerede besøgende", "app.containers.AdminPage.SettingsPage.bannerHeader": "Tekst i overskriften", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Overskrift for ikke-registrerede besøgende", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Tekst til underoverskrifter for ikke-registrerede besøgende", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Tekst til underoverskrift", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Bannertekst", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "<PERSON><PERSON> for", "app.containers.AdminPage.SettingsPage.brandingDescription": "Tilføj dit logo, og indstil platformens farver.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Platforms branding", "app.containers.AdminPage.SettingsPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.chooseLayout": "Layout", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON><PERSON><PERSON><PERSON> farve", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON> farve", "app.containers.AdminPage.SettingsPage.color_text": "Tekstfarve", "app.containers.AdminPage.SettingsPage.colorsTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "<PERSON>r du sikker på, at du ønsker at slette dette tag?", "app.containers.AdminPage.SettingsPage.contentModeration": "Moderering af indhold", "app.containers.AdminPage.SettingsPage.ctaHeader": "<PERSON>nap<PERSON>", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Brugerdefineret sideoverskrift | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Brugerdefineret", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Knaptekst", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Link til knap", "app.containers.AdminPage.SettingsPage.defaultTopic": "Standard-tag", "app.containers.AdminPage.SettingsPage.delete": "Slet", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Slet", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "<PERSON>te vil slette tagget fra alle eksisterende indlæg. <PERSON><PERSON> ænd<PERSON> gælder for alle projekter.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Tilføj og slet mærker, som du gerne vil bruge på din platform til at kategorisere indlæg. Du kan tilføje mærker til specifikke projekter i {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Skrivebord", "app.containers.AdminPage.SettingsPage.editFormTitle": "<PERSON><PERSON> områ<PERSON>", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Rediger tag", "app.containers.AdminPage.SettingsPage.fieldDescription": "Områdebeskrivelse", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "<PERSON>ne beskrivelse er kun til internt samarbejde og vises ikke for brugerne.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Område navn", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Angiv et områdenavn for alle sprog", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Det navn, du udval<PERSON>r for hvert om<PERSON>åde, kan bruges som valgmulighed i registreringsfeltet og til at filtrere projekter på forsiden.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Gem tag", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Kategori navn", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Angiv et tag-navn for alle sprog", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Det navn, du udvalgtr for hvert tag, vil være synligt for platformens brugere", "app.containers.AdminPage.SettingsPage.fixedRatio": "Fast målforhold", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Denne bannertype fungerer bedst med billeder, der ikke bør be<PERSON>, f.eks. billeder med tekst, et logo eller specifikke elementer, der er afgørende for dine borgere. Dette banner erstattes med en fast boks i den primære farve, når brugerne er logget ind. Du kan indstille denne farve i de generelle indstillinger. Du kan finde flere oplysninger om den anbefalede billedbrug på vores {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "vidensbase", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "<PERSON><PERSON> bredde", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "<PERSON><PERSON> banner strækker sig over hele bred<PERSON> for at opnå en fantastisk visuel effekt. Bill<PERSON><PERSON> vil forsøge at dække så meget plads som muligt, hvilket gør, at det ikke altid er synligt på alle tidspunkter. Du kan kombinere dette banner med et overlay af en hvilken som helst farve. Du kan finde flere oplysninger om den anbefalede billedbrug på vores {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "vidensbase", "app.containers.AdminPage.SettingsPage.header": "Banner på hjemmesiden", "app.containers.AdminPage.SettingsPage.headerDescription": "Tilpas hjemmesidebanneret billede og tekst.", "app.containers.AdminPage.SettingsPage.header_bg": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.helmetDescription": "Side med administratorindstillinger", "app.containers.AdminPage.SettingsPage.helmetTitle": "Side med administratorindstillinger", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Til<PERSON><PERSON><PERSON> dit eget indhold til den tilpassede sektion nederst på hjemmesiden.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Hjemmesideoverskrift | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Billedoverlejringsfarve", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Opacitet af billedoverlejring", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Opdag upassende indhold", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Automatisk detektering af upassende indhold, der er lagt ud på platformen.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Mens denne funktion er aktiveret, vil input, forslag og kommentarer fra deltagerne automatisk blive gennemgået. Indlæg, der er markeret som potentielt upassende, bliver ikke blokeret, men bliver fremhævet til gennemsyn på siden {linkToActivityPage}.", "app.containers.AdminPage.SettingsPage.languages": "Sp<PERSON>", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Du kan udvalgt flere sprog, som du ønsker at tilbyde din platform til brugerne på. Du skal oprette indhold til hvert enkelt udvalgte sprog.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Aktivitet", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Upload venligst et header-billede", "app.containers.AdminPage.SettingsPage.no_button": "Ingen knap", "app.containers.AdminPage.SettingsPage.organizationName": "Navn på by eller organisation", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "<PERSON><PERSON> et organisationsnavn eller en by for alle sprog.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Aktiver overlejring", "app.containers.AdminPage.SettingsPage.phone": "Telefon", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Konfiguration af platformen", "app.containers.AdminPage.SettingsPage.population": "Befolkning", "app.containers.AdminPage.SettingsPage.populationMinError": "Befolkningen skal være et positivt tal.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Det samlede antal indbyggere på dit område. Dette bruges til at beregne deltagelsesprocenten. Lad feltet være tomt, hvis det ikke er relevant.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Bandeords blokering", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Blokér input, forslag og kommentarer, der indeholder de mest indrapporterede stødende ord", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "<PERSON><PERSON> tekst vises på hjemmesiden over projekterne.", "app.containers.AdminPage.SettingsPage.projectsSettings": "projektindstillinger", "app.containers.AdminPage.SettingsPage.projects_header": "Projektets overskrift", "app.containers.AdminPage.SettingsPage.registrationFields": "Registreringsfelter", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Giv en kort beskrivelse toppen i din registreringsformular.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registrerings", "app.containers.AdminPage.SettingsPage.save": "Gem", "app.containers.AdminPage.SettingsPage.saveArea": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "<PERSON>get gik galt, prøv venligst igen senere.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Succes!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "<PERSON>e ændringer er blevet gemt.", "app.containers.AdminPage.SettingsPage.selectApprovers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "<PERSON><PERSON><PERSON><PERSON>, der skal vises til brugerne efter registrering.", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "<PERSON><PERSON><PERSON><PERSON>, der skal vises til brugerne efter registrering.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "<PERSON>nne ikke gemme. Prøv at ændre indstillingen igen.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Tilmeld dig\"", "app.containers.AdminPage.SettingsPage.signed_in": "Knap for registrerede besøgende", "app.containers.AdminPage.SettingsPage.signed_out": "Knap for ikke-registrerede besøgende", "app.containers.AdminPage.SettingsPage.signupFormText": "Hjælpetekst til registrerings", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Tilføj en kort beskrivelse toppen i tilmeldingsformularen.", "app.containers.AdminPage.SettingsPage.statuses": "Statusser", "app.containers.AdminPage.SettingsPage.step1": "Trin for e-mail og adgangskode", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Dette vises toppen på den første side i tilmeldingsformularen (navn, e-mail, adgangskode).", "app.containers.AdminPage.SettingsPage.step2": "Trin med spørgsmål til registrerings", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Dette vises toppen på den anden side af tilmeldingsformularen (yderligere registreringsfelter).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Definer de geografiske omr<PERSON><PERSON>, som du ø<PERSON><PERSON> at bruge til din platform, f.eks. kvar<PERSON>, bydele eller distrikter. Du kan tilknytte disse geografiske områder til projekter (kan filtreres på landingssiden) eller bede deltagerne om at udvalgt deres bopælsområde som et registreringsfelt for at oprette Smart Groups og definere adgangsrettigheder.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "<PERSON><PERSON><PERSON><PERSON>, h<PERSON>dan deltagere skal se din organisationsnavn, udval<PERSON> sprog på din platform og link til din websted.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Den angivne undertitel overskrider den maksimalt tilladte tegngrænse (90 tegn)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Angiv hvilke oplysninger man bliver bedt om at opgive, når man registrerer sig.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologi", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Indstillinger er opdateret med succes.", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "Branding", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Status for input", "app.containers.AdminPage.SettingsPage.tabPolicies": "<PERSON>iti<PERSON>", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Godkendelse af projekt", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Status for forslag", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registrerings", "app.containers.AdminPage.SettingsPage.tabSettings": "Generelt", "app.containers.AdminPage.SettingsPage.tabTopics2": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Definer hvilken geografisk enhed du ønsker at bruge til dine projekter (f.eks. k<PERSON>, distrikter, bydele osv.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geografiske områder", "app.containers.AdminPage.SettingsPage.titleBasic": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Den angivne titel overskrider den maksimalt tilladte tegngrænse (35 tegn)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Emner/tags", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Dette banner er især nyttigt med billeder, der ikke fungerer godt sammen med tekst fra titlen, undertitlen eller knappen. Disse elementer vil blive skubbet under banneret. Du kan finde flere oplysninger om den anbefalede billedanvendelse på vores {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "vidensbase", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON> <PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.urlError": "URL'en er ikke gyldig", "app.containers.AdminPage.SettingsPage.urlPatternError": "Indtast en gyldig URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Websted", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Du kan tilføje et link til din eget websted. Dette link vil blive brugt nederst på hjemmesiden.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "<PERSON><PERSON><PERSON><PERSON>, hvordan brugere uden et navn i deres profil skal vises på platformen. <PERSON><PERSON>, når du indstiller adgangsrettighederne for en fase til 'E-mailbekræftelse'. I alle tilfælde vil brugerne ved deltagelse kunne opdatere det profilnavn, vi autogenererer til dem.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Visning af brugernavn (kun for brugere med bekræftet e-mail)", "app.containers.AdminPage.SideBar.administrator": "Administrator", "app.containers.AdminPage.SideBar.communityPlatform": "Fællesskabsplatform", "app.containers.AdminPage.SideBar.community_monitor": "Borgerbarometer", "app.containers.AdminPage.SideBar.customerPortal": "Kundeportal", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "E-mails", "app.containers.AdminPage.SideBar.folderManager": "Mappeadministrator", "app.containers.AdminPage.SideBar.groups": "Grupper", "app.containers.AdminPage.SideBar.guide": "Vejledning", "app.containers.AdminPage.SideBar.inputManager": "Idéoverblik", "app.containers.AdminPage.SideBar.insights": "Indsigt", "app.containers.AdminPage.SideBar.inspirationHub": "Inspirationscenter", "app.containers.AdminPage.SideBar.knowledgeBase": "Vidensbase", "app.containers.AdminPage.SideBar.language": "Sp<PERSON>", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Sider og menu", "app.containers.AdminPage.SideBar.messaging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.moderation": "Aktivitet", "app.containers.AdminPage.SideBar.notifications": "Notifikationer", "app.containers.AdminPage.SideBar.processing": "Behandling", "app.containers.AdminPage.SideBar.projectManager": "Projektleder", "app.containers.AdminPage.SideBar.projects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.settings": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.signOut": "<PERSON><PERSON><PERSON> ud", "app.containers.AdminPage.SideBar.support": "Support", "app.containers.AdminPage.SideBar.toPlatform": "Til platformen", "app.containers.AdminPage.SideBar.tools": "Værktøj", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON> profil", "app.containers.AdminPage.SideBar.users": "Brugere", "app.containers.AdminPage.SideBar.workshops": "Workshops", "app.containers.AdminPage.Topics.addTopics": "Tilføj", "app.containers.AdminPage.Topics.browseTopics": "<PERSON><PERSON><PERSON><PERSON> mærker", "app.containers.AdminPage.Topics.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.confirmHeader": "<PERSON>r du sikker på, at du ønsker at slette dette projektmærke?", "app.containers.AdminPage.Topics.delete": "Slet", "app.containers.AdminPage.Topics.deleteTopicLabel": "Slet", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Dette tag vil ikke længere kunne tilføjes til nye indlæg i dette projekt.", "app.containers.AdminPage.Topics.inputForm": "Input formular", "app.containers.AdminPage.Topics.lastTopicWarning": "Der kræves mindst ét tag. Hvis du ikke ønsker at bruge mærker, kan de deaktiveres under fanen {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Du kan tilføje og slette de mærker, der kan tildeles indlæg i dette projekt.", "app.containers.AdminPage.Topics.remove": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.title": "Tilladte input-tags", "app.containers.AdminPage.Topics.topicManager": "Emner/tags", "app.containers.AdminPage.Topics.topicManagerInfo": "<PERSON><PERSON> du <PERSON> at tilføje yderligere projektmærker, kan du gøre det i {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Tilføj en ny gruppe", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Gruppens navn", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Angiv et gruppenavn", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Opret en manuel gruppe", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Hvilken type gruppe har du brug for?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/da/articles/7043801-brug-af-smarte-og-manuelle-brugergrupper", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Gem gruppe", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Opret en manuel gruppe", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Opret en smart gruppe", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Få mere at vide om grupper", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Du kan udvalgt brugere fra oversigten og tilføje dem til denne gruppe.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Du kan definere betingelser, og brugere, der opfylder betingelserne, tilføjes automatisk til denne gruppe.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "<PERSON> g<PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Smart gruppe", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Der er ingen i denne gruppe endnu", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "<PERSON><PERSON> til {allUsersLink} for man<PERSON>t at tilføje nogle brugere.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Ingen bruger(e) matcher din søgning", "app.containers.AdminPage.Users.GroupsPanel.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Eksporter alt", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Eksporter brugere i gruppen", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Eksport valgt", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Er du sikker på, at du vil slette denne gruppe?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Der opstod en fejl under tilføjelsen af brugere til grupperne, prøv igen.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Fjern fra gruppen", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Vil du slette udvalgte brugere fra denne gruppe?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Der opstod en fejl under sletning af brugere fra gruppen, prøv venligst igen.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Tilføj brugere til gruppen", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Tilføj", "app.containers.AdminPage.groups.permissions.add": "Tilføj", "app.containers.AdminPage.groups.permissions.addAnswer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addQuestion": "Tilføj demografiske spørgsmål", "app.containers.AdminPage.groups.permissions.answerChoices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.answerFormat": "Svarformat", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Der skal være mindst ét valg", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "<PERSON><PERSON> bruger modererer den mappe, der indeholder dette projekt. For at fjerne deres moderatorrettigheder til dette projekt kan du enten tilbagekalde deres mapperettigheder eller flytte projektet til en anden mappe.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Opret et nyt spørgsmål", "app.containers.AdminPage.groups.permissions.createAQuestion": "Opret et spørgsmål", "app.containers.AdminPage.groups.permissions.defaultField": "Standardfelt", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Slet", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Slet", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "<PERSON><PERSON> venligst en titel for alle valg", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Ja-nej (afkrydsningsfelt)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Da<PERSON>", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "<PERSON><PERSON> svar", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Multiple choice (vælg flere)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Numerisk værdi", "app.containers.AdminPage.groups.permissions.fieldType_select": "Multiple choice (vælg en)", "app.containers.AdminPage.groups.permissions.fieldType_text": "<PERSON><PERSON> svar", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Ændring af detaljerede tilladelser er ikke en del af din licens. Kontakt din GovSuccess Manager for at få mere at vide om det.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Er du sikker på, at du ønsker at fjerne denne gruppe fra projektet?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "<PERSON>d<PERSON><PERSON> en eller flere grupper", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {Ingen medlemmer} one {1 medlem} other {{count} medlemmer}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Udfyld venligst titlen på alle sprog", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "<PERSON>r du sikker?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Projektledere ikke fundet", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Der vises intet, fordi der ikke er nogen handlinger, som brugeren kan foretage i dette projekt.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Kun administratorer kan oprette et nyt spørgsmål.", "app.containers.AdminPage.groups.permissions.option1": "Mulighed 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "A<PERSON><PERSON><PERSON><PERSON> invitation", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Hvem kan kommentere dokumentet?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Hvem kan tilmelde sig et arrangement?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Hvem kan kommentere på input?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Hvem kan fremsætte bemærkninger til forslagene?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Hvem kan indsende et forslag?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Hvem kan stemme på input?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Hvem kan indsende input?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Hvem kan deltage i afstemningen?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Hvem kan deltage i undersøgelsen?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Hvem kan melde sig som frivillig?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Hvem kan stemme om forslag?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Hvem kan stemme?", "app.containers.AdminPage.groups.permissions.questionDescription": "Beskrivelse af spørgsmålet", "app.containers.AdminPage.groups.permissions.questionTitle": "Spørgsmålets titel", "app.containers.AdminPage.groups.permissions.save": "Gem", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "<PERSON>get gik galt, prøv venligst igen senere.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Succes!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "<PERSON>e ændringer er blevet gemt.", "app.containers.AdminPage.groups.permissions.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.selectValueError": "<PERSON>æ<PERSON>g venligst en svartype", "app.containers.AdminPage.new.createAProject": "Opret et projekt", "app.containers.AdminPage.new.fromScratch": "Fra bunden", "app.containers.AdminPage.phase.methodPicker.addOn1": "Tilføjelse", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "AI-dre<PERSON> in<PERSON>t", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> deltagerne med at få enighed og uenighed frem i lyset, én idé ad gangen.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Find fælles fodslag", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Indlejr en interaktiv PDF og indsaml kommentarer og feedback med Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Indsaml feedback på et dokument", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Integrer en tredjepartsundersøgelse", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "Ekstern undersøgelse", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Udnyt jeres brugeres kollektive intelligens. Inviter dem til at indsende og diskutere ideer og/eller give feedback i et offentligt forum.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Indsamle input og feedback i offentligheden", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Del information", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Mangler AI-drevet indsigt på platformen", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Mangler rapportering på platformen og datavisualisering og -behandling", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Link til platformens rapportbygger", "app.containers.AdminPage.phase.methodPicker.logic1": "Logik", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "<PERSON><PERSON> vifte af spørgsmålstyper", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "G<PERSON> <PERSON><PERSON>ne mulighed for at uploade ideer med en tids- og stemmebegrænsning.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON><PERSON>, and<PERSON><PERSON> eller initiativer", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "<PERSON><PERSON> a<PERSON>", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Lav et kort spørgeskema med flere valgmuligheder.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Giv information til brugerne, visualiser resultater fra andre faser og lav datarige rapporter.", "app.containers.AdminPage.phase.methodPicker.survey1": "Undersøgelse", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Forstå dine brugeres behov og tanker via en bred vifte af private spørgsmålstyper.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Unders<PERSON>gelsesmuligheder", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Opret en undersøgelse", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Bed brugerne om at melde sig frivilligt til aktiviteter og sager, el<PERSON> find deltagere til et panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Rek<PERSON><PERSON> deltagere eller frivillige", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Vælg en afstemningsmetode, og lad brugerne prioritere mellem et par forskellige muligheder.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Gennemfør en afstemning eller en prioriteringsøvelse", "app.containers.AdminPage.projects.all.all": "Alle", "app.containers.AdminPage.projects.all.createProjectFolder": "Ny mappe", "app.containers.AdminPage.projects.all.existingProjects": "Eksisterende projekter", "app.containers.AdminPage.projects.all.homepageWarning1": "Brug denne side til at indstille rækkefølgen af projekter i rullemenuen \"Alle projekter\" i navigationslinjen. Hvis du bruger widgetterne \"Publicerede projekter og mapper\" eller \"Projekter og mapper (ældre)\" på din hjemmeside, vil rækkefølgen af projekter i disse widgets også blive bestemt af den rækkefølge, du indstiller her.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "<PERSON><PERSON><PERSON><PERSON>, hvor du er projektleder, vises her.", "app.containers.AdminPage.projects.all.noProjects": "Ingen projekter fundet.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Kun administratorer kan oprette projektmapper.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projekter og mapper", "app.containers.AdminPage.projects.all.publishedTab": "Offentliggjort", "app.containers.AdminPage.projects.all.searchProjects": "Søg i projekter", "app.containers.AdminPage.projects.all.yourProjects": "<PERSON><PERSON> proje<PERSON>", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI-analyse", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Nøjagtighed: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "I stedet for at opsummere kan du stille relevante spørgsmål til dine data. Denne funktion er ikke inkluderet i din nuværende plan. Tal med jeres administrator for at låse den op.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Stil et spørgsmål", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "<PERSON><PERSON> indsigt omfatter følgende spørgsmål:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Slet spørgsmål", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "<PERSON>r du sikker på, at du vil slette dette spørgsm<PERSON>l?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Slet resumé", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Er du sikker på, at du vil slette disse resuméer?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "<PERSON>e tekstresuméer vil blive vist her, men du har i øjeblikket ikke nogen endnu.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Klik på knappen Auto-resumé ovenfor for at komme i gang.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "valgte input", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "At stille spørgsmål om færre input fører til en højere nøjagtighed. Reducer det aktuelle inputvalg ved at bruge tags, søgning eller demografiske filtre.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "S<PERSON>ørgsm<PERSON><PERSON> til", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Spørgsmål til alle inputs", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Vurder kvaliteten af denne indsigt", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Gendan filtre", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Opsummering", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Resumé for", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Sammen<PERSON>tning for alle input", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Tak for din feedback", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "Den kunstige intelligens kan ikke behandle så mange input på én gang. Del dem op i mindre grupper.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Du kan opsummere maksimalt 30 input ad gangen med din nuværende plan. Tal med jeres GovSuccess Manager (<PERSON><PERSON><PERSON>) eller interne administrator for at låse mere op.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Vores platform giver dig mulighed for at udforske de centrale temaer, opsummere data og undersøge forskellige perspektiver. Hvis du er på udkig efter specifikke svar eller indsigter, kan du overveje at bruge funktionen \"Stil et spørgsmål\" til at dykke dybere ned i resuméet.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "Selvom det er sjældent, kan AI'en lejlighedsvis generere information, som ikke var eksplicit til stede i det oprindelige datasæt.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Hallucinationer:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "AI'en kan fremhæve visse temaer eller ideer mere end andre, hvilket potentielt kan skævvride den overordnede fortolkning.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Overdrivelse:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Vores system er optimeret til at håndtere 20-200 veldefinerede input for at opnå de mest præcise resultater. Når mængden af data stiger ud over dette interval, kan oversigten blive mere overordnet og generaliseret. Det betyder ikke, at AI'en bliver \"mindre præcis\", men snarere at den vil fokusere på bredere tendenser og mønstre. For at få en mere nuanceret indsigt anbefaler vi at bruge (auto)-tagging-funktionen til at segmentere større datasæt i mindre, mere håndterbare delmængder.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Datamængde og -nøjagtighed:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "<PERSON><PERSON> an<PERSON><PERSON><PERSON> at bruge AI-genererede resuméer som et udgangspunkt for at forstå store datasæt, men ikke som det endelige ord.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Sådan arbejder du med AI", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Tilføj valgte input til tag", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "<PERSON>ne funktion er ikke inkluderet i din nuværende plan. Tal med jeres administrator for at låse den op.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Alle input", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Alle input", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "Alle tags", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON>, jeg gør det", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Vil du automatisk tildele input til dit tag?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Der er <b>forskellige metoder</b> til automatisk at tildele inputs til tags.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Brug <b>knappen auto-tag</b> til at starte din foretrukne metode.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Klik på et tag for at tildele det til det aktuelt valgte input.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "<PERSON><PERSON>, automatisk mærkning", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Auto-tag", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Auto-tags udledes automatisk af computeren. Du kan til enhver tid ændre eller fjerne dem.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Auto-tag", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Inputs, der allerede er forbundet med disse tags, vil ikke blive klassificeret igen.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Klassificeringen er udelukkende baseret på navnet på tagget. Vælg relevante søgeord for at få de bedste resultater.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tags: <PERSON><PERSON>æ<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Du opretter tags og tildeler manuelt nogle få inputs som et eksempel, computeren tildeler resten", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tags: <PERSON><PERSON> et godt eksempel", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "<PERSON><PERSON><PERSON> til \"Tags: by label\", men med øget nøjagtighed, da du træner systemet med gode eksempler.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Du opretter tags, og computeren tildeler input.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tags: <PERSON><PERSON>æ<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "Det fungerer godt, når du har et foruddefineret sæt tags, eller når dit projekt har et begrænset omfang med hensyn til tags.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Opdag input med et betydeligt dislikes/likes-forhold", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Kontroversiel", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Slet tag", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "<PERSON>r du sikker på, at du ønsker at slette dette tag?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Vis ikke dette igen", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Rediger tag", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Tilføj navn", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Vælg maksimalt 9 tags, som du vil have inputtene fordelt på.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Klassificeringen er baseret på de input, der i øjeblikket er tildelt tags. Computeren vil forsøge at følge dit eksempel.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Tags: <PERSON><PERSON> et godt eksempel", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Du har ikke nogen brugerdefinerede tags endnu.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Computeren registrerer automatisk tags og tildeler dem til dine input.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Tags: <PERSON><PERSON><PERSON> automatiseret", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "<PERSON><PERSON><PERSON> godt, n<PERSON>r dine projekter dækker en bred vifte af tags. <PERSON>t godt sted at starte.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "<PERSON><PERSON><PERSON> vil du tagge?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Input uden tags", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Registrer sproget for hvert input", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Sp<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Ingen aktive filtre", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Brug tags til at underinddele og filtrere input for at lave mere præcise eller målrettede opsummeringer.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "And<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Tags: Platform-tags", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "<PERSON><PERSON><PERSON> de eks<PERSON>ende <PERSON>, som forfatteren valgte, da han skrev opslaget.", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "An<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Navn", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Gem", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "<PERSON><PERSON><PERSON><PERSON> alle", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Tildel en positiv eller negativ følelse til hvert input, a<PERSON>t af teksten", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Stemning", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Registrering af tags", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Brug aktuelle filtre", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Hvilke input vil du tagge?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Autotagging-opgave", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Kontroversiel", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Brugerdefineret", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Sluttede ved", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "For eksempel", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "I gang", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "Med etiket", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Sp<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP-tag", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Ingen nyligt udførte AI-opgaver", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Platform-tag", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "<PERSON> kø", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Stemning", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "<PERSON><PERSON> på", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "L<PERSON>ked<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Opsummeringsopgave", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "<PERSON><PERSON><PERSON><PERSON> ved", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Alle", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "<PERSON><PERSON>for", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Fødselsår", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Engagement", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filtre", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "<PERSON>a", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Input", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "<PERSON><PERSON> kom<PERSON>r", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "<PERSON>tal reaktioner", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Antal stemmer", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Til", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Tilføj til analyse", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonymt input", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "For<PERSON>ttere efter alder", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "<PERSON><PERSON><PERSON><PERSON> efter bopæl", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Baggrundsjob", "app.containers.AdminPage.projects.project.analysis.comments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "Bop<PERSON>lskortet er for stort til at blive vist", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "<PERSON><PERSON><PERSON><PERSON> tomme svar", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.end": "Slut", "app.containers.AdminPage.projects.project.analysis.filter": "Vis kun input med denne værdi", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "<PERSON><PERSON><PERSON><PERSON> besvarelser uden svar", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Automatisk indsigt", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "<PERSON> findes {count} eks<PERSON><PERSON> på denne kombination.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Dislikes", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Udforsk", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "Falsk", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Input", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON>s", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "<PERSON><PERSON><PERSON> heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "<PERSON><PERSON><PERSON> in<PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Ikke en statistisk signifikant indsigt.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Auto Insights er ikke tilgængelige for projekter med mindre end 30 deltagere.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Tidliger<PERSON> heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Tid<PERSON>ger<PERSON> inds<PERSON>t", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistisk signifikant indsigt.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Opsummering", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Analyse-tags", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Se alle indsigter", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Se automatisk indsigt", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Input uden tags", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "En ugyldig shapefile blev uploadet og kan ikke vises.", "app.containers.AdminPage.projects.project.analysis.limit": "Grænse", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "<PERSON><PERSON>sp<PERSON>rg<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.manageInput": "Administrer input", "app.containers.AdminPage.projects.project.analysis.nextGraph": "<PERSON><PERSON><PERSON> graf", "app.containers.AdminPage.projects.project.analysis.noAnswer": "Intet svar", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "Der blev ikke givet noget svar.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Ingen shapefile uploadet.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Ingen input svarer til dine nuværende filtre", "app.containers.AdminPage.projects.project.analysis.previousGraph": "<PERSON><PERSON><PERSON> graf", "app.containers.AdminPage.projects.project.analysis.reactions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.removeFilter": "<PERSON><PERSON><PERSON> filter", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "<PERSON><PERSON><PERSON> filter", "app.containers.AdminPage.projects.project.analysis.search": "<PERSON><PERSON><PERSON> på", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* <PERSON><PERSON><PERSON><PERSON><PERSON> vises i GeoJSON-format her. Derfor vises styling i den originale fil muligvis ikke korrekt.", "app.containers.AdminPage.projects.project.analysis.start": "Start", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Supportartikel", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "{tenant<PERSON><PERSON>, select, Odense {https://matomo.org/} other {https://support.govocal.com/en/articles/8316692-ai-analysis}}", "app.containers.AdminPage.projects.project.analysis.unknown": "Ukendt", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "Se alle spørgsm<PERSON>l", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Se udvalgte spørgsmål", "app.containers.AdminPage.projects.project.analysis.votes": "Afstemninger", "app.containers.AdminPage.widgets.copied": "<PERSON><PERSON><PERSON> til udklipsholder", "app.containers.AdminPage.widgets.copyToClipboard": "<PERSON><PERSON><PERSON> denne kode", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Kopier HTML-koden", "app.containers.AdminPage.widgets.fieldAccentColor": "Accentfarve", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Widgets baggrundsfarve", "app.containers.AdminPage.widgets.fieldButtonText": "Knaptekst", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Tilmeld dig nu", "app.containers.AdminPage.widgets.fieldFont": "Skrifttype", "app.containers.AdminPage.widgets.fieldFontDescription": "Dette skal være et eksisterende skrifttypebetegnelse navn fra {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Skriftstørrelse (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Undertitel i overskriften", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Du kan være medbestemmende", "app.containers.AdminPage.widgets.fieldHeaderText": "Overskriftstitel", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Vores platform for deltagelse", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "<PERSON>tal in<PERSON>g", "app.containers.AdminPage.widgets.fieldProjects": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldRelativeLink": "Links til", "app.containers.AdminPage.widgets.fieldShowFooter": "Vis knap", "app.containers.AdminPage.widgets.fieldShowHeader": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldShowLogo": "Vis logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Baggrundsfarve på webstedet", "app.containers.AdminPage.widgets.fieldSort": "Sorteret efter", "app.containers.AdminPage.widgets.fieldTextColor": "Tekstfarve", "app.containers.AdminPage.widgets.fieldTopics": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldWidth": "Bredde", "app.containers.AdminPage.widgets.homepage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Du kan kopiere denne HTML-kode og indsætte den på den del af din websted, hvor du ø<PERSON> at tilføje din widget.", "app.containers.AdminPage.widgets.htmlCodeTitle": "HTML-kode for widget", "app.containers.AdminPage.widgets.previewTitle": "Eksempel", "app.containers.AdminPage.widgets.settingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortNewest": "Nyeste", "app.containers.AdminPage.widgets.sortPopular": "Populære", "app.containers.AdminPage.widgets.sortTrending": "Populære", "app.containers.AdminPage.widgets.subtitleWidgets": "Du kan oprette en widget, tilpasse den og tilføje den til din eget websted for at tiltrække deltagere til denne platform.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Dimensioner", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Overskrift og sidefod", "app.containers.AdminPage.widgets.titleInputSelection": "Valg af input", "app.containers.AdminPage.widgets.titleStyle": "Stil", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Gem", "app.containers.ContentBuilder.homepage.PageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.SaveError": "<PERSON><PERSON> gik galt, da jeg gemte hje<PERSON>n.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "To kolonner", "app.containers.ContentBuilder.homepage.bannerImage": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerSubtext": "Bannerets undertekst", "app.containers.ContentBuilder.homepage.bannerText": "Bannertekst", "app.containers.ContentBuilder.homepage.button": "Knap", "app.containers.ContentBuilder.homepage.chooseLayout": "Layout", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Tilpasning af andre indstillinger end billedet og teksten på startbanneret er ikke inkluderet i din nuværende licens. Kontakt Sø<PERSON> for at få mere at vide om det.", "app.containers.ContentBuilder.homepage.customized_button": "Brugerdefineret", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Knaptekst", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Link til knap", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Viser de 3 kommende begivenheder på din platform.", "app.containers.ContentBuilder.homepage.eventsDescription": "Viser de 3 kommende begivenheder på din platform.", "app.containers.ContentBuilder.homepage.fixedRatio": "Fast målforhold", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Denne bannertype fungerer bedst med billeder, der ikke bør be<PERSON>, f.eks. billeder med tekst, et logo eller specifikke elementer, der er afgørende for dine borgere. Dette banner erstattes med en fast boks i den primære farve, når brugerne er logget ind. Du kan indstille denne farve i de generelle indstillinger. Du kan finde flere oplysninger om den anbefalede billedbrug på vores {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "vidensbase", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "<PERSON>ld bredde banner", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "<PERSON><PERSON> banner strækker sig over hele bred<PERSON> for at opnå en god visuel effekt. Bill<PERSON>t vil forsøge at dække så meget plads som muligt, hvil<PERSON> gør, at det ikke altid er synligt. Du kan kombinere dette banner med et overlay af en hvilken som helst farve. Du kan finde flere oplysninger om den anbefalede billedbrug på vores {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "vidensbase", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Billedoverlejringsfarve", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Opacitet af billedoverlejring", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Ugyldig URL", "app.containers.ContentBuilder.homepage.no_button": "Ingen knap", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Ikke-registre<PERSON>e brugere", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Aktiver overlejring", "app.containers.ContentBuilder.homepage.projectsDescription": "For at konfigurere rækkefølgen, som dine projekter vises i, skal du ændre rækkefølgen på dem på {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Projektsiden", "app.containers.ContentBuilder.homepage.registeredUsersView": "<PERSON><PERSON><PERSON><PERSON> brugere", "app.containers.ContentBuilder.homepage.showAvatars": "Vis avatarer", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Vis profilbilleder af deltagere og antallet af deltagere til ikke-registrerede besøgende", "app.containers.ContentBuilder.homepage.sign_up_button": "<PERSON><PERSON><PERSON><PERSON> dig", "app.containers.ContentBuilder.homepage.signedInDescription": "Det er sådan, registrerede brugere ser banneret.", "app.containers.ContentBuilder.homepage.signedOutDescription": "Det er sådan, be<PERSON><PERSON><PERSON><PERSON>, der ikke er registreret på <PERSON>en, ser banneret.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Dette banner er især nyttigt med billeder, der ikke fungerer godt sammen med tekst fra titlen, undertitlen eller knappen. Disse elementer vil blive skubbet under banneret. Du kan finde flere oplysninger om den anbefalede billedanvendelse på vores {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "vidensbase", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON> <PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Indlejringshøjde (pixel)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "<PERSON><PERSON><PERSON><PERSON> hvor du ønsker, at dit indlejrede indhold skal vises på siden (i pixel).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "<PERSON><PERSON> beskrivelse af det indhold, du indlejrer", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Det er nyttigt at give disse oplysninger til brugere, der er afhængige af en skærmlæser eller anden hjælpeteknologi.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "T<PERSON><PERSON><PERSON><PERSON> din URL", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Den fulde URL-adresse for det websted, du ønsker at indlejre.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Vis indhold fra en ekstern hjemmeside på din side i en HTML iFrame. Bemærk, at ikke alle sider kan indlejres. <PERSON><PERSON> du har problemer med at indlejre en side, skal du spørge ejeren af siden, om den er konfigureret til at tillade indlejring.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Besøg vores supportside", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON>, dette indhold kunne ikke indlej<PERSON>. {visitLinkMessage} for at få mere at vide.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Indtast en gyldig webadresse, for eksempel https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Indlejr", "app.containers.admin.ContentBuilder.accordionMultiloc": "Ha<PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Åbnes som standard", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Tekst", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Dette er udvideligt harmonikaindhold. Du kan redigere og formatere det ved at bruge editoren i panelet til højre.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Titel", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Harmonika-titel", "app.containers.admin.ContentBuilder.buttonMultiloc": "Knap", "app.containers.admin.ContentBuilder.delete": "Slet", "app.containers.admin.ContentBuilder.error": "fejl", "app.containers.admin.ContentBuilder.errorMessage": "Der er en fejl i {locale}-indhold<PERSON>, ret venligst problemet for at kunne gemme dine ændringer", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Skjul deltagelseravatarer", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "<PERSON>te er en k<PERSON>, lø<PERSON><PERSON> under<PERSON>ø<PERSON>, der viser, hvordan du har det med ledelse og offentlige tjenester.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "<PERSON><PERSON><PERSON> spørgeskema<PERSON>", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> os med at betjene dig bedre", "app.containers.admin.ContentBuilder.homepage.default": "standard", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Opfordring til handling", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Beskrivelse", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "URL til primær knap", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "URL til sekundær knap", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> knaptekst", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Titel", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Banner på hjemmesiden", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Banner på hjemmesiden", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Billed- og tekstkort", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 kolonne", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Aktivér borgerforslag i afsnittet \"Forslag\" i administratorpanelet for at låse dem op på hjemmesiden.", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Borgerforslag", "app.containers.admin.ContentBuilder.imageMultiloc": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "<PERSON><PERSON> beskrivelse af billedet", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Det er vigtigt at tilføje \"alt-tekst\" til billeder for at gøre din platform tilgængelig for brugere, der bruger skærmlæsere.", "app.containers.admin.ContentBuilder.participationBox": "Boks til deltagelse", "app.containers.admin.ContentBuilder.textMultiloc": "Tekst", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 kolonner", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 kolonne", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 kolonner med henholdsvis 30 % og 60 % bredde", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 kolonner med henholdsvis 60 % og 30 % bredde", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 lige store kolonner", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Mest reagerede input", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "Der er ingen input til rådighed for dette projekt eller denne fase.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Antal input", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Vis mere ", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Titel", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Samlet input: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Komprimer lang tekst", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "Der er ingen input til rådighed for dette projekt eller denne fase.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "<PERSON><PERSON><PERSON><PERSON> fase", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Indhold", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Input", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Titel", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Registreringsgrad", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Deltagernes tidslinje", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Bemærk at deltagelsestallene måske ikke er helt nøjagtige, da nogle data indsamles i en ekstern undersøgelse, som vi ikke sporer.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Diagram", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Titel", "app.containers.admin.ReportBuilder.charts.noData": "Der er ingen data til rådighed for de filtre, du har valgt.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Trafik<PERSON>lder", "app.containers.admin.ReportBuilder.charts.users": "Brugere", "app.containers.admin.ReportBuilder.charts.usersByAge": "Brugere efter alder", "app.containers.admin.ReportBuilder.charts.usersByGender": "Brugere efter køn", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Besøgende tidslinje", "app.containers.admin.ReportBuilder.managerLabel1": "Projektleder", "app.containers.admin.ReportBuilder.periodLabel1": "Periode", "app.containers.admin.ReportBuilder.projectLabel1": "Projekt", "app.containers.admin.ReportBuilder.quarterReport1": "Rapport fra Borgerbarometer: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Start", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "<PERSON><PERSON><PERSON> en ekstra plads", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Bekræft", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "<PERSON>r du sikker på, at du ønsker at give 1 person projektled<PERSON><PERSON>tigheder?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Giv projektlederen rettigheder", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Du har nået grænsen for antallet af inkluderede pladser i dit abonnement, {noOfSeats} yderligere {noOfSeats, plural, one {plads} other {pladser}} vil blive tilføjet.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Tilføj status", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Standardstatusser kan ikke slettes.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Slet", "app.containers.admin.ideaStatuses.all.editButtonLabel": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Rediger status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Status, der i øjeblikket er tildelt deltagerinput, kan ikke slettes. Du kan fjerne/ændre status fra eksisterende input i fanen {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Denne status kan ikke slettes eller flyttes.", "app.containers.admin.ideaStatuses.all.manage": "Administrere", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Konfiguration af brugerdefinerede inputstatusser er ikke inkluderet i din nuværende plan. Tal med din Government Success Manager eller administrator for at låse det op.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Administrer den status, der kan tildeles deltagerinput i et projekt. Statussen er offentligt synlig og hjælper med at holde deltagerne informeret.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Administrer den status, der kan tildeles forslag i et projekt. Statussen er offentligt synlig og hjælper med at holde deltagerne informeret.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Rediger statusser for forslag", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "<PERSON>gt til implementering eller næste trin", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Godkendt", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Officiel feedback givet", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.category": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.categoryDescription": "Væ<PERSON><PERSON> den kategori, der bedst repræsenterer din status. <PERSON>te valg hjælper vores analyseværktøj med mere nøjagtig behandling og analyse af indlæg.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "<PERSON>er ikke nogen af de andre muligheder", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "And<PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "Status beskrivelse", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Angiv en statusbeskrivelse for alle sprog", "app.containers.admin.ideaStatuses.form.fieldTitle": "Status navn", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Angiv et statusnavn for alle sprog", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Succesfuldt implementeret", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Implementeret", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Forslaget er ikke støtteberettiget", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "<PERSON><PERSON><PERSON> s<PERSON>", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Ikke kvalificeret eller ikke valgt til at komme videre", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "<PERSON><PERSON><PERSON> valgt", "app.containers.admin.ideaStatuses.form.saveStatus": "Gem status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "<PERSON>gt til implementering eller næste trin", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "I gang", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Set - men endnu ikke behandlet", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Set", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Administrer input og deres statusser.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Input manager | Borgerinddragelsesplatformen for {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "<PERSON><PERSON> <PERSON>, til<PERSON><PERSON><PERSON> mæ<PERSON>, og flyt input fra et projekt til et andet", "app.containers.admin.ideas.all.inputManagerPageTitle": "Idéoverblik", "app.containers.admin.ideas.all.tabOverview": "Oversigt", "app.containers.admin.import.importInputs": "Import af input", "app.containers.admin.import.importNoLongerAvailable3": "<PERSON>ne funktion er ikke længere tilgængelig her. For at importere input til en idéfase skal du gå til fasen og vælge \"Import\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 ekstra administratorplads} other {# yderligere administratorpladser}} og {managerSeats, plural, one {1 ekstra administratorplads} other {# yderligere projektlederpladser}} vil blive tilfø<PERSON> over g<PERSON><PERSON><PERSON><PERSON>.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {<PERSON> tilføjes 1 ekstra administratorplads over gr<PERSON><PERSON><PERSON>} other {# yderligere administratorpladser vil blive tilføjet over gr<PERSON>nse<PERSON>}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {Der tilføjes 1 ekstra projektlederplads over græ<PERSON><PERSON>} other {# yderligere projektlederpladser vil blive tilføjet over grænse<PERSON>}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Bekræft og send invitationer ud", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Bekræftelse af virkningen på brugen af sæder", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Du har nået grænsen for antallet af ledige pladser i dit abonnement.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Administratorer og projektledere for dette projekt", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Kun administratorer og samarbejdspartnere", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Kun platformadministratorer, mappeadministratorer og projektledere kan foretage denne handling", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Alle", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "<PERSON><PERSON>, også ikke registrerede brugere, kan deltage.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "<PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Brugere i bestemte brugergrupper kan deltage. Du kan administrere brugergrupper under fan<PERSON> \"Brugere\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Hvem kan se dette projekt?", "app.containers.phaseConfig.enableSimilarInputDetection": "Aktivér registrering af lignende input", "app.containers.phaseConfig.similarInputDetectionTitle": "Registrering af lignende input", "app.containers.phaseConfig.similarInputDetectionTooltip": "Vis deltagerne lignende input, mens de skriver, for at hjælpe med at undgå dubletter.", "app.containers.phaseConfig.similarityThresholdBody": "<PERSON><PERSON><PERSON><PERSON>v<PERSON>rdi for lighed (krop)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "<PERSON><PERSON> s<PERSON>, hvor ens to beskrivelser skal være for at blive markeret som ens. Brug en værdi mellem 0 (streng) og 1 (le<PERSON><PERSON>g). <PERSON><PERSON> værdier giver færre, men mere præcise match.", "app.containers.phaseConfig.similarityThresholdTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rdi for lighed (titel)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "<PERSON><PERSON> s<PERSON>, hvor ens to titler skal være for at blive markeret som ens. Brug en værdi mellem 0 (streng) og 1 (lempelig). <PERSON><PERSON> værdier giver færre, men mere præcise matches.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Denne funktion er tilgængelig som en del af et tilbud om tidlig adgang indtil 30. juni 2025. <PERSON><PERSON> du gerne vil fortsætte med at bruge den efter denne dato, bedes du kontakte S<PERSON><PERSON> for at diskutere aktiveringsmuligheder.", "app.containers.survey.sentiment.noAnswers2": "Ingen svar på nuværende tidspunkt.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {ingen kommentarer} one {1 kommentar} other {# kommentarer}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Deltagere er brugere eller besøgende, der har deltaget i et projekt, postet eller interageret med et forslag eller deltaget i arrangementer.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Deltagelsesprocent", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Procentdel af besøgende, der bliver brugere.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Deltagere i alt", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automatiserede kampagner", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automatiserede e-mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "Fra {quantity} kampagner", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Tilpassede kampagner", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Brugerdefinerede e-mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "E-mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Samlede antal sendte e-mails", "app.modules.commercial.analytics.admin.components.Events.completed": "<PERSON><PERSON><PERSON>gg<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "I alt tilføjede begivenheder", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Kommende", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Acceptere<PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Invitationer", "app.modules.commercial.analytics.admin.components.Invitations.pending": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "<PERSON><PERSON> antal sendte invitationer", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Gå til Input Manager", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Input", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Aktiv", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "<PERSON><PERSON><PERSON><PERSON>, der ikke er arkiveret, og som er synlige i tabellen \"Aktive\" på startsiden", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Arkiveret", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Projekter i udkast", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Afsluttet", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Alle arkiverede projekter og aktive projekter på tidslinjen, der er afsluttet, tælles med her", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Projekter i alt", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Antal<PERSON> af projekter, der er synlige på platformen", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "<PERSON><PERSON> registreringer", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Registreringsgrad", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Procentdel af besøgende, der bliver registrerede brugere.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Registreringer i alt", "app.modules.commercial.analytics.admin.components.Tab": "Besøgende", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Sidste 30 dage:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Sidste 7 dage:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Sidevisninger pr. be<PERSON>øg", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "<PERSON><PERSON><PERSON><PERSON> varighed", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Besøgende", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Visitors\" er antallet af unikke besøgen<PERSON>. Hvis en person besøger platformen flere gange, tælles vedkommende kun med én gang.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Visits\" er antallet af sessioner. Hvis en person har besøgt platformen flere gange, tælles hvert besøg med.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "I går:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Sp<PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Procentdel af besøgende", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "klik her", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "For at se den fulde liste over hen<PERSON><PERSON>, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Besøgende", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Trafik<PERSON>lder", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.totalParticipants": "Deltagere i alt", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Der er endnu ingen besøgsdata.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Vi har ændret den må<PERSON>, vi inds<PERSON>ler og viser besøgsdata på. Det betyder, at besøgsdataene er mere nøjagtige, og at flere typer data er tilgængelige, samtidig med at de stadig overholder GDPR. Mens de data, der bruges til tidslinjen for besøgende, g<PERSON><PERSON> længere tilbage, begyndte vi først at indsamle data til \"Besøgsvarighed\", \"Sidevisninger pr. besøg\" og de andre grafer i november 2024, så før det, er der ingen data tilgængelige. Hvis du vælger data før november 2024, skal du derfor være opmærksom på, at nogle grafer kan være tomme eller se mærkelige ud.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "E-mail-leverancer over tid", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "<PERSON><PERSON><PERSON> over tid", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "<PERSON><PERSON><PERSON><PERSON> over tid", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Da<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistisk", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Samlet statistik", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Besøg og besøgende over tid", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "I alt i perioden", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Sp<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Di<PERSON>te adgang", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Procentdel af besøg", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Hen<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Henvisende websteder", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Søgemaskiner", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Sociale netværk", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO-om<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Kilde til trafik", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Websteder", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Du kan fjerne dette indholdsflag ved at udvalgt dette element og klikke på knappen Fjern toppen. Det vil derefter blive vist igen i fanerne Set eller Ikke set", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Uhensigtsmæssigt indhold registreres automatisk.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Der er ingen indlæg, der er rapporteret til gennemgang af fællesskabet eller markeret for upassende indhold af vores system til behandling af naturligt sprog", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Fjern {numberOfItems, plural, one {indholdsadvarsel} other {#indholdsadvarsler}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Rapporteret som upassende af en platformbruger.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "<PERSON><PERSON><PERSON> om indhold", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Rapportbygger", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "<PERSON><PERSON>, der vises på din navigationslinje", "app.modules.navbar.admin.containers.addProject": "Tilføj projekt til navbar", "app.modules.navbar.admin.containers.createCustomPageButton": "Opret brugerdefineret side", "app.modules.navbar.admin.containers.deletePageConfirmation": "Er du sikker på, at du ønsker at slette denne side? Dette kan ikke fortrydes. Du kan også fjerne siden fra navigationslinjen, hvis du ikke er klar til at slette den endnu.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Du kan kun tilføje op til 5 elementer til navigationslinjen", "app.modules.navbar.admin.containers.pageHeader": "Sider og menu", "app.modules.navbar.admin.containers.pageSubtitle": "Din navigationslinje kan vise op til fem sider ud over siderne Startside og Projekter. Du kan omdøbe menupunkter, omarrangere og tilføje nye sider med dit eget indhold.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widgets", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Brug ☰-ikonet nedenfor til at trække AI-indhold ind i din rapport.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Der er ingen tilgængelige AI-indsigter. Du kan oprette dem i dit projekt.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Gå til projektet", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "<PERSON><PERSON><PERSON><PERSON> fase", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "<PERSON><PERSON><PERSON> op for AI-analyse", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Træk AI-genererede indsigter ind i din rapport", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Rapporter hurtigere med AI", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Rapportering med AI er ikke inkluderet i dit nuværende abonnement. Tal med din Government Success Manager for at låse op for denne funktion.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Dette er ikke inkluderet i din nuværende plan. Kontakt din Government Success Manager eller administrator for at låse det op.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Gruppér efter registreringsfelt", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Gruppér efter survey spørgsmål", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Gruppetilstand", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "<PERSON><PERSON><PERSON><PERSON><PERSON> under<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> efter registreringsfelter (køn, placering, alder osv.) eller andre under<PERSON><PERSON><PERSON>spørgsm<PERSON>.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "Ingen", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Registreringsfelt", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Survey fase", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Spørgsmål til undersøgelsen", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Er du sikker på, at du vil slette dette?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Slet", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "<PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Indsend din kommentar", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Gem", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "<PERSON><PERSON><PERSON><PERSON> din kommentar her", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Klik på knapperne nedenfor for at følge eller stoppe med at følge. Antallet af projekter er vist i parentes.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "I dit område", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Præferencer for 'Følg'", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Der er i øjeblikket ingen aktive projekter i forhold til dine indstillinger.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "<PERSON>ne widget viser projekter, der er knyttet til de \"områ<PERSON>\", som brugeren følger. Be<PERSON>ærk, at din platform måske bruger et andet navn for \"områder\" - se fanen \"Områder\" i platformens indstillinger. Hvis brugeren ikke følger nogen områder endnu, viser widgetten de tilgængelige områder, der kan følges. I dette tilfælde vil widgetten maksimalt vise 100 områder.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Ingen offentliggjorte projekter eller mapper tilgængelige", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Publiserede projekter og mapper", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Denne widget viser de projekter og mapper, der er udgivet i øjeblikket, og respekterer den rækkefølge, der er defineret på projektsiden. Denne visning er den samme som fanen \"aktiv\" i widgetten \"ældre\" projekter.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Ingen projekter eller mapper valgt", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "<PERSON><PERSON><PERSON><PERSON> projekter eller mapper", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Udvalgte projekter og mapper", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Med denne widget kan du vælge og bestemme, i hvilken rækkefølge du vil have projekter eller mapper vist til brugerne.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projekter", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "besøg vores supportcenter", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Du kan få flere oplysninger om anbefalede billedopløsninger ved at {supportPageLink}."}