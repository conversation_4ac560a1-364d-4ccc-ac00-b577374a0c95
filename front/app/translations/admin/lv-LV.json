{"UI.FormComponents.required": "nepieciešams", "app.Admin.ManagementFeed.action": "Rīcība", "app.Admin.ManagementFeed.after": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.before": "Pirms", "app.Admin.ManagementFeed.changed": "Modificēts", "app.Admin.ManagementFeed.created": "Izveidots", "app.Admin.ManagementFeed.date": "Datums", "app.Admin.ManagementFeed.deleted": "Dzēsts", "app.Admin.ManagementFeed.folder": "Mapes", "app.Admin.ManagementFeed.idea": "I<PERSON>ja", "app.Admin.ManagementFeed.in": "projektā {project}", "app.Admin.ManagementFeed.item": "Prece", "app.Admin.ManagementFeed.key": "Galvenais", "app.Admin.ManagementFeed.managementFeedNudge": "Piekļuve pārvaldības kanālam nav iekļauta jūsu pašreizējā licencē. Sazinieties ar savu GovSuc<PERSON> men<PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON> par to.", "app.Admin.ManagementFeed.noActivityFound": "Nav atrasta nekāda darb<PERSON>ba", "app.Admin.ManagementFeed.phase": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.project": "Projekts", "app.Admin.ManagementFeed.projectReviewApproved": "Projekts apstiprināts", "app.Admin.ManagementFeed.projectReviewRequested": "Pieprasītā projekta pārskatīšana", "app.Admin.ManagementFeed.title": "Vadības plū<PERSON>", "app.Admin.ManagementFeed.user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.userPlaceholder": "Izvēlieties lietotāju", "app.Admin.ManagementFeed.value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.warning": "Eksperimentālā funkcija: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 30 dienās administratoru vai vadītāju veikto darbību minimālais saraksts. Nav iekļautas visas darbības.", "app.Admin.Moderation.managementFeed": "Vadības plū<PERSON>", "app.Admin.Moderation.participationFeed": "<PERSON><PERSON><PERSON> pl<PERSON>", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Vai esat pārlie<PERSON>āts?", "app.components.Admin.Campaigns.clicked": "Noklikšķiniet uz", "app.components.Admin.Campaigns.deleteCampaignButton": "Kampaņ<PERSON>", "app.components.Admin.Campaigns.deliveryStatus_accepted": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Atbalsots", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Noklikšķiniet uz", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip2": "<PERSON><PERSON>, cik saņēmēju ir noklikšķinājuši uz e-pasta saiti. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, ka dažas drošības sistēmas var automātiski sekot līdzi saitēm, lai tā<PERSON> s<PERSON>, kā rezultātā var rasties viltus klikšķi.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_failed": "Neveiksmīgs", "app.components.Admin.Campaigns.deliveryStatus_opened": "Atv<PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_openedTooltip": "<PERSON><PERSON>, cik saņēmēju ir atvēruši e-pastu. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, ka dažas dro<PERSON> sist<PERSON> (piemēram, Microsoft Defender) var iepriekš ielādēt saturu sken<PERSON>, kas var izraisīt viltus atvēr<PERSON>nu.", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Projekts", "app.components.Admin.Campaigns.from": "No", "app.components.Admin.Campaigns.manageButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.opened": "Atv<PERSON><PERSON>", "app.components.Admin.Campaigns.project": "Projekts", "app.components.Admin.Campaigns.recipientsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.reply_to": "Atbilde-To", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "Statistika", "app.components.Admin.Campaigns.subject": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.to": "Uz", "app.components.Admin.ImageCropper.cropFinalSentence": "<PERSON><PERSON><PERSON>t arī: {link}.", "app.components.Admin.ImageCropper.cropSentenceMobileCrop": "Lai galvenais saturs vien<PERSON><PERSON>r b<PERSON><PERSON>, turiet to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> līnij<PERSON>.", "app.components.Admin.ImageCropper.cropSentenceMobileRatio": "3:1 mobilajā ierīcē (attēlota tikai zona starp punktētām līnijām)", "app.components.Admin.ImageCropper.cropSentenceOne": "Attēls tiek apgriezts automātiski:", "app.components.Admin.ImageCropper.cropSentenceTwo": "{aspect} uz darb<PERSON> (parādīts visā platumā)", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.infoLinkText": "ieteicamā attie<PERSON>", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Galvenes teksts reģistrētajiem lietotājiem", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Brīdinājums: izv<PERSON><PERSON><PERSON><PERSON><PERSON>i krāsai nav pietiekami liela kontrasta. Tā rezultātā teksts var būt grūti salasāms. Lai optimizētu <PERSON>, izv<PERSON><PERSON><PERSON> tumšāku krāsu.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "<PERSON><PERSON><PERSON> na<PERSON> j<PERSON>", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON>a <PERSON><PERSON> iespēja ir iespē<PERSON><PERSON>, navigācijas joslā tiks pievienota saite uz visiem projekta notikumiem.", "app.components.AdminPage.SettingsPage.eventsSection": "<PERSON><PERSON><PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Sāku<PERSON>lapas pielāgojamā sadaļa", "app.components.AnonymousPostingToggle.userAnonymity": "Lietot<PERSON><PERSON> an<PERSON>", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Lietotāji varēs slēpt savu identitāti no citiem lietotājiem, projektu vadītājiem un administratoriem. Šos ieguldījumus joprojām var moderēt.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "<PERSON><PERSON> anon<PERSON>", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Lietotāji joprojām var izvēlēties piedalīties ar savu īsto vārdu, ta<PERSON><PERSON>, ja viņi vēlēsies, viņiem būs iespēja iesniegt informāciju anonīmi. Visiem lietotājiem joprojām būs jāatbilst piekļuves tiesību cilnē noteiktajām prasībām, lai viņu ieguldījumi tiktu pieņemti. Lietotāja profila dati nebūs pieejami līdzdalības datu eksportā.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Uzziniet vairāk par lietotāja anonimitāti mūsu vietnē {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "<PERSON><PERSON><PERSON> raksts", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Kad tiks pievienotas papildu s<PERSON>, j<PERSON><PERSON> rēķins tiks palielin<PERSON>. Sazinieties ar savu GovS<PERSON><PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "<PERSON>ld<PERSON>, ka aizpildījāt aptauju! Esiet laipni aicināti to darīt arī nākamajā ceturksnī.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Lejupielādēt kā pdf", "app.components.FormSync.downloadExcelTemplate": "Lejupielādēt Excel veidni", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel veidnēs netiks i<PERSON><PERSON><PERSON><PERSON> rang<PERSON> j<PERSON>, matrica<PERSON> j<PERSON>, failu aug<PERSON>upielā<PERSON> jautājumi un kartēšanas ievades jautājumi (Drop Pin, Draw Route, Draw Area, ESRI failu augšupielāde), jo pa<PERSON><PERSON> tie netiek atbalstīti masveida import<PERSON>.", "app.components.ProjectTemplatePreview.close": "Aizvērt", "app.components.ProjectTemplatePreview.createProject": "Izveidot projektu", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Izveidojiet projektu, pamatojoties uz veidni ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Atgriezties", "app.components.ProjectTemplatePreview.goBackTo": "Atgriezties atpakaļ uz {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Go Vocal eksperts", "app.components.ProjectTemplatePreview.infoboxLine1": "Vai vēlaties izmantot šo veidni savam līdzdalības projektam?", "app.components.ProjectTemplatePreview.infoboxLine2": "Sazinieties ar atbildīgo personu jūsu pilsētas administrācijā vai sazinieties ar {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Projekta mape", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "<PERSON>z<PERSON><PERSON><PERSON><PERSON>tais datums ir nederīgs. <PERSON><PERSON><PERSON><PERSON>, norādiet datumu šādā formātā: YYYY-MM-DD .", "app.components.ProjectTemplatePreview.projectNoStartDateError": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties projekta sākuma datumu", "app.components.ProjectTemplatePreview.projectStartDate": "Projekta sākuma datums", "app.components.ProjectTemplatePreview.projectTitle": "Projekta no<PERSON><PERSON>ms", "app.components.ProjectTemplatePreview.projectTitleError": "<PERSON><PERSON><PERSON><PERSON>, ierakstiet projekta nosaukumu", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "<PERSON><PERSON><PERSON><PERSON>, ierakstiet projekta nosaukumu visās valod<PERSON>s", "app.components.ProjectTemplatePreview.projectsOverviewPage": "projektu pārskata lapa", "app.components.ProjectTemplatePreview.responseError": "<PERSON><PERSON>, kaut kas aizgāja greizi.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> ve<PERSON>", "app.components.ProjectTemplatePreview.successMessage": "Projekts ir veiksmīgi izveidots!", "app.components.ProjectTemplatePreview.typeProjectName": "Ierakstiet projekta nosaukumu", "app.components.ProjectTemplatePreview.useTemplate": "Izmantojiet <PERSON> ve<PERSON>", "app.components.SeatInfo.additionalSeats": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.components.SeatInfo.additionalSeatsToolTip": "<PERSON><PERSON> ir norādīts papildu vietu skaits, kas iegād<PERSON>ts papildus \"Iekļautajām vietām\".", "app.components.SeatInfo.adminSeats": "Administratora vietas", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} iekļauti administratora sēdekļi", "app.components.SeatInfo.adminSeatsTooltip1": "Administratori ir atbild<PERSON>gi par platformu, un viņiem ir ties<PERSON><PERSON> p<PERSON> visas mapes un projektus. <PERSON> uzzin<PERSON>tu vairāk par dažādām lomām, varat {visitHelpCenter} .", "app.components.SeatInfo.currentAdminSeatsTitle": "Pašreizējās administratora vietas", "app.components.SeatInfo.currentManagerSeatsTitle": "Pašreiz<PERSON><PERSON><PERSON> vadī<PERSON>ja vietas", "app.components.SeatInfo.includedAdminToolTip": "Tas parāda gada līgumā iekļauto pieejamo vietu skaitu <PERSON>.", "app.components.SeatInfo.includedManagerToolTip": "Tas parāda gada līgumā iekļauto pieejamo vietu skaitu vad<PERSON>.", "app.components.SeatInfo.includedSeats": "Iekļ<PERSON><PERSON>", "app.components.SeatInfo.managerSeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vietas", "app.components.SeatInfo.managerSeatsTooltip": "Mapju/projektu pārvald<PERSON>ki var pārvaldīt ne<PERSON>obežotu skaitu mapju/projektu. <PERSON> uzzinātu vairāk par dažādām lomām, varat {visitHelpCenter} .", "app.components.SeatInfo.managersIncludedText": "{managerSeats} i<PERSON><PERSON><PERSON><PERSON>", "app.components.SeatInfo.remainingSeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vietas", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "<PERSON><PERSON><PERSON><PERSON><PERSON> vietu skaits", "app.components.SeatInfo.totalSeatsTooltip": "<PERSON><PERSON><PERSON> r<PERSON>tā<PERSON>ā ir redzams jūsu plāna sēdvietu un papildus iegādāto sēdvietu kopējais skaits.", "app.components.SeatInfo.usedSeats": "<PERSON><PERSON><PERSON>", "app.components.SeatInfo.view": "<PERSON><PERSON><PERSON><PERSON>", "app.components.SeatInfo.visitHelpCenter": "apmeklējiet mūsu palīdzības centru", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "<PERSON><PERSON><PERSON> plānā ir {adminSeatsIncluded}. Kad esat izmantojis visas vietas, papildu vietas tiks pievienotas sadaļā \"Papildu vietas\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "<PERSON><PERSON><PERSON> plānā ir {managerSeatsIncluded}, kas ir piemērots mapju vadītājiem un projektu vadītājiem. Kad esat izmantojis visas vietas, papildu vietas tiks pievienotas sadaļā \"Papildu vietas\".", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON>", "app.components.UserSearch.searchUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai meklētu lietot<PERSON>...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternatīvs kļūdas ziņoju<PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Pēc noklusējuma lietotājiem tiks parādīts šāds kļūdas ziņojums:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "<PERSON><PERSON> ziņo<PERSON><PERSON> katrai valodai var p<PERSON><PERSON>, izman<PERSON><PERSON>t tālāk redzamo teksta lauku \"Alternatīvs kļūdas ziņojums\". Ja teksta lodziņu atstā<PERSON> tuk<PERSON>, tiks parād<PERSON>ts noklusējuma ziņoju<PERSON>.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "<PERSON><PERSON> informā<PERSON>, ja tie neatbilst dal<PERSON><PERSON> pras<PERSON>.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Saglabāt <PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Nav izvēlēts neviens jautājums. <PERSON><PERSON><PERSON><PERSON>, vispirms izvēlieties jautājumu.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "Nav atbildes", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} atbildes", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} l<PERSON><PERSON><PERSON>im", "app.components.admin.DatePhasePicker.Input.openEnded": "Atklāts", "app.components.admin.DatePhasePicker.Input.selectDate": "Izvēlieties datumu", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "<PERSON><PERSON><PERSON><PERSON> beigu datums", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON> datums", "app.components.admin.Graphs": "Ar pašreizējiem filtriem dati nav pieejami.", "app.components.admin.Graphs.noDataShort": "Nav pieejami nekādi dati.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "<PERSON><PERSON><PERSON><PERSON> laika gaitā", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON> gait<PERSON>", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "<PERSON><PERSON><PERSON><PERSON> laika gaitā", "app.components.admin.InputManager.onePost": "1 ievades elements", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Bezsaistes atlases korekcija", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Bezsai<PERSON>s b<PERSON>", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "<PERSON><PERSON> opcija <PERSON>auj iek<PERSON><PERSON> datus par dalību, kas ieg<PERSON>ti no citiem avotiem, pie<PERSON><PERSON><PERSON>, no klātienes vai papīra formāta balsojumiem:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Tas vizuāli atšķirsies no digitālajiem balsojumiem.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "<PERSON><PERSON> ietekm<PERSON><PERSON> b<PERSON> rezultātus.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Tas netiks atspoguļots līdzdalības datu paneļ<PERSON>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Bezsaistes balsis par opciju projektā var iestatīt tikai vienu reizi, un tās ir kopīgas visiem projekta posmiem.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Vispirms ir jāievada kopējais bezsaistes dalībnieku s<PERSON>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON> dalībnieku skaits", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "<PERSON> aprēķinātu pareizus re<PERSON>, mums ir j<PERSON><PERSON>a <b>kop<PERSON><PERSON><PERSON> bezsaistes dalībnieku skaits šajā posmā</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "<PERSON><PERSON><PERSON><PERSON>, nor<PERSON><PERSON><PERSON> tikai to<PERSON>, kas piedal<PERSON><PERSON><PERSON><PERSON>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modificē {name}", "app.components.admin.PostManager.PostPreview.assignee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.cancelEdit": "Atcelt rediģējumu", "app.components.admin.PostManager.PostPreview.currentStatus": "Pašreizējais statuss", "app.components.admin.PostManager.PostPreview.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Vai esat p<PERSON>, ka vēlaties dzēst šos ievadītos datus? Šo darbību nevar atcelt.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst šos ievadītos datus? Dati tiks dzēsti no visiem projekta posmiem, un tos nevarēs at<PERSON>.", "app.components.admin.PostManager.PostPreview.edit": "Rediģēt", "app.components.admin.PostManager.PostPreview.noOne": "Nepiešķirts", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Cik reižu tas ir iekļauts citu dalībnieku līdzdalības budž<PERSON>s", "app.components.admin.PostManager.PostPreview.picks": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Reakciju skaits:", "app.components.admin.PostManager.PostPreview.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayer": "<PERSON>vienot <PERSON>", "app.components.admin.PostManager.addFeatureLayerInstruction": "Nokopējiet ArcGIS Online izvietotā elementu slāņa URL un ielīmējiet to tālāk norādītajā ievades laukā:", "app.components.admin.PostManager.addFeatureLayerTooltip": "<PERSON><PERSON><PERSON> <PERSON><PERSON> s<PERSON><PERSON> ka<PERSON>i", "app.components.admin.PostManager.addWebMap": "<PERSON><PERSON><PERSON> t<PERSON> karti", "app.components.admin.PostManager.addWebMapInstruction": "Nokopējiet savas tīmekļa kartes portāla ID no ArcGIS Online un ielīmējiet to turpmāk norādītajā ievades laukā:", "app.components.admin.PostManager.allPhases": "Visi posmi", "app.components.admin.PostManager.allProjects": "Visi projekti", "app.components.admin.PostManager.allStatuses": "Visi statusi", "app.components.admin.PostManager.allTopics": "Visi tagi", "app.components.admin.PostManager.anyAssignment": "Jebkurš administrators", "app.components.admin.PostManager.assignedTo": "Piešķirts {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Piešķirts man", "app.components.admin.PostManager.assignee": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.authenticationError": "Mēģinot iegūt <PERSON>, rad<PERSON><PERSON> autentifikācijas kļ<PERSON>. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet URL un vai jūsu Esri API atslēgai ir piekļuve šim slānim.", "app.components.admin.PostManager.automatedStatusTooltipText": "Šis statuss tiek at<PERSON>m<PERSON>, kad ir izpi<PERSON><PERSON><PERSON>.", "app.components.admin.PostManager.bodyTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel": "Atcelt", "app.components.admin.PostManager.cancel2": "Atcelt", "app.components.admin.PostManager.co-sponsors": "L<PERSON><PERSON>zfinansētā<PERSON>", "app.components.admin.PostManager.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "<PERSON><PERSON>, ka zaud<PERSON><PERSON>t visus datus, kas saist<PERSON>ti ar <PERSON>iem ievades datiem, <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON>, reak<PERSON><PERSON> un balsojumus. <PERSON>o darbību nevar atcelt.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Vai esat pā<PERSON>, ka vēlaties dzēst šos ievades datus?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "J<PERSON><PERSON> mēģināt šo ideju izņemt no fāzes, kurā tā ir saņēmusi balsis. <PERSON><PERSON> to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, š<PERSON><PERSON> balsis tiks zaudētas. Vai esat pārliecināts, ka vēlaties izņemt šo ideju no šī posma?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Ar <PERSON>o ideju sa<PERSON>t<PERSON>s balsis tiks zaud<PERSON>tas", "app.components.admin.PostManager.components.goToInputManager": "<PERSON>et uz ievades pā<PERSON>ldnieku", "app.components.admin.PostManager.components.goToProposalManager": "Pārejiet uz priekšlikumu pārvaldnieku", "app.components.admin.PostManager.contributionFormTitle": "Rediģēt ieguldījumu", "app.components.admin.PostManager.cost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.createInput": "Izveidot ievadi", "app.components.admin.PostManager.createInputsDescription": "<PERSON><PERSON><PERSON> ievades datu kopuma izveide no iepriekšējā projekta", "app.components.admin.PostManager.currentLat": "Ģeogrāfiskā platuma viduspunkts", "app.components.admin.PostManager.currentLng": "Ģeogrāfiskā garuma viduspunkts", "app.components.admin.PostManager.currentZoomLevel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "app.components.admin.PostManager.defaultEsriError": "Mēģinot ieg<PERSON>t <PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai ir izveidots tīkla savienojums un vai URL ir pareizs.", "app.components.admin.PostManager.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteAllSelectedInputs": "<PERSON><PERSON><PERSON><PERSON> {count} zi<PERSON><PERSON>", "app.components.admin.PostManager.deleteConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst šo s<PERSON>āni?", "app.components.admin.PostManager.dislikes": "Nepatīk", "app.components.admin.PostManager.edit": "Rediģēt", "app.components.admin.PostManager.editProjects": "Rediģēt projektus", "app.components.admin.PostManager.editStatuses": "Rediģēt statusus", "app.components.admin.PostManager.editTags": "Rediģēt tagus", "app.components.admin.PostManager.editedPostSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Datu importēšana no Esri ArcGIS Online ir papildfunkcija. Lai to atbloķētu, sazinieties ar savu GS menedžeri.", "app.components.admin.PostManager.esriSideError": "ArcGIS lietojumprogrammā ir notikusi k<PERSON>ūda. <PERSON><PERSON><PERSON><PERSON>, pagaidiet dažas minūtes un mēģiniet vēlreiz vēlāk.", "app.components.admin.PostManager.esriWebMap": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> karte", "app.components.admin.PostManager.exportAllInputs": "Eksportēt visas ziņas (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Eksportēt visus komentārus (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Eksportēt komentārus par šo projektu (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Eksportēt ziņas šajā projektā (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Eksportēt atlasītās <PERSON> (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Eksportēt komentārus par atlasītajām ziņām (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Eksportēt balsis pēc ievades (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Eksportēt balsis pēc lietotāja (.xslx)", "app.components.admin.PostManager.exports": "Eksports", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "<PERSON><PERSON><PERSON> datus drīkst augšupielādēt tikai kā GeoJSON slāņus vai importēt no ArcGIS Online. Lūdzu, noņemiet visus pašreizējos GeoJSON slāņ<PERSON>, ja vēlaties pievienot funkciju slāni.", "app.components.admin.PostManager.featureLayerTooltop": "ArcGIS Online elementa lapas labajā pusē var atrast elementa slāņa URL.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kā cilvēki redzēs jūsu vārdu", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Paskaidrojiet šo statusa maiņu", "app.components.admin.PostManager.fileUploadError": "Nav izdevies augšupielādēt vienu vai vairākus failus. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet faila izmēru un formātu un mēģiniet vēlreiz.", "app.components.admin.PostManager.formTitle": "Rediģēt ideju", "app.components.admin.PostManager.generalApiError2": "Mēģinot ieg<PERSON>t <PERSON> vien<PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai URL vai portāla ID ir pareizs un vai jums ir piekļuve šim vienumam.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Kartes datus drīkst augšupielādēt tikai kā GeoJSON slāņus vai importēt no ArcGIS Online. Ja vēlaties augšupielādēt GeoJSON slāni, <PERSON><PERSON><PERSON><PERSON>, noņemiet visus ArcGIS datus.", "app.components.admin.PostManager.goToDefaultMapView": "Dodieties uz noklusējuma kartes centru", "app.components.admin.PostManager.hiddenFieldsLink": "sl<PERSON><PERSON><PERSON> lauki", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Padoms: ja <PERSON><PERSON><PERSON>, pie<PERSON><PERSON><PERSON><PERSON> {hiddenFieldsLink}, la<PERSON>, kas ir atbild<PERSON>jis jū<PERSON> a<PERSON>.", "app.components.admin.PostManager.import2": "Importēt", "app.components.admin.PostManager.importError": "<PERSON>zv<PERSON><PERSON>ēto failu nav iespējams importēt, jo tas nav derīgs GeoJSON fails.", "app.components.admin.PostManager.importEsriFeatureLayer": "<PERSON><PERSON><PERSON> <PERSON>u s<PERSON>", "app.components.admin.PostManager.importEsriWebMap": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> ka<PERSON>", "app.components.admin.PostManager.importInputs": "<PERSON><PERSON><PERSON> ievades", "app.components.admin.PostManager.imported": "Importēts", "app.components.admin.PostManager.initiativeFormTitle": "Rediģēšanas iniciatīva", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} no {totalCount} {totalCount, plural, one {ievades ir} other {ievades ir}} importētas. Imports joprojām turpinās, lū<PERSON><PERSON>, pārbaud<PERSON> to vēl<PERSON>k.", "app.components.admin.PostManager.inputManagerHeader": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputs": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputsExportFileName": "ievade", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "<PERSON><PERSON><PERSON><PERSON><PERSON> tikai tos i<PERSON>, kuri<PERSON> jās<PERSON>z atgriezeniskā saite", "app.components.admin.PostManager.issueFormTitle": "Rediģēt j<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.latestFeedbackMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON> jau<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.layerAdded": "Veiksmī<PERSON>", "app.components.admin.PostManager.likes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "P<PERSON>rvietojo<PERSON> šo ievadi prom no tās pašreizējā projekta, tiks zaudēta informācija par tai piešķirtajām fāzēm. Vai vēlaties turpināt?", "app.components.admin.PostManager.mapData": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.multipleInputs": "{ideaCount} i<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.newFeedbackMode": "Uzrakstiet jaunu <PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.noFilteredResults": "<PERSON><PERSON><PERSON> atlas<PERSON>tie filtri nedeva nekādus rezultātus", "app.components.admin.PostManager.noInputs": "<PERSON><PERSON><PERSON> nav ievades datu", "app.components.admin.PostManager.noInputsDescription": "<PERSON><PERSON><PERSON> varat pievienot savu ieguldījumu vai sākt no kāda iepriekšējā līdzdalības projekta.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 ievades} one {1 ievades} other {# ievades}} tiks importētas no izvēlētā projekta un fāzes. Importēšana notiks fonā, un pēc tās pabeigšanas ievades tiks parādītas ievades pārvaldītājā.", "app.components.admin.PostManager.noOne": "Nepiešķirts", "app.components.admin.PostManager.noProject": "Nav projekta", "app.components.admin.PostManager.officialFeedbackModal.author": "Autors", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kā tiks parādīts jūsu vārds", "app.components.admin.PostManager.officialFeedbackModal.description": "Oficiālu atsauksmju sniegšana palīdz <PERSON>āt procesa pārredzamību un vairo uzticību platformai.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Autors ir <PERSON>", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>griezenisk<PERSON>", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Paskaidrojiet statusa maiņas iemeslu", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Pasta atsauksmes", "app.components.admin.PostManager.officialFeedbackModal.skip": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.title": "Paskaidrojiet savu lēmumu", "app.components.admin.PostManager.officialUpdateAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kā cilvēki redzēs jūsu vārdu", "app.components.admin.PostManager.officialUpdateBody": "Paskaidrojiet šo statusa maiņu", "app.components.admin.PostManager.offlinePicks": "Bezsaistes izvēle", "app.components.admin.PostManager.offlineVotes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.onlineVotes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.optionFormTitle": "Rediģēšanas iespēja", "app.components.admin.PostManager.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Tiešsaistes izvēle", "app.components.admin.PostManager.pbItemCountTooltip": "Cik reižu tas ir iekļauts citu dalībnieku līdzdalības budž<PERSON>s", "app.components.admin.PostManager.petitionFormTitle": "Rediģēt lūgumrakstu", "app.components.admin.PostManager.postedIn": "Publicēts {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Rediģēt projektu", "app.components.admin.PostManager.projectsTab": "Projekti", "app.components.admin.PostManager.projectsTabTooltipContent": "Lai pārvietotu ierakstus no viena projekta uz citu, i<PERSON><PERSON><PERSON>et funkciju vilkt un nomest. Ņemiet vērā, ka projektos ar laika grafiku jums ieraksti joprojām būs jāpievieno konkrētam posmam.", "app.components.admin.PostManager.proposalFormTitle": "Rediģēt priekšlikumu", "app.components.admin.PostManager.proposedBudgetTitle": "Ierosinā<PERSON>s b<PERSON>", "app.components.admin.PostManager.publication_date": "Publicēts", "app.components.admin.PostManager.questionFormTitle": "Rediģēt j<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "Atiestatīt filtrus", "app.components.admin.PostManager.resetInputFiltersDescription": "Atiestatiet filtrus, lai redzētu visus ievades datus.", "app.components.admin.PostManager.saved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.screeningTooltip": "Skrīnings nav iekļauts jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "<PERSON><PERSON><PERSON> fāzē skrīnings ir izslēgts. Dodieties uz fāzes iesta<PERSON>, lai to iespējotu.", "app.components.admin.PostManager.selectAPhase": "Izvēlieties fāzi", "app.components.admin.PostManager.selectAProject": "Izvēlieties projektu", "app.components.admin.PostManager.setAsDefaultMapView": "Saglabāt pašreizējo viduspunktu un tālummaiņas līmeni kā kartes noklusējuma iestatījumus", "app.components.admin.PostManager.startFromPastInputs": "Sākt no iepriekšējiem ievadītajiem datiem", "app.components.admin.PostManager.statusChangeGenericError": "<PERSON><PERSON>ū<PERSON>! <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz vēlāk vai sazinieties ar atbalsta dienestu.", "app.components.admin.PostManager.statusChangeSave": "Mainīt statusu", "app.components.admin.PostManager.statusesTab": "Statuss", "app.components.admin.PostManager.statusesTabTooltipContent": "<PERSON><PERSON><PERSON> i<PERSON> status<PERSON>, i<PERSON><PERSON><PERSON>t funkciju vilkt un nomest. Sākotnējais autors un citi autori saņems paziņojumu par statusa maiņu.", "app.components.admin.PostManager.submitApiError": "Veidlapas nosūtīšana bija problemātiska. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai nav k<PERSON>du, un mēģiniet vēlre<PERSON>.", "app.components.admin.PostManager.timelineTab": "Laika līnija", "app.components.admin.PostManager.timelineTabTooltipText": "Iekopēt ierakstus da<PERSON> projekta posmos, izmantojot funkciju vilkt un nomest.", "app.components.admin.PostManager.title": "Nosa<PERSON>ms", "app.components.admin.PostManager.topicsTab": "Tagi", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.view": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votesByInputExportFileName": "votes_by_input", "app.components.admin.PostManager.votesByUserExportFileName": "votes_by_user", "app.components.admin.PostManager.webMapAlreadyExists": "Vienlaikus var pievienot tikai vienu tīmekļa karti. Lai importētu citu karti, noņemiet pašreizējo.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Kartes datus drīkst augšupielādēt tikai kā GeoJSON slāņus vai importēt no ArcGIS Online. Lūdzu, noņemiet visus pašreizējos GeoJSON slāņ<PERSON>, ja vēlaties pievienot tīmekļa karti.", "app.components.admin.PostManager.webMapTooltip": "Tīmekļa kartes portāla ID var atrast ArcGIS Online elementu lapā, labajā pusē.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON><PERSON><PERSON> diena} one {Viena diena} other {# dienas}} pa kreisi", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "Atcelt", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "<PERSON><PERSON>, dzēst apsekojuma rezultātus", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "To nevar atcelt", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Aptaujas rezultātu d<PERSON>", "app.components.admin.ProjectEdit.survey.downloadResults2": "Aptaujas rezultātu le<PERSON>", "app.components.admin.ReportExportMenu.FileName.fromFilter": "no", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grupa", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projekts", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tags", "app.components.admin.ReportExportMenu.FileName.untilFilter": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ReportExportMenu.downloadPng": "Lejupielādēt kā PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Lejupielādēt kā SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Lejupielādēt Excel dokumentu", "app.components.admin.SlugInput.regexError": "<PERSON><PERSON><PERSON><PERSON><PERSON> var ietvert tikai parastos mazos burtus (a-z), c<PERSON><PERSON><PERSON> (0-9) un defises (-). Pirmā un pēdējā rakstu zīme nedrī<PERSON>t būt defise. Aizliegts izmantot divas defises pēc kārtas (--).", "app.components.admin.TerminologyConfig.saveButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.commonGroundInputManager.title": "Nosa<PERSON>ms", "app.components.admin.seatSetSuccess.admin": "Administrators", "app.components.admin.seatSetSuccess.allDone": "<PERSON><PERSON>", "app.components.admin.seatSetSuccess.close": "Aizvērt", "app.components.admin.seatSetSuccess.manager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.orderCompleted": "Pasūtījums izpildīts", "app.components.admin.seatSetSuccess.reflectedMessage": "Plāna izmaiņas tiks atspoguļotas nākamajā norēķinu ciklā.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} izvēlētajam(-iem) lietotājam(-iem) ir piešķirtas tiesības.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Vai esat p<PERSON>, ka vēlaties dzēst visus aptaujas rezultātus?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "<PERSON><PERSON> metode ir beta versijā. Mēs to pakāpenisk<PERSON>, lai apkopotu atsauksmes un uzlabotu pieredzi.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Atsauksmes par dokumentu vākšana ir piel<PERSON><PERSON>a funk<PERSON>, un tā nav iekļauta jūsu pašreizējā licencē. Sazinieties ar savu GovSuc<PERSON> p<PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> vair<PERSON> par to.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Ieguldījums", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Nepieciešams dienu skaits", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "<PERSON><PERSON>, lai sasniegtu minim<PERSON><PERSON> balsu skaitu", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Vairāk informācijas par to, kā iestrādāt saiti uz Google Forms, var atrast sadaļā {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "<PERSON><PERSON> at<PERSON> raksts", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "I<PERSON>ja", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Iniciat<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "<PERSON><PERSON> būtu j<PERSON> ievade?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Komentēt", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Šeit norādiet saiti uz savu Konveio dokumentu. Vairāk informācijas par Konveio iestatīšanu lasiet mūsu vietnē {supportArticleLink} .", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "<PERSON><PERSON><PERSON> raksts", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Tas nav iekļ<PERSON>s jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Nepieciešams maks<PERSON> b<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> balsu skaitam katrā variantā jābūt mazākam vai vienādam ar kopējo balsu skaitu.", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Nepiecieša<PERSON> maksim<PERSON> balsu s<PERSON>ts", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "<PERSON><PERSON><PERSON><PERSON><PERSON> bud<PERSON> nedrīkst pārs<PERSON>gt maks<PERSON><PERSON> b<PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Nepieciešams minimālai<PERSON> b<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "<PERSON><PERSON><PERSON><PERSON><PERSON> balsu skaits nedrīkst būt lielāks par maksim<PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Nepieciešams minimālais balsu skaits", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "<PERSON>r<PERSON><PERSON><PERSON> beigu datums", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "<PERSON>r<PERSON><PERSON><PERSON> sākuma datums", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Iespēja", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "<PERSON>evades pārvaldnieka cilne", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Pēc fā<PERSON> izveides konfigurējiet balsošanas opcijas cilnē Ievades pārvaldnieks.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Konfigurējiet balsošanas opcijas vietnē {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administratori un vadītāji", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b><PERSON><PERSON><PERSON><PERSON> anotē<PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} var piedalīties šajā posmā.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "Atcelt", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Komentārs:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON> zem<PERSON> fāze", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>zi", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, sv<PERSON><PERSON>t šo posmu", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Vai esat pā<PERSON>, ka vēlaties dzēst šo fāzi?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Visi ar šo posmu saistītie dati tiks dz<PERSON>sti. To nevar atcelt.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Dokumentu anotē<PERSON> fāze", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Ikviens", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON> posms", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Idejas posms", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "Platformas apsekojuma posmā", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Informācijas fāze", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Beigu datums nav norādīts", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "<PERSON><PERSON><PERSON><PERSON> fāze", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Priekšlikumu iesniegšanas posms", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Reaģēt:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Reģistrējies pasākumam:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Reģistrētie lietotāji", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b><PERSON><PERSON><PERSON><PERSON> ievades datus:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Aptauja:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b><PERSON><PERSON><PERSON><PERSON> veik<PERSON>na:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Lietotāji ar apstiprinātiem e-pastiem", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Brīv<PERSON><PERSON>ā<PERSON><PERSON>gais darbs:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Brīv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> darba posms", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Balsošanas posms", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Kas var piedalīties?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "<PERSON><PERSON><PERSON> neb<PERSON><PERSON>, kam<PERSON>r tos nepārskatīs un neapstiprinās administrators. <PERSON><PERSON> nevar rediģēt ievaddatus pēc tam, kad tie ir pārbaudīti vai uz tiem ir reaģēts.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Tikai administratori", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "<PERSON><PERSON><PERSON><PERSON>, kam ir sa<PERSON>, var mijiedarboties ar projekta projektu.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> projektu vadītājiem publicēt projektu.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Arhivēts", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Projekts", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Rediģēt aprakstu", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Ikviens", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Grupas", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Projektu var publicēt tikai administratori{inFolder, select, true { vai mapju p<PERSON>ldnieki} other {}} .", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 dalībnieks} other {{participantsCount} dal<PERSON><PERSON><PERSON><PERSON>}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>) dalībnieki.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Piezīme: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> anonīmas vai atklātas dalības atļaujas, lietot<PERSON><PERSON> var piedalīties vair<PERSON><PERSON> reizes, tā<PERSON><PERSON><PERSON><PERSON><PERSON> iegū<PERSON><PERSON> maldinošus vai nepilnīgus lietotāju datus.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON> <b>vidū nav:</b>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Piedalās:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Pasākuma reģistrētāji", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar Go <PERSON> metodēm", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Projekta recenzenti ir informēti.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publicēt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Publicēts - Aktīvs", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Publicēts - Pabeigts", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Atsvaidzināt projekta priekšskatījuma saiti", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Reģenerēt projekta priekšskatījuma saiti. Tādē<PERSON><PERSON><PERSON> iepriekšējā saite tiks anulēta.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Vecās saites vairs ne<PERSON>, ta<PERSON>u jebkur<PERSON> laikā varat izve<PERSON>t jaunu saiti.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Vai esat pārliecināts? Tas atslēgs pašreizējo saiti", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "Atcelt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "Jā, atsvaidziniet saiti", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> piepra<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Pirms projekta publicēšanas tas ir jāapst<PERSON>rina administratoram{inFolder, select, true { vai kādam no mapju pā<PERSON>ld<PERSON>kiem} other {}} . <PERSON> pie<PERSON> a<PERSON>, noklikšķiniet uz pogas zem<PERSON>k.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Iestatījumi", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Dalīties", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON><PERSON><PERSON><PERSON> saite", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Priv<PERSON><PERSON> sai<PERSON>u kopīgo<PERSON>na nav iekļauta jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Kopīgojiet šo projektu", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "<PERSON><PERSON> ir <PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projekts", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Priekšlikums", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, kas jā<PERSON><PERSON> vērā", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Nepieciešams minimālais balsu skaits", "app.components.app.containers.AdminPage.ProjectEdit.report": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Laika grafiks", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Satiksme", "app.components.formBuilder.cancelMethodChange1": "Atcelt", "app.components.formBuilder.changeMethodWarning1": "Metodes maiņa var izrai<PERSON><PERSON>t jeb<PERSON> ieva<PERSON> datu, kas rad<PERSON>ti vai <PERSON>, i<PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON>od<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.components.formBuilder.changingMethod1": "<PERSON><PERSON> mai<PERSON>a", "app.components.formBuilder.confirmMethodChange1": "Jā, turpiniet", "app.components.formBuilder.copySurveyModal.cancel": "Atcelt", "app.components.formBuilder.copySurveyModal.description": "<PERSON><PERSON> kopēs visus jautājumus un loģiku bez atbildēm.", "app.components.formBuilder.copySurveyModal.duplicate": "Dub<PERSON>āts", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "<PERSON><PERSON><PERSON> projektā nav atrasti atbilstoši posmi", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Nav izvē<PERSON><PERSON>ta neviena fāze. <PERSON><PERSON><PERSON><PERSON>, vispi<PERSON> izvēlieties fāzi.", "app.components.formBuilder.copySurveyModal.noProject": "Nav projekta", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Nav izvēlēts neviens projekts. <PERSON><PERSON><PERSON><PERSON>, vispirms izvēlieties projektu.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "<PERSON><PERSON><PERSON> jau esat saglabājis izmaiņas šajā aptaujā. Ja dublēsiet citu aptauju, izmaiņas tiks zaudētas.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Apsekojuma posms", "app.components.formBuilder.copySurveyModal.title": "<PERSON><PERSON>, kuru <PERSON>l<PERSON>", "app.components.formBuilder.editWarningModal.addOrReorder": "Jautā<PERSON><PERSON> vai pā<PERSON>", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "<PERSON><PERSON><PERSON> atbildes dati var būt <PERSON>", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Rediģēt tekstu", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>? Tas neietek<PERSON>ēs jūsu atbildes datus", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> datus, kas sa<PERSON>ti ar <PERSON> j<PERSON>.", "app.components.formBuilder.editWarningModal.deteleAQuestion": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "app.components.formBuilder.editWarningModal.exportYouResponses2": "eksportējiet savas atbildes.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Brīdinājums: <PERSON><PERSON><PERSON> varat uz visiem laikiem zaudēt atbildes datus. Pirms turpināt,", "app.components.formBuilder.editWarningModal.noCancel": "Nē, atcelt", "app.components.formBuilder.editWarningModal.title4": "Rediģēt tiešraides apsekojumu", "app.components.formBuilder.editWarningModal.yesContinue": "Jā, turpiniet", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "<PERSON><PERSON><PERSON> a<PERSON> piekļ<PERSON>īju<PERSON>", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "\"Demogrāfiskie lauki aptaujas veidlapā\" ir iespējota. Kad tiek parādīta aptaujas veidlapa, visi konfigurētie demogrāfiskie jautājumi tiks pievienoti jaunā lapā tieši pirms aptaujas beigām. <PERSON>os jautājumus var mainīt vietnē {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "piekļuves <PERSON> iestatījumi šajā posmā.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Aptaujas respondentiem nebūs jāreģistrējas vai jā<PERSON>, lai iesniegtu atbildes uz aptaujas jautājumiem, kā rezultātā atbildes var tikt iesniegtas divreiz.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "<PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšanās/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jūs <PERSON>, ka netiks vākti aptaujas respondentu demogrāfiskie dati, kas var ietekmēt jūsu datu analīzes iespējas.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "<PERSON><PERSON> aptauja ir iestatīta tā, lai cilnē <PERSON> ties<PERSON> tiktu atļauta piekļuve \"Jebkuram\".", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "<PERSON>a vēlaties to main<PERSON>t, varat to izdar<PERSON>t vietnē {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Reģistrēšanās/pier<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> posmā jūs aptaujas respondentiem uzdodat šādus demogrāfi<PERSON> j<PERSON>.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Lai racionalizētu demogrāfiskās informācijas vākšanu un nodrošinātu tās integrēšanu lietotāju datub<PERSON>z<PERSON>, iesakām visus demogrāfiskos jautājumus iekļaut tieši reģistrēšanās/pierakst<PERSON>šan<PERSON><PERSON> procesā. Lai to i<PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "<PERSON><PERSON><PERSON><PERSON><PERSON> lietotājus sekot jomām vai tēmām", "app.components.onboarding.followHelperText": "Tā<PERSON><PERSON><PERSON><PERSON><PERSON> tiek aktivizēts reģistrācijas procesa posms, kurā lietotāji varēs sekot tālāk norādītajām jomām vai tēmām.", "app.components.onboarding.followPreferences": "Sekojiet preferencēm", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} saskaņ<PERSON> ar plānu, {noOfAdditionalSeats} papildus", "app.components.seatsWithinPlan.seatsWithinPlanText": "Sēdvietas plānā", "app.containers.Admin.Campaigns.campaignFrom": "No:", "app.containers.Admin.Campaigns.campaignTo": "Kam:", "app.containers.Admin.Campaigns.customEmails": "Pielāgotie e-pasta ziņ<PERSON>jumi", "app.containers.Admin.Campaigns.customEmailsDescription": "Nosūtiet pielāgotus e-pasta ziņojumus un pārbaudiet statistiku.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet jums, šķiet, nav piekļuves e-pasta sadaļai.", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automatizēti e-pasta ziņojumi", "app.containers.Admin.Insights.tabReports": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.a11y_removeInvite": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.addToGroupLabel": "Pievie<PERSON><PERSON><PERSON> šīs personas konkrētām manuālo lietotā<PERSON> grupām", "app.containers.Admin.Invitations.adminLabel1": "Piešķirt i<PERSON><PERSON><PERSON>a <PERSON><PERSON>", "app.containers.Admin.Invitations.adminLabelTooltip": "Izvēloties šo <PERSON>, uzai<PERSON><PERSON>taj<PERSON>m personām būs pieejami visi platformas iestatījumi.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Nav neviena <PERSON>, kas at<PERSON>stu jū<PERSON> me<PERSON>", "app.containers.Admin.Invitations.deleteInvite": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst šo i<PERSON>ū<PERSON>u?", "app.containers.Admin.Invitations.deleteInviteTooltip": "At<PERSON><PERSON><PERSON> u<PERSON><PERSON><PERSON>, varēsiet to atkārtoti nosūtīt šai personai.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t un aizpildīt veidni", "app.containers.Admin.Invitations.downloadTemplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "app.containers.Admin.Invitations.email": "E-pasts", "app.containers.Admin.Invitations.emailListLabel": "<PERSON><PERSON><PERSON><PERSON> ievadiet to personu e-pasta adreses, kuras vēlaties uzaicināt. <PERSON><PERSON><PERSON> atdaliet ar komatu.", "app.containers.Admin.Invitations.exportInvites": "Eksportēt visus uzaicināju<PERSON>", "app.containers.Admin.Invitations.fileRequirements": "Svarīgi: lai uzaicinājumus nosūt<PERSON><PERSON>, no importa veidnes nedrīkst izņemt nevienu aili. Neizmantotās ailes atstā<PERSON>et tuk<PERSON>.", "app.containers.Admin.Invitations.filetypeError": "Nepareizs faila tips. Tiek atbalstīti tikai XLSX faili.", "app.containers.Admin.Invitations.groupsPlaceholder": "Nav atlasīta neviena grupa", "app.containers.Admin.Invitations.helmetDescription": "Uzaicināt lietotā<PERSON> u<PERSON>u", "app.containers.Admin.Invitations.helmetTitle": "Administ<PERSON><PERSON><PERSON> uzaicinājumu informācijas paneli", "app.containers.Admin.Invitations.importOptionsInfo": "<PERSON><PERSON><PERSON> i<PERSON>pējas tiks ņemtas vērā tikai tad, ja tās nav definētas Excel failā.\n      Vairāk informācijas vietnē {supportPageLink}.", "app.containers.Admin.Invitations.importTab": "E-pasta adrešu <PERSON>", "app.containers.Admin.Invitations.invitationExpirationWarning": "Ņemiet vērā, ka ielūgumu derīguma termiņš beidzas pēc 30 dienām. Pēc šī termiņa beigām tos joprojām varat nosūtīt atkārtoti.", "app.containers.Admin.Invitations.invitationOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.invitationSubtitle": "Uzaiciniet cilvēkus pievienoties platformai jebkurā brīdī. Viņi saņems neitrālu uzaicinājuma e-pastu ar jūsu logotipu, aicinot viņus reģistrēties platformā.", "app.containers.Admin.Invitations.invitePeople": "Ielūd<PERSON>t cilvēkus, izmantojot e-pastu", "app.containers.Admin.Invitations.inviteStatus": "Statuss", "app.containers.Admin.Invitations.inviteStatusAccepted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteTextLabel": "<PERSON><PERSON> <PERSON><PERSON>, i<PERSON><PERSON><PERSON>, kas tiks pievienota uzaicinājuma vēstulē.", "app.containers.Admin.Invitations.invitedSince": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Izvēlieties uzaicinājuma valodu", "app.containers.Admin.Invitations.moderatorLabel": "Piešķiriet šīm personām projekta vadības <PERSON>bas", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Izvēloties šo <PERSON>, uzai<PERSON><PERSON><PERSON><PERSON><PERSON>(-ām) personai(-ām) tiks piešķirtas projekta vadītāja tiesības izvēlētajam(-iem) projektam(-iem). Vairāk informācijas par projekta vadītāja tiesībām {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "š<PERSON>", "app.containers.Admin.Invitations.name": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.processing": "<PERSON><PERSON><PERSON>. Lūdzu, uzgaidiet...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Nav izvēlēts neviens projekts", "app.containers.Admin.Invitations.save": "Izsūtiet uzaicinājumus", "app.containers.Admin.Invitations.saveErrorMessage": "<PERSON>d<PERSON><PERSON> viena vai vair<PERSON> k<PERSON>, un uzaicinājumi netika iz<PERSON>tīti. <PERSON><PERSON><PERSON><PERSON>, izlabojiet turpmāk norādītās kļūdas un mēģiniet vēlreiz.", "app.containers.Admin.Invitations.saveSuccess": "Veiksmīgi!", "app.containers.Admin.Invitations.saveSuccessMessage": "<PERSON><PERSON><PERSON>ā<PERSON><PERSON> ir veik<PERSON>gi no<PERSON>.", "app.containers.Admin.Invitations.supportPage": "<PERSON><PERSON><PERSON> lapa", "app.containers.Admin.Invitations.supportPageLinkText": "A<PERSON>ek<PERSON><PERSON><PERSON><PERSON> at<PERSON><PERSON> lapu", "app.containers.Admin.Invitations.tabAllInvitations": "<PERSON><PERSON> uzaicinājumi", "app.containers.Admin.Invitations.tabInviteUsers": "Uzaiciniet lietotājus", "app.containers.Admin.Invitations.textTab": "Man<PERSON><PERSON>li ievadiet e-pasta adreses", "app.containers.Admin.Invitations.unknownError": "<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> ve<PERSON>u", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink}, ja vēlaties vairāk informācijas par visām atbalstītajām importa veidnes ailēm.", "app.containers.Admin.Moderation.all": "<PERSON><PERSON>", "app.containers.Admin.Moderation.belongsTo": "Pieder pie", "app.containers.Admin.Moderation.collapse": "s<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.comment": "Komentēt", "app.containers.Admin.Moderation.commentDeletionCancelButton": "Atcelt", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.confirmCommentDeletion": "Vai esat pā<PERSON>, ka vēlaties izdzēst šo komentāru? Tas ir neatgriezenisks un to nevar atcelt.", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "Datums", "app.containers.Admin.Moderation.deleteComment": "Dzēst komentāru", "app.containers.Admin.Moderation.goToComment": "<PERSON><PERSON><PERSON>rt šo komentāru jaunā cilnē", "app.containers.Admin.Moderation.goToPost": "<PERSON><PERSON><PERSON>rt šo iera<PERSON> jaunā cilnē", "app.containers.Admin.Moderation.goToProposal": "At<PERSON><PERSON>rt <PERSON> p<PERSON>š<PERSON>u jaunā cilnē", "app.containers.Admin.Moderation.markFlagsError": "<PERSON>eva<PERSON><PERSON><PERSON> at<PERSON>t vienumu(-us). Mēģiniet vēlreiz.", "app.containers.Admin.Moderation.markNotSeen": "Atzīmēt {selectedItemsCount, plural, one {# item} other {# items}} kā jaunu", "app.containers.Admin.Moderation.markSeen": "Atzīmēt {selectedItemsCount, plural, one {# item} other {# items}} kā a<PERSON>tu", "app.containers.Admin.Moderation.moderationsTooltip": "<PERSON><PERSON><PERSON> varat ā<PERSON> p<PERSON> visus jaunos iera<PERSON>tus, kas publicēti jūsu platformā, tostarp idejas un komentārus. Varat atzīmēt ierakstus kā \"apskatītus\", lai citi z<PERSON>, kas vēl jāapstr<PERSON>d<PERSON>.", "app.containers.Admin.Moderation.noUnviewedItems": "Nav jaunu vienumu", "app.containers.Admin.Moderation.noViewedItems": "Nav apskat<PERSON><PERSON> vienumu", "app.containers.Admin.Moderation.pageTitle1": "<PERSON><PERSON> j<PERSON>", "app.containers.Admin.Moderation.post": "Ieraksts", "app.containers.Admin.Moderation.profanityBlockerSetting": "Necenzētu vārdu bloķētājs", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Bloķējiet i<PERSON><PERSON><PERSON>, kuro<PERSON> vis<PERSON> sa<PERSON> a<PERSON>skar<PERSON>š<PERSON> vā<PERSON>.", "app.containers.Admin.Moderation.project": "Projekts", "app.containers.Admin.Moderation.read": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.removeFlagsError": "<PERSON>eva<PERSON><PERSON><PERSON> no<PERSON> brī<PERSON>(-us). Mēģiniet vēlreiz.", "app.containers.Admin.Moderation.rowsPerPage": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.settings": "Iestatījumi", "app.containers.Admin.Moderation.settingsSavingError": "Nevarēja saglabāt. Mēģiniet vēlreiz mainīt iestatījumu.", "app.containers.Admin.Moderation.show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.status": "Statuss", "app.containers.Admin.Moderation.successfulUpdateSettings": "Iestatījumi ve<PERSON>.", "app.containers.Admin.Moderation.type": "Tips", "app.containers.Admin.Moderation.unread": "Nav a<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "<PERSON>ī lapa sastāv no šādām sadaļām. Tās var aktvizēt/noņemt un rediģēt pēc vajadz<PERSON>.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "<PERSON><PERSON><PERSON><PERSON>u", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Nav red<PERSON>s lapā", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "<PERSON><PERSON><PERSON> lap<PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "<PERSON><PERSON><PERSON> failus (maks. 50 MB), kas būs pieejami leju<PERSON>lādei no lapas.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Apak<PERSON><PERSON><PERSON><PERSON> sad<PERSON>a", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Pievienot saturu pielāgojamā sadaļā lapas apakšā.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Rediģēt", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Notikumu sa<PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar projektiem saistītus notikumus.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "<PERSON><PERSON><PERSON><PERSON> lapas reklāmkaroga attēlu un tekstu.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Projektu saraksts", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Parā<PERSON><PERSON> projektus, pamatojoties uz lapas iestatījumiem. Varat priekšskatīt parādāmos projektus.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Pievienot saturu pielāgojamā sadaļā lapas augšpusē.", "app.containers.Admin.PagesAndMenu.addButton": "<PERSON><PERSON><PERSON> j<PERSON>", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Nosaukums navigācijas joslā", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Vai esat p<PERSON>, ka vēlaties dzēst šo lapu? Šo darbību nevar atsaukt.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Sniedziet nosaukumu visām valodām", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Citas pieejamās lapas", "app.containers.Admin.PagesAndMenu.components.savePage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lapu", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON><PERSON> ve<PERSON>gi sagla<PERSON>ta", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "<PERSON><PERSON><PERSON> (ne vairāk kā 50 MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Veiksmīgi", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Nebija iespējams saglabāt pie<PERSON>us", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "<PERSON><PERSON><PERSON> nedr<PERSON><PERSON><PERSON> b<PERSON><PERSON> par 50 Mb. Pievienotie faili tiks parādīti šīs lapas apak<PERSON>ā", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Sagla<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Pielikumi | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Saglabāt un iespējot pielikumus", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "<PERSON><PERSON><PERSON> sa<PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "<PERSON><PERSON><PERSON><PERSON>t saturu visās valod<PERSON>s", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Veiksmīgi", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Nebija iespējams saglabāt apakšējo informācijas sadaļu", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Saglabā<PERSON> a<PERSON><PERSON> sadaļa", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Apak<PERSON><PERSON><PERSON><PERSON> sad<PERSON>a", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Saglabāt un iespējot apakšējo informācijas sadaļu", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Saglabāt a<PERSON>š<PERSON>jo informācijas sadaļu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Pašreizē<PERSON><PERSON> licencē nav iekļauta pielāgotu lapu izveide. Sazinieties ar savu Gov<PERSON><PERSON><PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON> par to.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties vismaz vienu tagu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Veiksmīgi", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "<PERSON><PERSON><PERSON> birka<PERSON>(-ēm)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Projektu rādīšana pēc birkas vai apgabala nav daļa no jūsu pašreizējās licences. Sazinieties ar savu Gov<PERSON><PERSON><PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> vair<PERSON><PERSON> par to.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Rediģēt pielāgotu lapu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "<PERSON><PERSON><PERSON><PERSON> projekti", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kurus projektus un saistītos notikumus var parādīt lapā.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Lapa veiksmīgi izveidota", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON><PERSON> ve<PERSON>gi sagla<PERSON>ta", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapa saglabāta", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Virsraksts navigācijas j<PERSON>lā", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Izveidot pielāgotu lapu | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Izveidot pie<PERSON>ā<PERSON> lapu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Nav", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Lapas iestatījumi", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "<PERSON>g<PERSON><PERSON><PERSON><PERSON> lapu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Lū<PERSON><PERSON>, izvēlieties apgabalu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Izvē<PERSON>ē<PERSON>s a<PERSON>ga<PERSON>s", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>s", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "<PERSON><PERSON><PERSON><PERSON><PERSON> var ietvert tikai parastos mazos burtus (a-z), c<PERSON><PERSON><PERSON> (0-9) un defises (-). Pirmā un pēdējā rakstu zīme nedrī<PERSON>t būt defise. Aizliegts izmantot divas defises pēc kārtas (--).", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Nosa<PERSON>ms", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "<PERSON><PERSON><PERSON><PERSON><PERSON> virs<PERSON>u visā<PERSON> valod<PERSON>s", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Ievadiet nosaukumu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "<PERSON><PERSON><PERSON><PERSON> lapu", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "Poga", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Rediģēt pielāgotu lapu | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Lapas saturs", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Rediģēt", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "<PERSON><PERSON>a posma projekt<PERSON>, ja beigu datums ir tukšs un apraksts nav aizpildīts, projekta lapā netiks parādīta laika grafi<PERSON>.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Nav pieejamu projektu, pamatojoties uz jūsu {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "<PERSON><PERSON> projektam nav birkas vai apga<PERSON>a filtra, tā<PERSON><PERSON><PERSON> netiks rādīti nekādi projekti.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Projektu saraksts | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "lapas i<PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projektu saraksts", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "<PERSON><PERSON><PERSON>ā tiks parādīti š<PERSON>di projekti, pamatojoties uz jūsu {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "NOKLUSĒJUMS", "app.containers.Admin.PagesAndMenu.deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.editButton": "Rediģēt", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Veiksmīgi", "app.containers.Admin.PagesAndMenu.heroBannerError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.homeTitle": "Sā<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Nodrošiniet saturu vismaz vienā valodā", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Navigācijas joslā var pievienot tikai līdz 5 vienībām.", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Lapas un izvēlne | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Noņemt no navigācijas joslas", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Saglabāt un iespējot lielo rek<PERSON>u", "app.containers.Admin.PagesAndMenu.title": "Lapas un izvēlne", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Veiksmīgi", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoError": "Nebija iespējams saglabāt augšējo informācijas sadaļu", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Saglab<PERSON><PERSON> augšē<PERSON><PERSON> sada<PERSON>a", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> sadaļ<PERSON> | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Saglabāt un iespējot augšējo informācijas sadaļu", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Saglabā<PERSON> au<PERSON>š<PERSON> informācijas sadaļu", "app.containers.Admin.PagesAndMenu.viewButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Vecums", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "<PERSON>st<PERSON><PERSON><PERSON> lī<PERSON>a i<PERSON> rādīt<PERSON>ji", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "Turpmākajā sadaļā ir izklāst<PERSON>ti iekļauša<PERSON> rād<PERSON>, kuros ir uzsvērts mūsu progress ceļā uz iekļaujošākas un reprezentatīvākas līdzdalības platformas izveidi.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Augst<PERSON><PERSON><PERSON> līmeņa līdzdalības rādītāji", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "Turpmākajā sadaļā ir izklāstīti galvenie līdzdalības rādītāji par izvēlēto laika posmu, snied<PERSON>t pārskatu par iesaistīšanās tendencēm un darbības rādītājiem.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projekti", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "publicētie projekti", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Platformas ziņojums", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "<PERSON><PERSON><PERSON> projekti", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "Nākamajā sadaļā sniegts pārskats par publiski redzamiem projektiem, kas pārklājas ar izvēlēto laika periodu, šajos projektos visvairāk izmantotajām metodēm un metriku par kopējo līdzdalības apjomu.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Reģistrācijas grafiks", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Bloķētie lietotāji", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Bloķēto lietot<PERSON><PERSON> p<PERSON>.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Dzēst grupu", "app.containers.Admin.Users.GroupsHeader.editGroup": "Rediģēt grupu", "app.containers.Admin.Users.GroupsPanel.admins": "<PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.allUsers": "Reģistrētie lietotāji", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grupas", "app.containers.Admin.Users.GroupsPanel.managers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Piešķirtie priekš<PERSON>i", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Gūt pārskatu par visām platformā reģistrētajām personām un organizācijām. Pievienot atlasītus lietotājus <PERSON>ām grupām vai Viedajām grupām.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Neapstiprināts uzaicinājums", "app.containers.Admin.Users.admin": "Administrators", "app.containers.Admin.Users.assign": "Piešķirt", "app.containers.Admin.Users.assignedItems": "Piešķirtie priek<PERSON><PERSON>i {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Iegādājieties vienu papildu sēdvietu", "app.containers.Admin.Users.changeUserRights": "<PERSON><PERSON><PERSON> lie<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.confirm": "Apstipriniet", "app.containers.Admin.Users.confirmAdminQuestion": "Vai esat pā<PERSON>, ka vēlaties piešķirt platformas {name} <PERSON><PERSON>?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Vai esat pārl<PERSON>ts, ka vēlaties iestatīt {name} kā parastu lietotāju?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Vai esat pārliecināts, ka vēlaties iestatīt {name} kā parastu lietotāju? <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, ka viņ<PERSON> zaudē<PERSON> pārvaldnieka tiesības uz visiem projektiem un mapēm, kas viņam ir piešķirtas pēc apstiprin<PERSON>.", "app.containers.Admin.Users.deleteUser": "Dzēst lietotāju", "app.containers.Admin.Users.email": "E-pasts", "app.containers.Admin.Users.folder": "Mapes", "app.containers.Admin.Users.folderManager": "Mapju pārvaldnieks", "app.containers.Admin.Users.helmetDescription": "Lietotāju saraksts admin sada<PERSON>", "app.containers.Admin.Users.helmetTitle": "Administrēt lietotāju informācijas paneli", "app.containers.Admin.Users.inviteUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.joined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.lastActive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.containers.Admin.Users.name": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.noAssignedItems": "Nav piešķirto vienumu", "app.containers.Admin.Users.options": "<PERSON>es<PERSON>ējas", "app.containers.Admin.Users.permissionToBuy": "<PERSON> piešķirtu {name} <PERSON><PERSON>, ir jāpērk 1 papildu vieta.", "app.containers.Admin.Users.platformAdmin": "Platformas administrators", "app.containers.Admin.Users.projectManager": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "app.containers.Admin.Users.reachedLimitMessage": "<PERSON><PERSON><PERSON> plān<PERSON> ir sasniegts sēdvietu limits, tiks pievienota 1 papildu sēdvieta {name} .", "app.containers.Admin.Users.registeredUser": "Reģistrēts lietotājs", "app.containers.Admin.Users.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.removeModeratorFrom": "Lietotājs mitrina mapi, kurai pieder šis projekts. Tā vietā noņemiet uzdevumu no \"{folderTitle}\".", "app.containers.Admin.Users.role": "<PERSON><PERSON>", "app.containers.Admin.Users.seeProfile": "<PERSON><PERSON><PERSON><PERSON> profilu", "app.containers.Admin.Users.selectPublications": "Atlasiet projektus vai mapes", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Veiciet meklēšanu", "app.containers.Admin.Users.setAsAdmin": "Iestatīts kā administrators", "app.containers.Admin.Users.setAsNormalUser": "Iestatīts kā parasts lietotājs", "app.containers.Admin.Users.setAsProjectModerator": "Iestatīts kā projekta vadītājs", "app.containers.Admin.Users.setUserAsProjectModerator": "Piešķirt {name} kā projekta vadītāju", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.blockAction": "Bloķēt lietotāju", "app.containers.Admin.Users.userBlockModal.blockInfo1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> lietot<PERSON>ja saturs netiks dzēsts. Neaizmirstiet vajadzības gadījumā moderēt viņa saturu.", "app.containers.Admin.Users.userBlockModal.blocked": "Bloķēts", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "<PERSON>is lietotājs ir bloķēts kopš {from}. Aizliegums ir spēkā līdz {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Atcelt", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Vai esat pārl<PERSON>, ka vēlaties atbloķēt {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} ir bloķēts līdz {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 diena} other {{numberOfDays} dienas}}", "app.containers.Admin.Users.userBlockModal.header": "Bloķēt lietotāju", "app.containers.Admin.Users.userBlockModal.reasonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Par to tiks paziņots bloķētajam lietotājam.", "app.containers.Admin.Users.userBlockModal.subtitle1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lietotājs nevarēs pieteikties platformā {daysBlocked}. Ja vēlaties to main<PERSON><PERSON>, varat atbloķēt viņu no bloķēto lietotāju saraksta.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Atbloķēt", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, es vēlos atbloķēt šo lietotāju", "app.containers.Admin.Users.userDeletionConfirmation": "Pastā<PERSON><PERSON>gi dzēst šo lietotā<PERSON>?", "app.containers.Admin.Users.userDeletionFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>, notika <PERSON>, l<PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "app.containers.Admin.Users.userDeletionProposalVotes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tiks dzēsti arī visi šā lietotāja balsojumi par priekšlikumiem, par kuriem vēl ir iespējams balsot.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "Lie<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.youCantDeleteYourself": "<PERSON><PERSON> kontu jū<PERSON>, i<PERSON><PERSON><PERSON><PERSON> lietotāja <PERSON>a lapu.", "app.containers.Admin.Users.youCantUnadminYourself": "<PERSON><PERSON><PERSON> nevarat atteikties no administratora lomas.", "app.containers.Admin.communityMonitor.communityMonitorLabel": "<PERSON><PERSON><PERSON> uzraudz<PERSON>ā<PERSON>s", "app.containers.Admin.communityMonitor.healthScore": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "app.containers.Admin.communityMonitor.healthScoreDescription": "<PERSON><PERSON> rādītājs ir vidējais rādītājs no visiem noskaņojuma skalas j<PERSON>, uz kuriem dalī<PERSON>nieki atbildēja par izvēlēto periodu.", "app.containers.Admin.communityMonitor.lastQuarter": "p<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "app.containers.Admin.communityMonitor.liveMonitor": "Tiešraide", "app.containers.Admin.communityMonitor.noResults": "Par šo periodu rezultātu nav.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Nav aptaujas atbilžu", "app.containers.Admin.communityMonitor.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings": "Iestatījumi", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Tiek pieņemti iesniegumi Kopienas monitoringa apsekojumam.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Piek<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "<PERSON><PERSON><PERSON>, kad lietotāj<PERSON> reģistrē pasā<PERSON><PERSON> a<PERSON>, ies<PERSON><PERSON><PERSON> balsojumu vai atgriežas projekta lapā pēc aptaujas iesniegšanas.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "<PERSON><PERSON>nas monitora pārvaldnieki var piekļūt visiem Kopienas monitora iestatījumiem un datiem un pārvaldīt tos.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "<PERSON><PERSON><PERSON>u v<PERSON>", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>ji var rediģēt Community Monitor aptauju un atļaujas, skat<PERSON>t atbildes datus un veidot pārskatus.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "Noklusējuma frekvences vērtība ir 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Iznirstošo logu biežums (0 līdz 100)", "app.containers.Admin.communityMonitor.settings.management2": "Vadī<PERSON>", "app.containers.Admin.communityMonitor.settings.popup": "Iznirstošie logi", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Lietotājiem periodiski tiek parādīts uznir<PERSON> logs, kas aicina aizpild<PERSON>t Kopienas monitora aptauju. Varat <PERSON>, kas nosaka pro<PERSON><PERSON><PERSON><PERSON>, kuri izlases veidā redzēs uz<PERSON> logu, ja ir izpildīti turpmāk izklāstītie nosacījumi.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Iznirstošo logu iestatījumi", "app.containers.Admin.communityMonitor.settings.preview": "Priekšskatījums", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Lietotājs nav aizpildījis aptauju iepriekšējo 3 mēnešu laikā.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Lietotājs nav redzējis uznirstošo logu iepriekšējo 3 mēnešu laikā.", "app.containers.Admin.communityMonitor.settings.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.saved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.settings": "Iestatījumi", "app.containers.Admin.communityMonitor.settings.survey2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.surveySettings3": "Vispārīgie iestatījumi", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Pēc sāku<PERSON>lapas vai pielāgotas lapas ielā<PERSON>.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonimizēt visus lietotāja datus", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Visi aptaujas dati no lietotājiem tiks anonīmi pirms to reģistrēšanas.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Lietotāji<PERSON> j<PERSON> būs j<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON> prasības sadaļā \"Piekļuves tiesības\". Lietotāja profila dati nebūs pieejami apsekojuma datu eksportā.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "<PERSON><PERSON><PERSON> apstākļ<PERSON> lietotā<PERSON> var parādīties uznirstošais logs?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "<PERSON>s ir vad<PERSON><PERSON>?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>bilžu skaits", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI kopsavilkums", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "<PERSON><PERSON> funkcija nav iekļauta jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.containers.Admin.communityMonitor.upsell.healthScore": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "app.containers.Admin.communityMonitor.upsell.learnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "<PERSON><PERSON> gait<PERSON> ieg<PERSON> rezultā<PERSON>", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "Community Monitor palīdz jums būt soli <PERSON>, ne<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sekojot līdzi iedzīvotāju <PERSON>, apmierinātībai ar pakalpojumiem un kopienas dzīvei.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Iegū<PERSON><PERSON> skaidrus rezultātus, spē<PERSON><PERSON><PERSON> citātus un ceturkš<PERSON>a pārskatu, ko varat kopīgot ar kolēģiem vai ievēlētajām amatpersonām.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "<PERSON><PERSON><PERSON>, kas laika gaitā <PERSON>.", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Galvenie iedzīvot<PERSON><PERSON> cit<PERSON>, apkopoti pēc AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "<PERSON><PERSON><PERSON> pilsētas kontekstam pielāgoti jautāju<PERSON>", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas <PERSON><PERSON> pieņemti darbā pēc nejaušības principa", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Ceturkšņa PDF pārskati, kas ir gatavi kopīgošanai", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> par to, kā jūtas jūsu kopiena, pirms problēmas pieaug", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Count", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Darbvirsmas vai cits", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobilais", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Planšetdators", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> veidi", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Ierīces tips", "app.containers.Admin.earlyAccessLabel": "Agrīn<PERSON>", "app.containers.Admin.earlyAccessLabelExplanation": "<PERSON><PERSON> ir <PERSON>, kas pieejama agrīn<PERSON>s pie<PERSON> program<PERSON>.", "app.containers.Admin.emails.addCampaign": "Izveidot e-pastu", "app.containers.Admin.emails.addCampaignTitle": "Izveidot jaunu e-pastu", "app.containers.Admin.emails.allParticipantsInProject": "Visi projekta dalībnieki", "app.containers.Admin.emails.allUsers": "Reģistrētie lietotāji", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Automatizēti e-pasta ziņojumi tiek automātiski izsūtīti un tiek aktivizēti pēc lietotāja darbībām. Dažus no tiem varat izslēgt visiem platformas lietotājiem. Pārējos automātiskos e-pastus nevar izslēgt, jo tie ir nepieciešami jūsu platformas pareizai darbībai.", "app.containers.Admin.emails.automatedEmails": "Automatizēti e-pasti", "app.containers.Admin.emails.automatedEmailsDigest": "E-pasts tiks nosūtīts tikai tad, ja būs saturs.", "app.containers.Admin.emails.automatedEmailsRecipients": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kas saņems šo e-pastu", "app.containers.Admin.emails.automatedEmailsTriggers": "Notik<PERSON>, kas izraisa šo e-pastu", "app.containers.Admin.emails.changeRecipientsButton": "Mainiet saņēmējus", "app.containers.Admin.emails.clickOnButtonForExamples": "Noklikšķiniet uz pogas <PERSON>, lai apskatītu šī e-pasta piemērus mūsu atbalsta lapā.", "app.containers.Admin.emails.confirmSendHeader": "E-pasts visiem lietotājiem?", "app.containers.Admin.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.draft": "Melnraksts", "app.containers.Admin.emails.editButtonLabel": "Rediģēt", "app.containers.Admin.emails.editCampaignTitle": "Rediģēt kampa<PERSON>u", "app.containers.Admin.emails.editDisabledTooltip2": "Drīzumā gaidāms: <PERSON><PERSON><PERSON><PERSON><PERSON> šo e-pastu nav iespējams rediģēt.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Pogas teksts", "app.containers.Admin.emails.editRegion_intro_multiloc": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editRegion_subject_multiloc": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editRegion_title_multiloc": "Nosa<PERSON>ms", "app.containers.Admin.emails.emailCreated": "Veiksmīgi izveidots e-pasta projekts", "app.containers.Admin.emails.emailUpdated": "E-pasts ve<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.emptyCampaignsDescription": "Viegli sazinieties ar da<PERSON><PERSON><PERSON>, nos<PERSON><PERSON><PERSON> viņiem e-pasta vēstules. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ar ko sazin<PERSON>, un sekojiet līdzi savai iesaistei.", "app.containers.Admin.emails.emptyCampaignsHeader": "Nosūtiet savu pirmo e-pasta vēstuli", "app.containers.Admin.emails.failed": "Neveiksmīgi", "app.containers.Admin.emails.fieldBody": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBodyError": "Norādīt e-pasta ziņoju<PERSON> visās valodās", "app.containers.Admin.emails.fieldGroupContent": "E-pasta saturs", "app.containers.Admin.emails.fieldReplyTo": "Atbildes jānosūta uz š<PERSON> ad<PERSON>i", "app.containers.Admin.emails.fieldReplyToEmailError": "Norādīt e-pasta adresi pareizā formātā, piem<PERSON>ram, <EMAIL>.", "app.containers.Admin.emails.fieldReplyToError": "Norādīt e-pasta adresi", "app.containers.Admin.emails.fieldReplyToTooltip": "Varat i<PERSON>, kur sūtīt atbildes uz jūsu e-pasta ziņojumiem.", "app.containers.Admin.emails.fieldSender": "No", "app.containers.Admin.emails.fieldSenderError": "Norādīt e-pasta sūtītāju", "app.containers.Admin.emails.fieldSenderTooltip": "<PERSON><PERSON><PERSON>, ko saņēmēji redzēs kā e-pasta sūtītāju.", "app.containers.Admin.emails.fieldSubject": "E-pasta temats", "app.containers.Admin.emails.fieldSubjectError": "Norādīt e-pasta tematu visās valodās", "app.containers.Admin.emails.fieldSubjectTooltip": "<PERSON>s būs redzams e-pasta temata rindā un lietotāja iesūtnes pārskatā. <PERSON> jābūt skaidram un saistošam.", "app.containers.Admin.emails.fieldTo": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldToTooltip": "<PERSON><PERSON><PERSON> varat atlasīt lietot<PERSON> grup<PERSON>, kas saņems jūsu e-pastu.", "app.containers.Admin.emails.formSave": "Saglabāt kā melnrakstu", "app.containers.Admin.emails.formSaveAsDraft": "Saglab<PERSON>t kā projektu", "app.containers.Admin.emails.from": "No:", "app.containers.Admin.emails.groups": "Grupas", "app.containers.Admin.emails.helmetDescription": "Izsūtiet manuālus e-pasta ziņojumus lietotāju grupām un aktivizējiet automatizētas kampaņas.", "app.containers.Admin.emails.nameVariablesInfo2": "<PERSON><PERSON><PERSON> varat tie<PERSON>i sa<PERSON> ar <PERSON>, i<PERSON><PERSON><PERSON><PERSON> main<PERSON> lielum<PERSON> {firstName} {lastName}. <PERSON><PERSON><PERSON><PERSON>, \"<PERSON><PERSON><PERSON><PERSON> {firstName} {lastName}, ...\".", "app.containers.Admin.emails.previewSentConfirmation": "Uz jūsu e-pasta adresi ir nosūtīts e-pasts priekšskatīšanai", "app.containers.Admin.emails.previewTitle": "Priekšskatījums", "app.containers.Admin.emails.regionMultilocError": "<PERSON><PERSON><PERSON><PERSON>, nor<PERSON><PERSON>t vērtību visām valodām", "app.containers.Admin.emails.seeEmailHereText": "Tiklīdz šāda veida e-pasta vēstule tiks nosūtīta, to varē<PERSON>t pā<PERSON><PERSON> šeit.", "app.containers.Admin.emails.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sendNowButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag<PERSON>", "app.containers.Admin.emails.sendTestEmailButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> man testa e-pastu", "app.containers.Admin.emails.sendTestEmailTooltip2": "Noklikšķinot uz šīs <PERSON>, uz jūsu e-pasta adresi tiks nosūtīts testa e-pasts. <PERSON><PERSON> <PERSON>auj j<PERSON>, kā e-pasts izskatās \"reālajā dzīvē\".", "app.containers.Admin.emails.senderRecipients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un saņēmēji", "app.containers.Admin.emails.sending": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sentToUsers": "Tie ir e-pasta z<PERSON><PERSON>, kas nos<PERSON><PERSON><PERSON><PERSON> lie<PERSON>em", "app.containers.Admin.emails.subject": "Temats:", "app.containers.Admin.emails.supportButtonLabel": "<PERSON><PERSON><PERSON><PERSON> piem<PERSON>rus mūsu atbalsta lapā", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "Uz:", "app.containers.Admin.emails.toAllUsers": "Vai vēlaties nosūtīt šo e-pastu visiem reģistrētajiem lietotājiem?", "app.containers.Admin.emails.viewExample": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.ideas.import": "Importēt", "app.containers.Admin.inspirationHub.AllProjects": "Visi projekti", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "<PERSON><PERSON><PERSON> uzraudz<PERSON>ā<PERSON>s", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Dokumenta anotācija", "app.containers.Admin.inspirationHub.ExternalSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Country": "Valsts", "app.containers.Admin.inspirationHub.Filters.Method": "Metode", "app.containers.Admin.inspirationHub.Filters.Search": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.population": "Iedzīvotā<PERSON> s<PERSON>ts", "app.containers.Admin.inspirationHub.Highlighted": "Izcelts", "app.containers.Admin.inspirationHub.Ideation": "Ideju i<PERSON>strā<PERSON>", "app.containers.Admin.inspirationHub.Information": "Informācija", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>, lai skat<PERSON><PERSON> piespraustos projektus", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Valsts", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Šai valstij nav atrasts neviens piesprausts projekts. <PERSON><PERSON> vals<PERSON>, lai redzētu citu valstu piespraustos projektus.", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "<PERSON><PERSON><PERSON>, lai red<PERSON> vairāk piesprausto projektu", "app.containers.Admin.inspirationHub.Poll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Atklāts", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "<PERSON><PERSON><PERSON> v<PERSON>...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Fāze {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, lai jūsu projekts tiktu iekļ<PERSON> i<PERSON>ves<PERSON> centrā, sazinieties ar savu GovSuc<PERSON> vadī<PERSON>āju.", "app.containers.Admin.inspirationHub.Proposals": "Priekšlikumi", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (vispirms zemākā vērtība)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (pir<PERSON>is augs<PERSON>)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "<PERSON><PERSON><PERSON><PERSON> datums (vispirms vecākais)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "<PERSON><PERSON><PERSON><PERSON> datums (jau<PERSON><PERSON><PERSON><PERSON> pirma<PERSON>)", "app.containers.Admin.inspirationHub.Survey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "<PERSON>āko projektu saraksts visā pasaulē.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Aprunājieties ar citiem praktizētājiem un mācieties no viņiem.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Atlasiet pēc metodes, pilsētas lieluma un valsts.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "<PERSON>espējot Inspiration Hub", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "<PERSON><PERSON> funkcija nav iekļauta jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.containers.Admin.inspirationHub.UpsellNudge.inspirationHubSupportArticle": "https://support.govocal.com/en/articles/11093736-using-the-inspiration-hub", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Iedvesmas centrs ļauj jums piekļūt izciliem līdzdalības projektiem, kas tiek atlasīti Go Vocal platformās visā pasaulē. <PERSON><PERSON><PERSON><PERSON>, kā citās pilsētās tiek īstenoti veiksmīgi projekti, un aprunājieties ar citiem praktizētājiem.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Pievienojieties demokrātijas pionieru tīklam.", "app.containers.Admin.inspirationHub.Volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> darbs", "app.containers.Admin.inspirationHub.Voting": "<PERSON><PERSON><PERSON>ša<PERSON>", "app.containers.Admin.inspirationHub.commonGround": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ts", "app.containers.Admin.inspirationHub.filters": "filtri", "app.containers.Admin.inspirationHub.resetFilters": "Atiestatīt filtrus", "app.containers.Admin.inspirationHub.seemsLike": "Šķiet, ka projektu vairs nav. Mēģiniet mainīt {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Kampaņ<PERSON> la<PERSON> rediģēšana", "app.containers.Admin.messaging.automated.variablesToolTip": "Ziņojumā varat izmantot šādus <PERSON>īgos:", "app.containers.Admin.messaging.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "<PERSON><PERSON> logrīks parāda katram lietotājam projektus, <b>pamatojoties uz viņa sekot preferencēm</b>. <PERSON><PERSON> ietver projektus, k<PERSON><PERSON> viņi seko, k<PERSON> <PERSON><PERSON><PERSON> projektus, kuro<PERSON> viņi seko ievaddatiem, un projektus, kas saist<PERSON>ti ar tēmām vai jomām, kas viņus interesē.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Šis logrīks lietotājam tiks parādīts tikai tad, ja ir projekti, kuro<PERSON> vi<PERSON> var piedalīties. Ja redzat šo <PERSON>azi<PERSON>, tas no<PERSON><PERSON><PERSON>, ka jūs (administrators) pašlaik nevarat piedalīties nevienā projektā. Šis ziņojums nebūs redzams īstajā sākumlapā.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Atvērts dalībai", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "<PERSON><PERSON><PERSON><PERSON> tiks parād<PERSON>ti projekti, kuros lietotā<PERSON> p<PERSON> var <b>piedalīties</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Nosa<PERSON>ms", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "<PERSON>is logrīks lietotājam tiks parādīts tikai tad, ja, pamatojoties uz viņa sekot preferencēm, būs pieejami viņam atbilstoši projekti. <PERSON>a redzat š<PERSON> zi<PERSON>, tas noz<PERSON>, ka j<PERSON> (administratoram) pa<PERSON>laik nekas netiek sekots. Šis ziņojums nebūs redzams īstajā sākumlapā.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Sekotie p<PERSON>šmeti", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Arhivēts", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Pabei<PERSON><PERSON> un arhivēts", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "<PERSON>ti nav pieejami", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "<PERSON><PERSON> logrīks parāda <b>p<PERSON><PERSON><PERSON> un/vai arhivētus projektus..</b>\"Pabeigti\" ietver arī projektus, kas ir pēdējā fāzē un kuru pēdējā fāze ir ziņojums.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Pabeigtie projekti", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "<PERSON><PERSON><PERSON>, mēs to darījām...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Norā<PERSON><PERSON> nosaukumu visām valodām", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Proje<PERSON><PERSON> nevar būt tuk<PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Nosa<PERSON>ms", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projekts", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Rezultātā iegūtais URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Pievienot projektu", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "Navigācijas joslā būs redzami tikai tie projekti, kuriem lietot<PERSON> ir pie<PERSON>ļ<PERSON>.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "<PERSON><PERSON> būs red<PERSON>s tikai <PERSON>, kad <PERSON> uzraudzīt<PERSON><PERSON><PERSON> atbil<PERSON>.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "<PERSON><PERSON><PERSON> uzraudz<PERSON>ā<PERSON>s", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "Poga", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Nosa<PERSON>ms", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Svarīgi:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> j<PERSON>ājuma pie<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "Beigu datums nav norādīts", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Nospiediet escape, lai i<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projekti un mapes (mantotie)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Projektu nosaukums", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} pa<PERSON><PERSON><PERSON> strādā pie", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Pogas teksts", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Piedalies jau tagad!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "mape", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties projektu vai mapi", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Izvēlieties projektu vai mapi", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Uzmanības centrā", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Nosa<PERSON>ms", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "<PERSON><PERSON><PERSON> no {days} dienas", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "<PERSON><PERSON><PERSON> no {weeks} ned<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} pirms vairākām dienām", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} pirms vairākām nedē<PERSON>m", "app.containers.Admin.project.Campaigns.campaignFrom": "No:", "app.containers.Admin.project.Campaigns.campaignTo": "Uz:", "app.containers.Admin.project.Campaigns.customEmails": "Pielāgotie e-pasta ziņ<PERSON>jumi", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Nosūtiet pielāgotus e-pasta ziņojumus un pārbaudiet statistiku.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet šķiet, ka jums nav piekļuves e-pasta sadaļai.", "app.containers.Admin.project.emails.addCampaign": "Izveidot e-pastu", "app.containers.Admin.project.emails.addCampaignTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Visi {participants} un sekotāji no projekta", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Tas ietver reģistrētos lietotā<PERSON><PERSON>, kas projektā ir veikuši kādu darbību. Nav iekļauti nereģistrēti vai anonimizēti lietotāji.", "app.containers.Admin.project.emails.dateSent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datums", "app.containers.Admin.project.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.draft": "Projekts", "app.containers.Admin.project.emails.editButtonLabel": "Rediģēt", "app.containers.Admin.project.emails.editCampaignTitle": "Rediģēt kampa<PERSON>u", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Viegli sazinieties ar da<PERSON><PERSON><PERSON>, nos<PERSON><PERSON><PERSON> viņiem e-pasta vēstules. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ar ko sazin<PERSON>, un sekojiet līdzi savai iesaistei.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Nosūtiet savu pirmo e-pasta vēstuli", "app.containers.Admin.project.emails.failed": "Neveiksmīgs", "app.containers.Admin.project.emails.fieldBody": "E-pasta ziņa", "app.containers.Admin.project.emails.fieldBodyError": "Sniegt e-pasta ziņ<PERSON><PERSON><PERSON> visā<PERSON> valod<PERSON>s", "app.containers.Admin.project.emails.fieldReplyTo": "Atbildes jānosūta uz š<PERSON> ad<PERSON>i", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Norādiet e-pasta adresi pareizā formātā, piem<PERSON>ram, <EMAIL>.", "app.containers.Admin.project.emails.fieldReplyToError": "Sniedziet e-pasta adresi", "app.containers.Admin.project.emails.fieldReplyToTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, uz kuras e-pasta adreses jāsa<PERSON>em tieš<PERSON> atbildes no lietotājiem uz jūsu e-pastu.", "app.containers.Admin.project.emails.fieldSender": "No", "app.containers.Admin.project.emails.fieldSenderError": "Norādiet e-pasta sūtītāju", "app.containers.Admin.project.emails.fieldSenderTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuru lietotāji redzēs kā e-pasta sūtītāju.", "app.containers.Admin.project.emails.fieldSubject": "E-pasta temats", "app.containers.Admin.project.emails.fieldSubjectError": "Norādiet e-pasta tematu visās valodās", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Tas tiks parādīts e-pasta temata rindā un lietotāja iesūtnes pārskatā. Izklāstiet to skaidri un saistoši.", "app.containers.Admin.project.emails.fieldTo": "Uz", "app.containers.Admin.project.emails.formSave": "Saglab<PERSON>t kā projektu", "app.containers.Admin.project.emails.from": "No:", "app.containers.Admin.project.emails.helmetDescription": "Manuālu e-pasta vēstuļu nosūtīšana projekta dalī<PERSON>niekiem", "app.containers.Admin.project.emails.infoboxAdminText": "Cilnē Projekta ziņojumi varat nosūtīt e-pastu tikai visiem projekta dalībniekiem.  Lai nosūtītu e-pastu citiem dalībniekiem vai lietotāju a<PERSON>š<PERSON>pām, dodieties uz cilni {link} .", "app.containers.Admin.project.emails.infoboxLinkText": "Platformas ziņ<PERSON>", "app.containers.Admin.project.emails.infoboxModeratorText": "Cilnē Projekta ziņojumi varat nosūtīt e-pastu tikai visiem projekta dalībniekiem. Administratori var sūtīt e-pasta vēstules citiem dalībniekiem vai lietotāju a<PERSON>š<PERSON>ām, izmantojot cilni Platformas ziņojumi.", "app.containers.Admin.project.emails.message": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.nameVariablesInfo2": "<PERSON><PERSON><PERSON> varat tie<PERSON>i sa<PERSON> ar <PERSON>, i<PERSON><PERSON><PERSON><PERSON> main<PERSON> lielum<PERSON> {firstName} {lastName}. <PERSON><PERSON><PERSON><PERSON>, \"<PERSON><PERSON><PERSON><PERSON> {firstName} {lastName}, ...\".", "app.containers.Admin.project.emails.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.previewSentConfirmation": "Uz jūsu e-pasta adresi ir nosūtīts priekšskatījuma e-pasts", "app.containers.Admin.project.emails.previewTitle": "Priekšskatījums", "app.containers.Admin.project.emails.projectParticipants": "Projekta <PERSON>nie<PERSON>", "app.containers.Admin.project.emails.recipients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sendTestEmailButton": "Nosūtīt priekšskatījumu", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Nosūtiet šo e-pasta vēstules projektu uz e-pasta adresi, kurā esat pieteicies, lai <PERSON>, kā tas izskatās \"reālajā dzīvē\".", "app.containers.Admin.project.emails.senderRecipients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un saņēmēji", "app.containers.Admin.project.emails.sending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "Tie ir e-pasta z<PERSON><PERSON>, kas nos<PERSON><PERSON><PERSON><PERSON> lie<PERSON>em", "app.containers.Admin.project.emails.status": "Statuss", "app.containers.Admin.project.emails.subject": "Temats:", "app.containers.Admin.project.emails.to": "Uz:", "app.containers.Admin.project.messaging.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "<PERSON><PERSON> attēls ir daļa no mapes kartes; kartes, kas apkopo mapi un tiek parādīta, pie<PERSON><PERSON><PERSON>, sākumlapā. Plašāku informāciju par ieteicamo attēlu izšķirtspēju skatīt {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "<PERSON><PERSON> attēls ir redzams mapes lapas augšpusē. Plašāku informāciju par ieteicamo attēlu izšķirtspēju skatīt {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "apmeklējiet mūsu atbalsta centru", "app.containers.Admin.projects.all.askPersonalData3": "Pievienojiet vārda un e-pasta laukus", "app.containers.Admin.projects.all.calendar.UpsellNudge.enableCalendarView": "<PERSON><PERSON><PERSON><PERSON><PERSON> skata iespē<PERSON>", "app.containers.Admin.projects.all.calendar.UpsellNudge.featureNotIncluded": "<PERSON><PERSON> funkcija nav iekļauta jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.containers.Admin.projects.all.calendar.UpsellNudge.learnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.calendar.UpsellNudge.timelineSupportArticle": "https://support.govocal.com/en/articles/7034763-creating-and-customizing-your-projects#h_ce4c3c0fd9", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellDescription2": "Iegūstiet vizuālu pārskatu par savu projektu grafiku mūsu kalendāra skatā. <PERSON><PERSON>, kuri projekti un posmi drīz sāksies vai beigsies un kam ir nepieciešama rīcība.", "app.containers.Admin.projects.all.calendar.UpsellNudge.upsellTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> par to, kas un kad notiek", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Visi jautājumi ir parādīti PDF formātā. <PERSON><PERSON>r pa<PERSON>k nav iespējams importēt, i<PERSON><PERSON><PERSON>t FormSync, <PERSON><PERSON><PERSON> jautāju<PERSON>: <PERSON><PERSON><PERSON><PERSON>, birkas un failu augšup<PERSON>de.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Visi jautājumi ir parādīti PDF formātā. <PERSON><PERSON><PERSON> pašlaik nav iespējams importēt, i<PERSON><PERSON><PERSON>t FormSync, <PERSON><PERSON><PERSON> jautājumus: kart<PERSON><PERSON><PERSON> jautājumi (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> maršruts un zīmēšanas apgabals), klasifikācijas jautājumi, matricas jautājumi un failu augšupiel<PERSON>des jautājumi.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Veidlapas beigas", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Veidl<PERSON><PERSON> s<PERSON>ku<PERSON>", "app.containers.Admin.projects.all.components.archived": "Arhivēts", "app.containers.Admin.projects.all.components.draft": "Melnraksts", "app.containers.Admin.projects.all.components.manageButtonLabel": "Rediģēt", "app.containers.Admin.projects.all.copyProjectButton": "Ko<PERSON><PERSON><PERSON> projektu", "app.containers.Admin.projects.all.copyProjectError": "<PERSON><PERSON><PERSON><PERSON>, kop<PERSON><PERSON><PERSON> proje<PERSON>, l<PERSON><PERSON><PERSON>, mēģiniet vēl<PERSON><PERSON> vēlāk.", "app.containers.Admin.projects.all.customiseEnd": "Pielāgojiet veidlapas beigas.", "app.containers.Admin.projects.all.customiseStart": "Pielāgojiet veid<PERSON> sākumu.", "app.containers.Admin.projects.all.deleteFolderButton1": "Dzēst mapi", "app.containers.Admin.projects.all.deleteFolderConfirm": "Vai esat pārliecināts, ka vēlaties dzēst šo mapi? Tiks izdzēsti arī visi šajā mapē esošie projekti. Šo darbību nav iespējams atcelt.", "app.containers.Admin.projects.all.deleteFolderError": "<PERSON><PERSON><PERSON><PERSON>, no<PERSON><PERSON><PERSON> šo mapi. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Dzēst projektu", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst šo projektu? Šo darbību nevar atsaukt.", "app.containers.Admin.projects.all.deleteProjectError": "<PERSON><PERSON> projektu <PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēl<PERSON><PERSON> vēlāk.", "app.containers.Admin.projects.all.exportAsPDF1": "Lejupielādēt PDF veidlapu", "app.containers.Admin.projects.all.itIsAlsoPossible1": "V<PERSON>t apvienot tiešsaistes un bezsaistes atbildes. <PERSON> aug<PERSON>tu bezsaistes atbildes, dodieties uz šā projekta cilni \"Ievades pārvaldnieks\" un noklikšķiniet uz \"Importēt\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "V<PERSON>t apvienot tiešsaistes un bezsaistes atbildes. <PERSON> aug<PERSON>tu bezsaistes atbildes, dodieties uz šī projekta cilni \"Aptauja\" un noklikšķiniet uz \"Importēt\".", "app.containers.Admin.projects.all.logicNotInPDF": "Aptaujas loģika netiks atspoguļota lejupielādētajā PDF failā. Papīra formātā respondenti redzēs visus aptaujas jautājumus.", "app.containers.Admin.projects.all.new.Folders.Filters.searchFolders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mapes", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Visas mapes ir iel<PERSON><PERSON>tas", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Mapes", "app.containers.Admin.projects.all.new.Folders.Table.managers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# projekts} other {# projekti}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Statuss", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Projekta sākuma datums", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Atklājamība", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Mapes", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pēc pašre<PERSON><PERSON><PERSON><PERSON><PERSON> fāzes da<PERSON> metodes", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Ideju i<PERSON>strā<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Informācija", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Līdzdalības metode", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Dokumenta anotācija", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ts", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Priekšlikumi", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> darbs", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "<PERSON><PERSON><PERSON>ša<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informēšana", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Nav uzsākta", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Līdzdalības valsts", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.PendingApproval.pendingApproval": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Search.searchProjects": "<PERSON><PERSON><PERSON><PERSON><PERSON> projektus", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "<PERSON><PERSON><PERSON> al<PERSON> (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "<PERSON><PERSON><PERSON> (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "<PERSON><PERSON><PERSON> s<PERSON>kas vai drīz be<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_asc1": "<PERSON><PERSON>n <PERSON> (vecs-jauns)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created_desc1": "<PERSON>esen izveido<PERSON> (jauns-novs)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Statuss", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Grupas", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Redzamība", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "<PERSON><PERSON><PERSON> filtru", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Neviens filtrs vairs nav jāpievieno", "app.containers.Admin.projects.all.new.Projects.Table.admins": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Visi projekti ir i<PERSON>ti", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Ikviens", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Arhivēts", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fāze", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d kreisi pa kreisi", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d, lai sāktu", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Atklājamība:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Projekts", "app.containers.Admin.projects.all.new.Projects.Table.end": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.ended": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Grupas", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo pa kreisi", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo, lai sāktu", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Nākamais posms:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "Nav piešķirts", "app.containers.Admin.projects.all.new.Projects.Table.phase": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Pirms pala<PERSON>šanas", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projekts", "app.containers.Admin.projects.all.new.Projects.Table.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.published": "Publicēts", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Ritiniet uz leju, lai i<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>k", "app.containers.Admin.projects.all.new.Projects.Table.start": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.status": "Statuss", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Statuss:", "app.containers.Admin.projects.all.new.Projects.Table.thisColumnUsesCache": "<PERSON><PERSON><PERSON> slejā tiek izmantoti kešētie dalībnieku dati. <PERSON> skat<PERSON>tu j<PERSON> s<PERSON>, pārbaudiet projekta cilni \"Dalībnieki\".", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Redzamība", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Redzamība:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} grupas", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} v<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y pa kreisi", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y, lai sāktu", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fāze: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} at<PERSON><PERSON><PERSON><PERSON> dienu skaits", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Mapes: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fāze nav", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Beigu datums nav norādīts", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "Nav posmu", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Fāze {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Fāzes:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projekts", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Darba uzsākšanas datums: {date}", "app.containers.Admin.projects.all.new.arrangeProjects": "Organizēt projektus", "app.containers.Admin.projects.all.new.calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.folders": "Mapes", "app.containers.Admin.projects.all.new.projects": "Projekti", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON> i<PERSON>t laika grafi<PERSON>.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Projektam nav beigu datuma", "app.containers.Admin.projects.all.new.timeline.project": "Projekts", "app.containers.Admin.projects.all.notes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.personalDataExplanation5": "Izmantojot š<PERSON>, eksportētajam PDF dokumentam tiks pievienoti vārda, uzvārda un e-pasta lauki. Pēc papīra veidlapas augšupielādes mēs i<PERSON><PERSON>, lai automātiski izveidotu kontu bezsaistes aptaujas respondentam.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI kopsavilkums", "app.containers.Admin.projects.project.analysis.Comments.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> ir pieejams, ja ir 5 vai vairāk koment<PERSON>ri.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Apkopot komentā<PERSON>", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON>} =1 {1 jauns komentārs} other {# jauni komentāri}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI kopsavilkums", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Šis ir mākslīgā intelekta radīts saturs. Tas var nebūt 100% precīzs. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet un salīdziniet ar faktiskajiem ievades datiem, lai pārliecinātos par precizitāti. Ņemiet vērā, ka precizit<PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON>, u<PERSON><PERSON><PERSON><PERSON>, ja izvēlēto ievades datu skaits tiks samazināts.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Paziņojumi pa e-pastu nosūtīti tikai dal<PERSON>m", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Nav indeksēts meklētājprogrammās", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Nav redzams sāku<PERSON><PERSON> vai logrīkos", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "<PERSON><PERSON><PERSON> tika<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> tiešo URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, cik atklājams ir šis projekts.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Šis projekts ir redzams visiem, kam ir <PERSON>, un tas būs redzams sākumlapā un logrīkos.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Šis projekts būs paslēpts plašākai sabiedrībai un būs redzams tikai tiem, kam ir saite.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Kas var atrast šo projektu?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Atvērtā AI analīze", "app.containers.Admin.projects.project.ideas.analysisText2": "Izpētiet ar mākslīgo intelektu darbināmus kopsavilkumus un apskatiet atsevišķus iesniegumus.", "app.containers.Admin.projects.project.ideas.importInputs": "Importēt", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "<PERSON>ēc ziņo<PERSON><PERSON> izveides varat izvēlēties to kopīgot ar sa<PERSON>, tiklīdz posms ir sācies.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Sarežģītākas informācijas kopīgošanas lapas izveide", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Izveidojiet z<PERSON>ņ<PERSON>mu, lai:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Izveidot ziņojumu", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Izveidojiet pārskatu par iepriekšējo posmu vai sāciet no jauna.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "<PERSON><PERSON> ziņojums nav publiski pieejams. <PERSON> to publiski pieejamu, aktivizējiet slēdzi \"Redzams\".", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "<PERSON>is posms ir sā<PERSON>, bet ziņojums vēl nav publiski pieejams. Lai to padar<PERSON><PERSON> publiski pieejamu, aktivizējiet pārslēdzēju \"Redzams\".", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Sāciet ar fā<PERSON> veidni", "app.containers.Admin.projects.project.information.ReportTab.report": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Kopīgojiet iepriekš veiktas aptaujas vai ideju izstrādes posma rezultātus.", "app.containers.Admin.projects.project.information.ReportTab.visible": "Redzams", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Šis ziņojums tiks publis<PERSON>, tik<PERSON><PERSON><PERSON>z posms sāksies. Lai tas nebūtu publiski pieej<PERSON>, izsl<PERSON><PERSON><PERSON><PERSON> slēdzi \"Redzams\".", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "Šis ziņojums pašlaik ir publiski pieejams. Lai tas nebūtu publiski pieejams, izslēd<PERSON>t slēdzi \"Redzams\".", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Vai esat pā<PERSON>ā<PERSON>, ka vēlaties dzēst šo ziņo<PERSON>mu? Šo darbību nevar atcelt.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "<PERSON><PERSON> turpin<PERSON>t darbu, jums tam ir j<PERSON><PERSON>.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Veidlapu var lejupielādēt šeit.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> veidlapa tika izveidota ar sadaļu \"Personas dati\".", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Veidlapu valoda", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Ar šo es piekrītu šī <PERSON>a a<PERSON>ādei, <PERSON>zman<PERSON><PERSON>t Google mākoņveidlapu parseri.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Importēt Excel failu", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "<PERSON><PERSON><PERSON><PERSON>, au<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai tur<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "Šablonu var lejupielādēt šeit.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Augšupielād<PERSON><PERSON><PERSON> a<PERSON> <b>Excel failu</b> (.xlsx). <PERSON><PERSON><PERSON> jā<PERSON>manto šim projektam paredzētā veidne. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Augšupielādējiet <b>skenētu veidlapu PDF failu</b>. <PERSON><PERSON><PERSON> jā<PERSON><PERSON>to šajā posmā izdrukāta veidlapa. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Izmantojiet šo e-pasta adresi jaunajam lietotājam", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Ievadiet derīgu e-pasta adresi, lai izve<PERSON>tu jaunu kontu", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Autoram tiks izveidots jauns konts ar šo informāciju. Tam tiks pievienots šis ievads.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Uzvārds", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "<PERSON><PERSON><PERSON><PERSON>, ievadiet e-pasta adresi un/vai vārdu un uzvārdu, lai piešķirtu šo ievadi autoram. Vai arī noņemiet atzīmi piekrišanas lodziņā.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Ar šo e-pasta adresi jau ir saistīts konts. <PERSON><PERSON> ievads tiks pievienots tam.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Lietotāja <PERSON> (izveidot lietotāja kontu)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus ievades datus", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Autors:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "E-pasts:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Importa laikā ir radu<PERSON><PERSON><PERSON>, un daži ievades dati nav importēti. <PERSON><PERSON><PERSON><PERSON>, izlabojiet kļūdas un atkārtoti importējiet trūkstošos ievades datus.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "<PERSON><PERSON><PERSON><PERSON> veidlapas dati. <PERSON><PERSON><PERSON><PERSON><PERSON>, vai iep<PERSON>ād<PERSON><PERSON><PERSON>ā veidlapā nav kļūdu.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Importēt Excel failu (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Importēt", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>u <PERSON> (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "<PERSON><PERSON><PERSON><PERSON> veid<PERSON>u <PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON> ievadi", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importēšana. Šis process var aizņemt da<PERSON> min<PERSON>.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "<PERSON><PERSON> dati tika <PERSON>.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} ir import<PERSON>ti ievades dati, un tie ir j<PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} i<PERSON><PERSON><PERSON><PERSON><PERSON> nevarēja apstiprināt. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai katrā ievadītajā informācijā nav apstiprinā<PERSON>nas problēmu, un apstipriniet to atsevišķi.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Atraša<PERSON><PERSON><PERSON> vieta:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Pagaidām nav ko pārskatīt. Noklikšķiniet uz \"{importFile}\", lai importētu PDF failu ar skenētām ievades veidlapām vai Excel failu ar ievades datiem.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Vēl nav ko pārskatīt. Noklikšķiniet uz \"{importFile}\", lai importētu Excel failu ar ievaddatiem.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "<PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Nevar parādīt importēto failu. Importētā faila skatīšana ir pieejama tikai PDF importam.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Fāze:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fāzē nevar būt ievades. L<PERSON><PERSON><PERSON>, izvēlieties citu.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "<PERSON><PERSON><PERSON> projektā nav fāžu, k<PERSON><PERSON><PERSON> var būt idejas.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kurai fāzei vēlaties pievienot <PERSON>os ievades datus.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Ievades <PERSON>", "app.containers.Admin.projects.project.participation.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.inputs": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.participantsTimeline": "Dalībnieku grafiks", "app.containers.Admin.projects.project.participation.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "Izvēlieties periodu", "app.containers.Admin.projects.project.participation.usersByAge": "Lietotā<PERSON> pēc ve<PERSON>a", "app.containers.Admin.projects.project.participation.usersByGender": "Lietotāji pēc d<PERSON>a", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Nepieciešams", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "Iespēja pievienot vai rediģēt lietotāja laukus fāzes līmenī nav iekļauta jūsu pašreizējā licencē. Sazinieties ar savu Gov<PERSON><PERSON><PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> par to.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} iespējas", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Lauka statuss", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "<PERSON><PERSON> tiks pievienoti kā aptaujas veidlapas pēdēj<PERSON>, jo fāzes iestatījumos ir atlasīta opcija \"Rād<PERSON>t laukus aptaujā?\".", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON> netiks uzdoti.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Pēc izvēles", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "<PERSON>ēc izv<PERSON> - v<PERSON><PERSON><PERSON><PERSON>, jo uz to atsaucas grup<PERSON>.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> v<PERSON><PERSON><PERSON><PERSON>, jo uz to atsaucas grupa", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Aizpildiet tāl<PERSON>k nor<PERSON><PERSON> pap<PERSON> j<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Apstipriniet savu e-pasta adresi", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "No verifikācijas metodes atgrieztie dati:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Ievadiet vārdu, uzvārdu, e-pastu un paroli", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Ievadiet savu e-pasta adresi", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "<PERSON><PERSON> nesen ir jā<PERSON><PERSON><PERSON><PERSON>a lieto<PERSON>?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Identitā<PERSON> verifi<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> {verificationMethod} (pamatojoties uz lietotāju grupu).", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "<PERSON>, nav jā<PERSON><PERSON> nek<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Izmanto<PERSON><PERSON> v<PERSON>, lai ierobe<PERSON>, pamatojoties uz iepriekš minētajiem pārbaud<PERSON>ta<PERSON>em datiem.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Lietotājiem jābūt verificētiem pēdējo 30 minūšu laikā.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Lietot<PERSON><PERSON><PERSON> jābūt verificētiem pēdējo {days} dienu laik<PERSON>.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "Pēdējo 30 dienu laik<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "Pēdējo 30 minūšu laikā", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "Pēdējo 7 dienu laik<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Vienreiz ir pietiekami", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} p<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Konta izveide", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Dalībniekiem ir jāizveido pilns konts, nor<PERSON><PERSON>t savu vārdu, apstiprinātu e-pasta adresi un paroli.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Dalībniekiem ir jāizveido pilns konts, nor<PERSON><PERSON>t savu vārdu, e-pasta adresi un paroli.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Autentifikācija", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "Rediģēt", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "E-pasta apstiprinājums", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Dalībniekiem ir jāapstiprina savs e-pasts ar vienre<PERSON><PERSON>ju kodu.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "<PERSON><PERSON><PERSON><PERSON><PERSON> surog<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "<PERSON><PERSON>, analizējot IP adreses un ierīces datus, palīdz novērst atkārtotu apsekojumu iesniegšanu no izrakstītiem lietotājiem. Lai gan tā nav tik precīza kā pieteik<PERSON>n<PERSON>, tā var palīdzēt samazināt atkārtotu atbilžu skaitu.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Piezīme: k<PERSON><PERSON><PERSON><PERSON><PERSON> tīk<PERSON> (<PERSON><PERSON><PERSON><PERSON>, birojos vai publiskos Wi-Fi tīklos) pastāv neliela iespēja, ka dažādi lietotāji var tikt atzīmēti kā dublētāji.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Iespējot u<PERSON> surogā<PERSON>pas<PERSON> at<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "Nav", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Piedalīties var ikviens bez reģistrēšanās vai pieteikšanās.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Pa<PERSON><PERSON><PERSON> jautājumu un grupu atiestatīšana", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Ierobežot dalību tikai lietotāju grupai(-ām)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO verifikācija", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir jāapstiprina sava identitāte vietnē {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Atvērtā AI analīze", "app.containers.Admin.projects.project.survey.allFiles": "Visi faili", "app.containers.Admin.projects.project.survey.allResponses": "Visas atbildes", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Precizitāte: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Mākslīgā intelekta kopsavilkuma ģenerēšanā tika pieļauta k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet to reģenerēt tālāk tekstā.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Atvērtā AI analīze", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>us par <PERSON>o j<PERSON>", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ievades", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "<PERSON><PERSON><PERSON><PERSON> analīzes darb<PERSON>bas", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} jaunas atbildes", "app.containers.Admin.projects.project.survey.analysis.regenerate": "At<PERSON>uno<PERSON>", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Rādīt AI ieskatu", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Pašreizējā plānā vienlaicīgi varat apkopot ne vairāk kā 30 ievades datus. Sazinieties ar savu GovSuccess pārvald<PERSON> vai <PERSON>u, lai atkl<PERSON><PERSON> vairāk.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Izvēlie<PERSON> saist<PERSON>tus j<PERSON>", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Vai {question}anal<PERSON><PERSON><PERSON> vēlaties iekļaut arī citus saistītus j<PERSON>?", "app.containers.Admin.projects.project.survey.cancel": "Atcelt", "app.containers.Admin.projects.project.survey.consentModalButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCancel": "Atcelt", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "Es piekrītu izmantot OpenAI kā datu apstrādātāju šajā projektā.", "app.containers.Admin.projects.project.survey.consentModalText1": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ka OpenAI tiek izman<PERSON>ta kā šī projekta datu apstrādātājs.", "app.containers.Admin.projects.project.survey.consentModalText2": "OpenAI API nodrošina automātisko teksta kopsavilkumu un daļu no automātiskās marķēšanas pieredzes.", "app.containers.Admin.projects.project.survey.consentModalText3": "Uz OpenAI API mēs nosūtām tikai to, ko lietotāji rakstīja savās aptaujās, idejās un komentāros, un nekad nekādu informāciju no viņu profila.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI neizmantos šos datus turpmākai savu modeļu apmācībai. Vairāk informācijas par to, kā OpenAI rīkojas ar datu konfidencialitāti, var atrast {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "š<PERSON>", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON><PERSON> turpin<PERSON>t", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "<PERSON><PERSON><PERSON> nevarat ieva<PERSON>, pirms neesat rediģējis veidlapu.", "app.containers.Admin.projects.project.survey.deleteAnalysis": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Vai esat p<PERSON>, ka vēlaties dzēst šo analīzi? Šo darbību nevar atcelt.", "app.containers.Admin.projects.project.survey.explore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.followUpResponses": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> vidēji", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Eksportēt kā GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Eksportējiet atbildes uz šo jautājumu kā GeoJSON failu. Katram GeoJSON objektam visas saistītās respondenta aptaujas atbildes tiks uzskaitītas šī objekta \"īpašību\" objektā.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Paslēpt informā<PERSON>", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondenti} one {{respondentCount} respondents} other {~{respondentCount} respondenti}}respondents ~ ~", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} izvēles} one {~{numberChoices} izvēles} other {~{numberChoices} izvēles}}", "app.containers.Admin.projects.project.survey.heatMap": "Siltuma karte", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Uzziniet vairāk par siltuma kartēm, ka<PERSON> ģenerētas, <PERSON><PERSON><PERSON><PERSON><PERSON> E<PERSON>ri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Siltuma karte ir <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Esri Smart Mapping. Siltuma kartes ir node<PERSON><PERSON><PERSON>, ja ir liels datu punktu skaits. Ja punktu skaits ir ma<PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON>, labāk ir tieši apskatīt tikai atrašanās vietas punktus. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Siltuma kartes skats", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Pa<PERSON><PERSON><PERSON>pts pēc loģikas", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Kad lietotā<PERSON>s iz<PERSON>, loģika izlaiž visas lapas līdz {pageNumber} lapai ({numQuestionsSkipped} jautā<PERSON><PERSON> izlai<PERSON>i). Noklikšķiniet, lai paslēptu vai parādītu izlaistās lapas un jautājumus.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Ja lietotā<PERSON>s iz<PERSON>, loģika pāriet uz aptaujas beigām ({numQuestionsSkipped} jautājumi ir izlaisti). Noklikšķiniet, lai paslēptu vai parādītu izlaistās lapas un jautājumus.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Loģika šajā lapā izlaiž visas lapas līdz {pageNumber} lapai ({numQuestionsSkipped} jautā<PERSON><PERSON> izlai<PERSON>i). Noklikšķiniet, lai paslēptu vai parādītu izlaistās lapas un jautājumus.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Loģika šajā lapā izlaiž aptaujas beigas ({numQuestionsSkipped} jautāju<PERSON> izlaisti). Noklikšķiniet, lai paslēptu vai parādītu izlaistās lapas un jautājumus.", "app.containers.Admin.projects.project.survey.newAnalysis": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.nextInsight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.openAnalysis": "Atvērtā AI analīze", "app.containers.Admin.projects.project.survey.otherResponses": "Citas atbildes", "app.containers.Admin.projects.project.survey.page": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "Iepriekšē<PERSON>is <PERSON>", "app.containers.Admin.projects.project.survey.responses": "Atbildes", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "<PERSON><PERSON><PERSON> lapas atbilžu skaits ir mazāks nekā kopējais aptaujas atbilžu skaits, jo daži respondenti aptaujas loģikas dēļ nebūs redzējuši šo lapu.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Atbilžu skaits uz šo jautājumu ir mazāks nekā kopējais aptaujas atbilžu skaits, jo daži respondenti aptaujas loģikas dēļ nebūs redzējuši šo jautājumu.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Noskaņojuma skala", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Uzreiz apkopojiet visas savas atbildes.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Run<PERSON>jiet ar saviem datiem dabiskajā valodā.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Saņ<PERSON>t atsauces uz atsevišķām atbildēm no mākslīgā intelekta ģenerētiem kopsavilkumiem.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Pilnu pārskatu skatiet mūsu vietnē {link} .", "app.containers.Admin.projects.project.survey.upsell.button": "Atbloķējiet AI analīzi", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "<PERSON><PERSON><PERSON> raksts", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Ātrāka datu analīze ar mākslīgo intelektu", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "<PERSON><PERSON> funkcija nav iekļauta jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.containers.Admin.projects.project.survey.viewAnalysis": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Izpētiet ar mākslīgo intelektu darbināmus kopsavilkumus un apskatiet atsevišķus iesniegumus.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Izvēlieties periodu", "app.containers.Admin.projects.project.traffic.trafficSources": "Satiksmes avoti", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Esam main<PERSON> veidu, kā vācam un rādām apmeklētāju datus. Rezultātā apmeklētāju dati ir precīzāki un pieejami vairāk datu veidu, vien<PERSON><PERSON> saglabājot atbilstību GDPR. <PERSON><PERSON> jaunos datus sākām vākt tikai 2024. gada novembrī, tāpēc pirms tam dati nav pieejami.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Apmeklētāju gra<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Posma ziņojums", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Pievienojiet tekstu par fāzi", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Šeit ir teksts. To var rediģēt un formatēt, i<PERSON><PERSON><PERSON>t redaktoru panelī labajā pusē.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Projekta rezultāti", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Pievienojiet projekta mērķi, i<PERSON><PERSON><PERSON><PERSON><PERSON> līdzdalības metodes un rezultātu", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "<PERSON><PERSON><PERSON> ir i<PERSON><PERSON><PERSON> nesaglabātas izmaiņas. Pirms drukāšanas l<PERSON>, saglabājiet to.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Nosaukums jau ir a<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar iepriekšējām {days} dienām", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "<PERSON><PERSON><PERSON><PERSON><PERSON> statistiku", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lī<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "<PERSON><PERSON><PERSON><PERSON><PERSON> salī<PERSON> ar iepriekšē<PERSON> periodu", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Vispirms ir jāizv<PERSON>las datumu diapazons.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Dal<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON> ievades", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "<PERSON><PERSON><PERSON><PERSON><PERSON> balsis", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demogrā<PERSON><PERSON><PERSON> dati", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Reģistrācijas datumu diapazons", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Reģistrācijas lauks", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "<PERSON><PERSON>inā<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Lietotāji: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Arhivēts", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Atklāts", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Plānots", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projekti", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Publikācijas statuss", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Publicēts", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Trūkst šā logrīka datu. Pārkonfigurējiet vai d<PERSON>ē<PERSON>t to, lai varētu saglabāt pārskatu.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "<PERSON><PERSON><PERSON> monitora veselī<PERSON> rād<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Ce<PERSON><PERSON><PERSON>ļ<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "Gads", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "<PERSON><PERSON><PERSON> projektā nav atrasti atbilstoši posmi", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Nav izvē<PERSON><PERSON>ta neviena fāze. <PERSON><PERSON><PERSON><PERSON>, vispi<PERSON> izvēlieties fāzi.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Nav projekta", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Nav izvēlēts neviens projekts. <PERSON><PERSON><PERSON><PERSON>, vispirms izvēlieties projektu.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "<PERSON><PERSON><PERSON>t <PERSON>, jo tajā ir i<PERSON><PERSON> da<PERSON>, kuriem jums nav pie<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "<PERSON>o pā<PERSON>atu nevarat rediģēt, jo tajā ir i<PERSON><PERSON><PERSON> dati, kuriem jums nav piekļ<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Vai esat pā<PERSON>, ka vēlaties dzēst \"{reportName}\"? <PERSON>o darbību nevar atsaukt.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Vai esat pā<PERSON>ā<PERSON>, ka vēlaties dzēst šo ziņo<PERSON>mu? Šo darbību nevar atcelt.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Dub<PERSON>āts", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Rediģēt", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modificēts {days, plural, no {# dienas} one {# diena} other {# dienas}} pirms", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Mēģinot izveidot <PERSON>, rad<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēl<PERSON><PERSON> vēlāk.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Sāciet ar tukšu lapu", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Sāciet ar Ko<PERSON>nas monitor<PERSON> ve<PERSON>ni", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Izveidot ziņojumu", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Pielāgojiet savu ziņojumu un kopīgojiet to ar iekšējām ieinteresētajām personām vai sabiedrību PDF faila veidā.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Izveidot ziņojumu", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Izveidojiet savu pirmo ziņ<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Nav izvēlēts neviens projekts", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Sāciet ar platformas veidni", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Drukāt uz PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Sāciet ar projekta veidni", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Ce<PERSON><PERSON><PERSON>ļ<PERSON> {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Ziņojums ar š<PERSON>du nosaukumu jau ir sagatavots. <PERSON><PERSON><PERSON><PERSON>, izvēlieties citu nosaukumu.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Izvēlieties ceturksni", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Izvēlieties gadu", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Lai to kop<PERSON><PERSON>u ar citiem, izdr<PERSON><PERSON><PERSON><PERSON> ziņojumu PDF formātā.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Kopīgojiet kā tīmekļa saiti", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "<PERSON><PERSON> tīmek<PERSON>a saite ir pieejama tikai <PERSON>am.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Koplietot", "app.containers.Admin.reporting.contactToAccess": "Pielāgota ziņojuma izveide ir daļa no premium licences. Sazinieties ar savu GovSuc<PERSON> men<PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> vair<PERSON>k par to.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Visi ziņojumi", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Ko<PERSON>nas uzraudzītāja ziņojumi", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Šie ziņojumi ir sa<PERSON>īti ar Kopienas uzraudzītāju. Pārskati tiek automātiski ģenerēti katru ceturksni.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Izveidot ziņojumu", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Pielāgojiet savu ziņojumu un kopīgojiet to ar iekšējām ieinteresētajām personām vai sabiedrību, i<PERSON><PERSON><PERSON><PERSON> tīmek<PERSON>a saiti.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "<PERSON><PERSON><PERSON> z<PERSON> tiks parādīti <PERSON>.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Mekl<PERSON><PERSON><PERSON> ziņ<PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Progresa z<PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Šos pārsk<PERSON> ir izveido<PERSON> j<PERSON> vald<PERSON> veiksm<PERSON> men<PERSON>ž<PERSON>.", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.deprecated": "DEPRECATED", "app.containers.Admin.reporting.helmetDescription": "<PERSON><PERSON> <PERSON>a", "app.containers.Admin.reporting.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.printPrepare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "app.containers.Admin.reporting.reportBuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.reportHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> galvene", "app.containers.Admin.reporting.warningBanner3": "<PERSON><PERSON><PERSON> ziņojumā iekļautie grafiki un skaitļi automātiski atjauninās tikai šajā lapā. Lai tos atjaunin<PERSON><PERSON> citā<PERSON> lap<PERSON>, saglabājiet ziņ<PERSON>.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ts", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideju i<PERSON>strā<PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Informācija", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "<PERSON>zmantotās metodes", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {days} dienas: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Priekšlikumi", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> darbs", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "<PERSON><PERSON><PERSON>ša<PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Di<PERSON>ram<PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "Tabula", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.surveyFormTab.downloads": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Dublēt citu apsekojumu", "app.containers.Admin.surveyFormTab.editSurveyForm": "<PERSON><PERSON><PERSON><PERSON> veidlapas rediģēšana", "app.containers.Admin.surveyFormTab.inputFormDescription": "<PERSON><PERSON><PERSON><PERSON>, kāda informācija ir j<PERSON>, pie<PERSON><PERSON><PERSON><PERSON> aprakstus vai nor<PERSON>, lai palī<PERSON><PERSON><PERSON><PERSON> da<PERSON><PERSON><PERSON><PERSON> sniegt at<PERSON>, un nor<PERSON><PERSON>t, vai katrs lauks ir izvēles vai obligāts.", "app.containers.Admin.surveyFormTab.surveyForm": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>a", "app.containers.Admin.tools.apiTokens.createTokenButton": "Izveidot j<PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Atcelt", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "<PERSON><PERSON>su žetons ir izve<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, nokopējiet zemāk redzamo {secret} un droši uzglabājiet to.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Izveido<PERSON>et jaunu token, lai to izmantotu ar mūsu publisko API.", "app.containers.Admin.tools.apiTokens.createTokenError": "Norādiet žeton<PERSON> no<PERSON>", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Izveidot žetonu", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b><PERSON><PERSON><PERSON><PERSON>!</b> <PERSON><PERSON> viet<PERSON> {secret} var kopēt tikai vienu reizi. Ja jūs aizvērsiet šo logu, jūs to vairs nevarē<PERSON>t redzēt.", "app.containers.Admin.tools.apiTokens.createTokenName": "Nosa<PERSON>ms", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Piešķiriet savam žetonam nosaukumu", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Jūsu žetons ir izveidots", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Aizvērt", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Ko<PERSON>ēts!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Izveidot j<PERSON>", "app.containers.Admin.tools.apiTokens.createdAt": "Izveidots", "app.containers.Admin.tools.apiTokens.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst šo ž<PERSON>?", "app.containers.Admin.tools.apiTokens.description": "Pārvaldiet savus API žetonus mūsu publiskajam API. Lai i<PERSON> vair<PERSON>k informācijas, skatiet mūsu {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Pēd<PERSON><PERSON> reizi i<PERSON>", "app.containers.Admin.tools.apiTokens.link": "API dokumentācija", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Nosa<PERSON>ms", "app.containers.Admin.tools.apiTokens.noTokens": "Jums vēl nav žetonu.", "app.containers.Admin.tools.apiTokens.title": "Publiskie API žetoni", "app.containers.Admin.tools.esriDisabled": "Esri integrācija ir papildfunkcija. Sazinieties ar savu Gov<PERSON><PERSON><PERSON>, ja vēlaties saņ<PERSON>t vairāk informācijas par to.", "app.containers.Admin.tools.esriIntegration2": "<PERSON>sri <PERSON>teg<PERSON>", "app.containers.Admin.tools.esriIntegrationButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Savienojiet savu Esri kontu un importējiet datus no ArcGIS Online tieši savos kartēšanas projektos.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri logotips", "app.containers.Admin.tools.esriKeyInputDescription": "Pievienojiet savu Esri API atslēgu, lai varētu importēt kartes slāņus no ArcGIS Online projektu karšu cilnēs.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API atslēga", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Šeit ielīmējiet API atslēgu", "app.containers.Admin.tools.esriMaps": "Esri Maps", "app.containers.Admin.tools.esriSaveButtonError": "<PERSON><PERSON><PERSON><PERSON> sagla<PERSON>, l<PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API atslēgas saglabāšana", "app.containers.Admin.tools.esriSaveButtonText": "Saglab<PERSON><PERSON>", "app.containers.Admin.tools.learnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.managePublicAPIKeys": "API atslēgu pārvaldība", "app.containers.Admin.tools.manageWidget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.manageWorkshops": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBIAPIImage": "Power BI attēls", "app.containers.Admin.tools.powerBIDescription": "Izmantojiet mūsu plug & play Power BI veidnes, lai piek<PERSON><PERSON><PERSON> Go Vocal datiem savā Microsoft Power BI darba vidē.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI nav daļa no jūsu licences. Sazinieties ar savu GovSuc<PERSON>, ja vēlaties saņemt vairāk informācijas par šo j<PERSON>.", "app.containers.Admin.tools.powerBIDownloadTemplates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "<PERSON>a plān<PERSON>t izmantot Go Vocal datus Power BI datu plūsmā, <PERSON><PERSON> veid<PERSON> iestatīt jaunu datu plūsmu, kas ir savienota ar jūsu Go Vocal datiem. Kad esat lejupielādējis <PERSON>, pirms augšupielādēšanas PowerBI jums vispirms jāatrod un jāaizstāj šablonā šādas virknes ##CLIENT_ID### un ##CLIENT_SECRET### ar saviem publiskajiem API akreditācijas datiem.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Leju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu plū<PERSON> ve<PERSON>ni", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "<PERSON><PERSON> p<PERSON><PERSON><PERSON> ve<PERSON>", "app.containers.Admin.tools.powerBITemplates.intro": "Piezīme: <PERSON> kādu no šīm Power BI veidnēm, vispirms ir jābūt {link}.", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "izveidot mūsu publiskā API pilnvaru kopumu.", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Šī veidne izveidos Power BI pārskatu, pamatojoties uz jūsu Go Vocal datiem. Tajā tiks iestatīti visi datu savienojumi ar jūsu Go Vocal platformu, izveidots datu modelis un daži noklusējuma paneļi. Atverot veidni Power BI programmā, jums tiks piedāvāts ievadīt publiskos API akreditācijas datus. Jums būs jāievada arī jūsu platformas bāzes url, kas ir: {baseUrl}.", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Leju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zi<PERSON> ve<PERSON>", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Sīkāku informāciju par Go Vocal datu i<PERSON> Power BI var atrast mūsu vietnē {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "<PERSON><PERSON><PERSON> raksts", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI veidnes", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Pārvaldiet akreditācijas datus, lai izveidotu pielāgotas integrācijas mūsu publiskajā API.", "app.containers.Admin.tools.publicAPIDisabled1": "Publiskais API nav daļa no jūsu pašreizējās licences. Sazinieties ar savu GovSuc<PERSON>, ja vēlaties saņemt vairāk informācijas par šo jautājumu.", "app.containers.Admin.tools.publicAPIImage": "Publiskais API attēls", "app.containers.Admin.tools.publicAPITitle": "Publiskā piekļuve API", "app.containers.Admin.tools.toolsLabel": "Instrumenti", "app.containers.Admin.tools.widgetDescription": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.Admin.tools.widgetImage": "<PERSON>g<PERSON><PERSON><PERSON> at<PERSON>", "app.containers.Admin.tools.widgetTitle": "Logrīks", "app.containers.Admin.tools.workshopsDescription": "<PERSON><PERSON><PERSON> video san<PERSON><PERSON><PERSON>, veicin<PERSON>t vienlaicīgas grupu diskusijas un debates. Apkopojiet viedok<PERSON>, balsojiet un panāciet vienprātību tāpat kā bezsaistē.", "app.containers.Admin.tools.workshopsImage": "Darbnīcas att<PERSON>", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Apsp<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "kopējais lie<PERSON> s<PERSON>ts <PERSON>ā", "app.containers.AdminPage.DashboardPage._blank": "nav zināms", "app.containers.AdminPage.DashboardPage.allGroups": "Visas grupas", "app.containers.AdminPage.DashboardPage.allProjects": "Visi projekti", "app.containers.AdminPage.DashboardPage.allTime": "<PERSON><PERSON> laiks", "app.containers.AdminPage.DashboardPage.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Lai novērtētu platformas lietotāju p<PERSON>, nepie<PERSON>šams pamatdatu kopums.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Drī<PERSON>mā", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "<PERSON><PERSON><PERSON><PERSON> mēs strādājam pie {fieldName} informācijas paneļa; tas būs drīzumā pieejams.", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# item is} other {# items are}} paslēpti šajā diagrammā. Nomainiet uz {tableViewLink}, lai redzētu visus datus.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} lietotāja reģistrācijai", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} no i<PERSON><PERSON><PERSON><PERSON> lietot<PERSON> skaita {total} ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vēl {numberOfHiddenItems}", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "<PERSON><PERSON><PERSON><PERSON>, nor<PERSON><PERSON><PERSON> pamat<PERSON>tu kopumu.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rād<PERSON>tājs:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "<PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, cik precīzi platformas lietotāju dati atspoguļo visu iedzīvotāju kopumu. Vairāk informācijas {reprezentativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "skatījums tabulā", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Pievienot vecuma grupu", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{vecums} un vecāki", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Vecuma grupa", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Nav iek<PERSON><PERSON>a vecuma grupa(-as) {upperBound} un vecāki.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Vecuma grupa {skaitl<PERSON>}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Vecuma grupas", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "un vecāki", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "<PERSON><PERSON><PERSON><PERSON> pie<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> visu", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "No", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Iestatīt vecuma grupas, lai tās atbilstu jūsu pamatdatu kopumam.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Interv<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Rediģēt vecuma grupas", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Šis vienums netiks aprēķināts.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Skatīt {numberOfHiddenItems} vairāk...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "<PERSON><PERSON><PERSON> (izvēles)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Vecuma grupas (dzimšanas gads)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Drī<PERSON>mā", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Pilnīgs", "app.containers.AdminPage.DashboardPage.components.Field.default": "Noklusējums", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "<PERSON><PERSON><PERSON><PERSON>, aizpildiet visas iespējotās iespējas vai atspējojiet iespējas, kuras vēlaties izslēgt no diagrammas. Jāaizpilda vismaz viena iespēja.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Nepilnīgs", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.options": "<PERSON>es<PERSON>ējas", "app.containers.AdminPage.DashboardPage.components.Field.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.saved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "<PERSON> sāktu ievad<PERSON>t p<PERSON>, vispi<PERSON> lū<PERSON><PERSON>, iesta<PERSON>t {setAgeGroupsLink}.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "note<PERSON>t vecuma grupas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> atbildes laiks: {days} dienas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "<PERSON><PERSON><PERSON><PERSON><PERSON> dienu skaits līdz atbildēšanai", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Ievades datu statuss", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "<PERSON><PERSON>des dati pēc statusa", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "<PERSON><PERSON><PERSON> datu skaits", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Atbildēša<PERSON> laiks", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Statuss", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Statuss mainīts", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Kopā", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Rediģēt pamatdatus", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "kā mēs aprēķinām pārstāv<PERSON>bas rādītājus", "app.containers.AdminPage.DashboardPage.continuousType": "Bez laika grafika", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ma", "app.containers.AdminPage.DashboardPage.customDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.day": "diena", "app.containers.AdminPage.DashboardPage.false": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.female": "sieviete", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "5 galvenie ievadītie dati pēc reak<PERSON>m", "app.containers.AdminPage.DashboardPage.fromTo": "no {no} līdz {līdz}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Informācijas paneļi platformā veiktajām darbībām", "app.containers.AdminPage.DashboardPage.helmetTitle": "Administrēt informācijas paneļa lapu", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> resursus, kas tiks rādīti pa projektiem", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> resursu, ko rādīt pa tagiem", "app.containers.AdminPage.DashboardPage.inputs1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "<PERSON>evadi pēc statusa", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Izvēlēties lietotāju grupu", "app.containers.AdminPage.DashboardPage.male": "v<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.month": "mēnesis", "app.containers.AdminPage.DashboardPage.noData": "Nav datu, ko parād<PERSON>t.", "app.containers.AdminPage.DashboardPage.noPhase": "Šim projektam nav izveidots neviens posms", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "To da<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>, kuri ir i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ievadd<PERSON>, reaģējuši vai komentējuši.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Nepatīk", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.DashboardPage.overview.management": "Vadī<PERSON>", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projekti un dalība", "app.containers.AdminPage.DashboardPage.overview.showLess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.overview.showMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.participationPerProject": "<PERSON><PERSON><PERSON> katrā projektā", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Dalība pa tagiem", "app.containers.AdminPage.DashboardPage.perPeriod": "Pa {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Iepriek<PERSON><PERSON><PERSON><PERSON><PERSON> 30 dienās", "app.containers.AdminPage.DashboardPage.previous90Days": "Iepriek<PERSON><PERSON><PERSON><PERSON><PERSON> 90 dienās", "app.containers.AdminPage.DashboardPage.previousWeek": "Iepriek<PERSON><PERSON><PERSON><PERSON> ned<PERSON>", "app.containers.AdminPage.DashboardPage.previousYear": "Iepriek<PERSON><PERSON><PERSON><PERSON> gadā", "app.containers.AdminPage.DashboardPage.projectType": "Projekta tips : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "<PERSON><PERSON> pamat<PERSON>tu kopums ir ne<PERSON>, lai aprēķinātu platformas lietotāju pārstāvību salīdzinājumā ar visu iedzīvotāju kopumu.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "<PERSON><PERSON><PERSON><PERSON>, nor<PERSON><PERSON><PERSON> pamat<PERSON>tu kopumu.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> mērā jūsu platformas lietotāji atspoguļo visu iedzīvotāju kopumu, pamatojoties uz datiem, kas apkopoti lietotāju reģistrācijas laikā. Vairāk informācijas {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> mērā jūsu platformas lietotāji atspoguļo visu iedzīvotāju kopumu, pamatojo<PERSON> uz datiem, kas apkopoti lietotāju reģistrācijas laikā.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "<PERSON><PERSON><PERSON> p<PERSON>rstāvī<PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Atgriezties uz informācijas paneli", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Pa<PERSON><PERSON>k netiek atbalstīts neviens no iespējotajiem reģistrācijas laukiem.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Šeit varat parādīt/noslēpt elementus informācijas panelī un ievadīt pamatdatus. Šeit būs redzami tikai iespējotie lauki {userRegistrationLink}.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Rediģēt pamatdatus", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "lietotāja reģistrācija", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.AdminPage.DashboardPage.resolutionday": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.resolutionmonth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.resolutionweek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.selectProject": "Izvēlēties projektu", "app.containers.AdminPage.DashboardPage.selectedProject": "pašreizējo projektu filtrs", "app.containers.AdminPage.DashboardPage.selectedTopic": "pašreizējo tagu filtrs", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "<PERSON><PERSON><PERSON><PERSON>, kas notiek jūsu <PERSON>.", "app.containers.AdminPage.DashboardPage.tabOverview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabReports": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Pārstāvība", "app.containers.AdminPage.DashboardPage.tabUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.timelineType": "Laika līnija", "app.containers.AdminPage.DashboardPage.titleDashboard": "Informāci<PERSON>is", "app.containers.AdminPage.DashboardPage.total": "Kopā", "app.containers.AdminPage.DashboardPage.totalForPeriod": "<PERSON><PERSON><PERSON> {period}", "app.containers.AdminPage.DashboardPage.true": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.unspecified": "nav precizēts", "app.containers.AdminPage.DashboardPage.users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Lietotā<PERSON> pēc ve<PERSON>a", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Lietotāji pēc ģeogrāfiskās zonas", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Lietotāji pēc d<PERSON>a", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Reģistrācijas", "app.containers.AdminPage.DashboardPage.week": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "<PERSON><PERSON><PERSON>, kā izvēlēties favicon attēlu: izvēlieties vienkāršu attēlu, jo parādītā attēla izmērs ir ļoti mazs. Attēlam jābūt saglabā<PERSON> kā <PERSON>, un tam jābūt kvadrātveida ar caurspīdīgu fonu (vai, ja <PERSON><PERSON><PERSON>, ar baltu fonu). Favicon vēlams iestatīt tikai vienu reizi, jo izmaiņu veikšanai nepieciešams tehniskais atbalsts.", "app.containers.AdminPage.FaviconPage.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "<PERSON>ut kas nogāja greizi. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Veiksmīgi!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "<PERSON><PERSON><PERSON> ve<PERSON> i<PERSON> ir saglab<PERSON>.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Mapju p<PERSON>rvaldnieki", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Mapju pārvaldnieki var rediģēt mapes aprakstu, mapē izveidot jaunus projektus un tiem ir projektu pārvaldības tiesības attiecībā uz visiem mapē esošajiem projektiem. Viņi nevar izdzēst projektus, un viņiem nav piekļuves projektiem, kas nav viņu mapē. Varat {projectManagementInfoCenterLink} atrast vairāk informācijas par projektu pārvaldības tiesībām.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Nav atrasta neviena atbilstība", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "vērsieties mūsu palīdzības centrā", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Mek<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.addToFolder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Arhivēts", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Dzēst šo mapi", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.draftStatus": "Melnraksts", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "<PERSON><PERSON><PERSON> mapei failus", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> par 50 Mb. Pievienotie faili tiks parād<PERSON>ti mapes lapā.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "<PERSON><PERSON><PERSON> mapē nav projektu. Atgriezieties galvenajā Projektu cilnē, lai izveidotu un pievienotu projektus.", "app.containers.AdminPage.FoldersEdit.folderName": "<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Virsraksta attēls", "app.containers.AdminPage.FoldersEdit.multilocError": "<PERSON><PERSON><PERSON><PERSON> valo<PERSON> ir j<PERSON><PERSON>lda visi teksta lauki.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Nav projektu, ko pievienot š<PERSON> mapei.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Mapes kartes attēls", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Atļaujas", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Mapes projekti", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Iestatījumi", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "<PERSON><PERSON><PERSON> mapē pievienotie projekti", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "<PERSON><PERSON><PERSON><PERSON>, kurus varat pievienot šai mapei", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Izvēlēties mapes statusu - \"melnraksts\", \"publicēts\" vai \"arhivēts\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Publicēts", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Izņemt no mapes", "app.containers.AdminPage.FoldersEdit.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "<PERSON>ut kas nogāja greizi. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Veiksmīgi!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "<PERSON><PERSON><PERSON> ve<PERSON> i<PERSON> ir saglab<PERSON>.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Īss apraksts", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "<PERSON><PERSON><PERSON> m<PERSON>", "app.containers.AdminPage.FoldersEdit.statusLabel": "Publikācijas statuss", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "<PERSON><PERSON><PERSON><PERSON> saistību starp projektiem, definēt vizu<PERSON>lo identitāti un dalīties ar informāciju.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "<PERSON><PERSON><PERSON><PERSON> saistību starp projektiem, definēt vizu<PERSON>lo identitāti un dalīties ar informāciju.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Jāaizpilda visi teksta lauki.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Nosa<PERSON>ms", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Izveidot jaunu mapi", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Iestatījumi", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "<PERSON><PERSON><PERSON><PERSON> mapi", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "<PERSON><PERSON><PERSON><PERSON> lielā reklāmkaroga attēlu un tekstu.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Iedvesmas centrs ir vieta, kur varat rast iedvesmu saviem projektiem, p<PERSON><PERSON>ū<PERSON>jot citu platformu projektus.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Rediģēt platformas lapas Par un Biežāk uzdotie jautājumi. Citas lapas, tostarp Par un Biežāk uzdotie jautājumi, var rediģēt cilnē {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Platformas politika", "app.containers.AdminPage.PagesEdition.privacy-policy": "Privātuma politika", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "<PERSON><PERSON><PERSON> un nosacījumi", "app.containers.AdminPage.Project.confirmation.description": "<PERSON><PERSON> nevar at<PERSON>t.", "app.containers.AdminPage.Project.confirmation.no": "Atcelt", "app.containers.AdminPage.Project.confirmation.title": "Vai esat pārl<PERSON>, ka vēlaties atiestatīt visus dalības datus?", "app.containers.AdminPage.Project.confirmation.yes": "<PERSON><PERSON><PERSON> da<PERSON> datu at<PERSON>", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON> atbildes, a<PERSON><PERSON><PERSON> atbildes, brīvprātīgie un pasākumu reģistrētāji. Balsošanas posmu gadījumā š<PERSON> darbība dzēs<PERSON>s balso<PERSON>, bet ne iespējas.", "app.containers.AdminPage.Project.data.title": "Izdzēst visus šā projekta dalības datus", "app.containers.AdminPage.Project.resetParticipationData": "<PERSON><PERSON><PERSON> da<PERSON> datu at<PERSON>", "app.containers.AdminPage.Project.settings.accessRights": "Piek<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.back": "Atpakaļ", "app.containers.AdminPage.Project.settings.data": "<PERSON><PERSON>", "app.containers.AdminPage.Project.settings.description": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.events": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.general": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.projectTags": "Projekta birkas", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Platformā esošo projektu saraksts", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Projektu informācijas panelis", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Izveidot jaunus projektus vai pārvaldīt esošos projektus.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projekti", "app.containers.AdminPage.ProjectDashboard.published": "Publicēts", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Aizvērt iestatījumu paneli", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Centrs", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Pilns platums", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Pogu i<PERSON>lī<PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Pogas teksts", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Ievadiet pogas tekstu", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Pogas tips", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Sekundārais", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Pogas URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Ievadiet pogas URL", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "<PERSON>ļ<PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Sākumlapas apraksts", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Redzams projekta kartē sākumlapā.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Redzams projekta lapā. Skaidri aprakstiet projekta būt<PERSON>bu, ko jūs sagaidāt no lietotājiem un ko viņi var sagaidīt no jums.", "app.containers.AdminPage.ProjectDescription.errorMessage": "<PERSON>ut kas nogāja greizi. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.AdminPage.ProjectDescription.preview": "Priekšskatījums", "app.containers.AdminPage.ProjectDescription.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "<PERSON><PERSON><PERSON> ve<PERSON> i<PERSON> ir saglab<PERSON>.", "app.containers.AdminPage.ProjectDescription.saved": "Saglabāts!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "<PERSON><PERSON><PERSON><PERSON>, kādu vēstījumu vēlaties nodot mērķauditorijai. Rediģēt projektu un papildiniet to ar attēliem, videoklipiem, failu pielikumiem... Šī informācija palīdz apmeklētājiem saprast jūsu projekta būtību.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Projekta apraksts", "app.containers.AdminPage.ProjectDescription.whiteSpace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> augstums", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Vidēja", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Maz<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Atcelt rediģēšanu", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "<PERSON><PERSON><PERSON> viduspunkta noklusējuma platums. Pieņem vērtību no -90 līdz 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "<PERSON><PERSON><PERSON> viduspunkta noklusējuma garums. <PERSON><PERSON><PERSON> vērtību no -90 līdz 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Rediģēt kartes slāni", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Rediģēt slāni", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "<PERSON>ut kas nogāja greizi. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.AdminPage.ProjectEdit.MapTab.here": "š<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Importēt GeoJSON failu", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Noklusējuma platums", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Visos slāņa elementos tiks izmantota šī krāsa. Tā arī nomainīs jebkuru pašreiz izmantoto stilu jūsu GeoJSON failā.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Marķiera ikona", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "<PERSON>a v<PERSON>, iz<PERSON><PERSON><PERSON><PERSON> ikonu, kas parādīta marķieriem. Noklikšķiniet uz {url}, lai skatītu izvēlei pieejamo ikonu sarakstu.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "<PERSON><PERSON> s<PERSON> no<PERSON> ir redzams kartes leģendā", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>a padoms", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Šis teksts redzams kā ekrāna padoms, virzo<PERSON> pāri slāņa elementiem kartē.", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> mēs atbalstām GeoJSON failus. Las<PERSON> {supportArticle}, lai u<PERSON>, kā konvertēt un vizuāli pielā<PERSON> kar<PERSON>.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Noklus<PERSON><PERSON><PERSON> garums", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "<PERSON><PERSON><PERSON> viduspunkts un tālummaiņa", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Kartes noklusējuma centra punkts un tālummaiņas līmenis. Manuāli pielāgojiet tālāk norādītās vērtības vai noklikšķiniet uz pogas {button} kartes kreisajā apakšējā stūrī, lai saglabātu pašreizējo kartes centra punktu un tālummaiņas līmeni kā noklusējuma vērtības.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Pielāgojiet kartes skatu, tostarp augšupielādējiet un stilizējiet kartes slāņus un iestatiet kartes viduspunktu un tālummaiņas līmeni.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Kartes konfigurācija pašlaik ir kopīga visiem posmiem, nevarat izveidot dažādas kartes konfigurācijas katram posmam.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Noņemt slāni", "app.containers.AdminPage.ProjectEdit.MapTab.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "<PERSON><PERSON><PERSON> raksts", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Nenosaukts slānis", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "<PERSON><PERSON><PERSON> l<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "<PERSON><PERSON>s no<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>as līmenis. <PERSON><PERSON><PERSON> vērtību no 1 līdz 17, kur 1 ir pilnī<PERSON>ā att<PERSON>lin<PERSON>ta (redzama visa pasaule) un 17 ir pilnībā pietuvināta (redzami bloki un ēkas).", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonimizēt visus lietotāja datus", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Visi aptaujas dati no lietotājiem tiks anonīmi pirms to reģistrēšanas.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Lietotāji<PERSON> j<PERSON> būs j<PERSON><PERSON><PERSON><PERSON><PERSON> prasības piekļuves cilnē \"Piekļuves tiesības\". Lietotāja profila dati nebūs pieejami apsekojuma datu eksportā.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "<PERSON>a iespējos<PERSON>, lietotāja reģistrācijas lauki tiks parādīti kā pēdējā aptaujas lapa, nevis kā daļa no reģistrēšanās procesa.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demogrā<PERSON><PERSON><PERSON> lauki a<PERSON> ve<PERSON>lap<PERSON>", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "<PERSON><PERSON><PERSON><PERSON><PERSON> demo<PERSON><PERSON> lauku<PERSON> aptauj<PERSON>?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "<PERSON><PERSON><PERSON><PERSON> par to, kā darb<PERSON> auto<PERSON> k<PERSON>, lasiet <PERSON> r<PERSON>.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Automātisk<PERSON><PERSON> k<PERSON> rezultāti", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Pēc posma beigām balsošanas rezultāti tiek paziņoti platformā un pa e-pastu dalībniekiem. Tas nodrošina pārredzamību pēc noklusējuma.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Re<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "<PERSON><PERSON>not atbildes iespēju", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Atcelt", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Atcelt", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Atcelt", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Atbildes rediģēšanas iespēja", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Atbildes iespēju <PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Rediģēt atbilžu iespējas", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Rediģēt j<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Eksportēt aptaujas rezultātus", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON> skaits pārsniedz iespēju skaitu.", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Iespēju nav", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "<PERSON>isi<PERSON> j<PERSON> jāb<PERSON>t atbilžu variantiem", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Tikai viena iespēja", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Aptaujas respondentiem ir tikai viena izvēle", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Pārvaldiet atbilžu iespējas: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Šeit varat izveidot aptaujas jautājumus, iesta<PERSON><PERSON><PERSON> atbilžu variantus, no kuriem dal<PERSON>nieki var izvēlēties katrā jautājumā, i<PERSON><PERSON><PERSON>, vai dalībnieki var izvēlēties tikai vienu atbilžu variantu (viena izvēle) vai vairākus atbilžu variantus (vairākas izvēles), kā arī eksportēt aptaujas rezultātus. Vienā aptaujā varat būt vairāki aptaujas jautājumi.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Viena izvēle", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Aptauju iestatījumi un rezultāti", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Nepareiza maksim<PERSON> vērt<PERSON>ba", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Importēt", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON><PERSON>, pie<PERSON><PERSON> tagus vai iekopēt ierakstus nākamajā projekta posmā.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Pārvald<PERSON> p<PERSON>, snied<PERSON>t atsauksmes un piešķiriet tēmas.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "<PERSON><PERSON>des datu p<PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Rezultātu kopīgošana ir izslēgta.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "<PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON> b<PERSON> rezultāti netik<PERSON> k<PERSON>, ja vien tos nemain<PERSON>t fāzes iestatīju<PERSON>.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "<PERSON>ie rezultāti tiks automātiski kopīgoti pēc posma beigām. Mainiet šī posma beigu datumu, lai mainītu rezultātu kopīgošanas laiku.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Eksportēt apsekojuma rezultātus (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Šeit varat lejupielādēt šajā projektā veiktās(-o) Typeform apsekojuma(-u) rezultātus Excel faila formātā.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>a", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "<PERSON><PERSON><PERSON><PERSON> rezultāti", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Iepazīties ar apsekojuma atbildēm", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Vai esat pārlie<PERSON>āts?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> to, lai p<PERSON>, kā<PERSON> ir prasības brīvprātīgajiem un ko viņi var sagaid<PERSON>t.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo ve<PERSON> ir <PERSON>.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Nosa<PERSON>ms", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Rediģēt", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Pasākums ir rīcība vai darbība, kur<PERSON> dal<PERSON> var iesaistīties kā brīvprātīgie.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Rediģēt pasākumu", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Eksportēt brīvprātīgos", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Pasākums ir rīcība vai darbība, kur<PERSON> dal<PERSON> var iesaistīties kā brīvprātīgie.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "<PERSON><PERSON><PERSON> pas<PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "<PERSON>eit varat iestat<PERSON>t p<PERSON>, kuro<PERSON> lieto<PERSON> var brīvprātīgi iesaistīties, un lejupielādēt brīvprātīgos.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> darbs", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {nav brīvprātīgo} one {# brīvprātīgais} other {# brīvprātīgie}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "bud<PERSON><PERSON> piešķīrums", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Piešķiriet variantiem budžetu un aiciniet dalībniekus izvēlēties vēlamos variantus, kas iekļ<PERSON> kopējā budžetā.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Budžeta piešķīrums", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Ļaujot lietotā<PERSON><PERSON> koment<PERSON>t, b<PERSON><PERSON><PERSON><PERSON> process var būt neobjektīvs.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Iespēju no<PERSON>lusējuma skats", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Darbības <PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> papildu darbības lietotāji var veikt.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "<PERSON><PERSON><PERSON><PERSON> summa", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "<PERSON>a tas paliks tuk<PERSON>, pēc nok<PERSON><PERSON> būs \"balsot\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Uzziniet v<PERSON><PERSON>k par to, kad <PERSON> <b> {voteTypeDescription} </b> , mūsu vietnē {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> balsu skaits katrai iespējai", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>u s<PERSON>ts", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Varat ierobežot lietotāju kopējo balsojumu skaitu (par katru opciju var nobalsot ne vairāk kā vienu reizi).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "vair<PERSON><PERSON> balsis par katru iespēju", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Lietotājiem tiek piešķirts ž<PERSON>u s<PERSON>, ko sadal<PERSON>t starp iespējām.", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "<PERSON><PERSON><PERSON><PERSON> balsis par katru iespēju", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Balsu skaits uz lietotāju", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Iespēju analīzes pārskats", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "<PERSON><PERSON><PERSON><PERSON><PERSON>, par kurām balsot", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "viena balss par katru iespēju", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Lietotāji var izvēlēties apstiprināt jebkuru no iespējām.", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Viena balss par katru iespēju", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Žetons", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Neierobežots", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Kā būtu saucams balsojums?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, og<PERSON><PERSON><PERSON><PERSON> kred<PERSON>...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, punkts, oglekļa kredīts...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Balsojums", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Katrai balsošanas metodei ir dažādas iepriekšējas konfigurācijas.", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> metode", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "<PERSON><PERSON><PERSON><PERSON><PERSON> metode nosa<PERSON> note<PERSON>, k<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> balso.", "app.containers.AdminPage.ProjectEdit.addNewInput": "<PERSON><PERSON><PERSON> ievades datus", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Varat pievienot projektu mapē tagad vai arī to i<PERSON><PERSON><PERSON>t vēlāk projekta iestatījumos.", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Projekta tagi", "app.containers.AdminPage.ProjectEdit.altText": "Alt teksts", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "To ies<PERSON><PERSON><PERSON><PERSON><PERSON>, nav iesp<PERSON><PERSON><PERSON>z<PERSON>, kur<PERSON> par ko balsojis. Lietotājiem joprojām ir nepiecieša<PERSON> konts, un viņi var balsot tikai vienu reizi.", "app.containers.AdminPage.ProjectEdit.approved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archived": "Arhivēts", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Arhivētie projekti joprojām ir <PERSON>, bet tajos vairs nav iespējams piedalīties.", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Arhivēts", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "<PERSON><PERSON> ap<PERSON><PERSON> ne<PERSON>, jo tas tie<PERSON>, lai parādītu projektus nākamajā(-ās) pielāgotajā(-ās) lapā(-ēs). Pirms apgabala dzēšanas jums būs jāatvieno apgabala saite no lapas vai jāizdzēš lapa.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Visas zonas", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Projekts tiks parādīts katrā zonas filtrā.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Zonas filtrs", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Sākumlapā projektus var filtrēt, izmantojot zonas. Zonas var iestatīt {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "š<PERSON>", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Nav konkrētas zonas", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> zona<PERSON>, projekts neb<PERSON>s red<PERSON>.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Atlase", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Projekts būs redzams izvēlētajā(-os) zonas(-u) filtrā(-os).", "app.containers.AdminPage.ProjectEdit.cardDisplay": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Pievienojiet apsekojuma saturu", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Ir sākuši pienākt priekšlikumi šai aptaujai. Apsekojuma izmaiņu rezultātā eksportētajos failos var tikt zaudēti dati un tie var būt nepilnīgi.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON> sagla<PERSON>ta", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Izvēlieties balsošanas metodi un ļaujiet lietotājiem noteikt prioritātes starp vairākām dažādām iespējām.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Veikt b<PERSON> vai prioritāš<PERSON>", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Izveidot projektu no veidnes", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Izveidot ārēju <PERSON>ko<PERSON>mu", "app.containers.AdminPage.ProjectEdit.createInput": "<PERSON><PERSON><PERSON> j<PERSON> i<PERSON>", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Izveidot platformā veiktu apsekojumu", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> mūsu <PERSON>.", "app.containers.AdminPage.ProjectEdit.createPoll": "Izveidot aptauju", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Izveidot anketu ar vairākām atbilžu iespējām.", "app.containers.AdminPage.ProjectEdit.createProject": "Jauns projekts", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Iestrādāt Typeform, Google Form, Enalyzer, SurveyXact, Qualtrics, SmartSurvey, Snap Survey vai Microsoft Forms apsekojumu.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "<PERSON><PERSON><PERSON> varat iestatīt no<PERSON> se<PERSON>, kādā ieraksti tiks rādīti galvenajā projekta lapā.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Šķirošana", "app.containers.AdminPage.ProjectEdit.departments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.descriptionTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "Tas ieslēgs vai izslēgs nepatīk, bet patika joprojām būs iespējota. Mēs iesakām atstāt šo opciju atspējotu, ja vien neveicat iespēju analī<PERSON>.", "app.containers.AdminPage.ProjectEdit.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Nepatiku skaits uz vienu dalībnieku", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Atsauksmes par dokumentu vākšana", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Ievietojiet interaktīvu PDF failu un apkopojiet komentārus un atsauksmes, izmantojot Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Projektu projekti ir slēpti visiem cilvēkiem, iz<PERSON><PERSON>ot administratorus un projektu vadītājus.", "app.containers.AdminPage.ProjectEdit.draft": "Melnraksts", "app.containers.AdminPage.ProjectEdit.draftStatus": "Melnraksts", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Rediģēt", "app.containers.AdminPage.ProjectEdit.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> līdzdalības darbības lietotāji var veikt.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne vairāk kā 50 MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "<PERSON><PERSON><PERSON> nedr<PERSON><PERSON><PERSON> b<PERSON><PERSON> par 50 Mb. Pievienotie faili tiks parādīti projekta informācijas lapā.", "app.containers.AdminPage.ProjectEdit.filesTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON> br<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "<PERSON><PERSON><PERSON><PERSON>t dalībniekus brīvprātīgi iesaistīties aktivitātēs un pasākumos.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "Kā mapju pārvaldnieks varat izvēlēties mapi, kad veido<PERSON>t projektu, bet pēc tam to var mainīt tikai administrators.", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Mapes kartes attēla alternatīvais teksts", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Izvēlieties mapi, kur<PERSON> pievienot šo projektu.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Pielāgotais saturs", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Ir sākusies š<PERSON> veidlap<PERSON> iesniegša<PERSON>. Veicot izmaiņ<PERSON> veidlap<PERSON>, eksportētajos fail<PERSON> var tikt zaudēti dati un dati var būt nepiln<PERSON>gi.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Veidlapa veiksmīgi saglab<PERSON>ta", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Aptaujas beigas", "app.containers.AdminPage.ProjectEdit.fromATemplate": "No veidnes", "app.containers.AdminPage.ProjectEdit.generalTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Virsraksta attēla alt teksts", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Virsraksta attēls", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "JAUNUMS", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Sniedziet informāciju lietotājiem vai izmantojiet pārskatu veidotāju, lai kopīgotu iepriekšējo posmu rezultātus.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Informācijas vai rezultātu kopīgošana", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "<PERSON><PERSON><PERSON><PERSON> dati un atsauksmes", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Veidot vai ap<PERSON>pot ievaddatus, reakcijas un/vai komentārus. Izvēlieties dažādus ievades veidus: ideju v<PERSON>, i<PERSON><PERSON><PERSON><PERSON>, jaut<PERSON><PERSON><PERSON> un atbildes, problēmu identificēšana un citi.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Kas ir atbildīgs par ierakstu apstrādi?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Visi jaunie ievades dati šajā projektā tiks piešķirti šai personai. Personu, kurai tie tiek piešķirti, var mainīt {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Ierakstu komentē<PERSON>na", "app.containers.AdminPage.ProjectEdit.inputFormTab": "<PERSON><PERSON><PERSON> datu veid<PERSON>a", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "ievades datu p<PERSON>", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "<PERSON><PERSON>des datu p<PERSON>", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Ierakstu iesniegšana", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reaģēšana uz ievades datiem", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Noklusēju<PERSON> skat<PERSON>", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Izvēlēties noklusējuma skatījumu ievades datu parādīšanai: kartes režģa skatā vai kā spraudītes kartē. Dalībnieki var manuāli pārslēgties starp abiem skatījumiem.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Iedvesmas centrs", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Iebildēt Konveio URL", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Number of likes per participant", "app.containers.AdminPage.ProjectEdit.limited": "Ierobežoti", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> ve<PERSON>", "app.containers.AdminPage.ProjectEdit.mapDisplay": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.mapTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxDislikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxLikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> \"pat<PERSON>k", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> balsu skaitam katrā variantā jābūt mazākam vai vienādam ar kopējo balsu skaitu.", "app.containers.AdminPage.ProjectEdit.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> savu <PERSON>, da<PERSON><PERSON><PERSON><PERSON><PERSON> nedrī<PERSON>t pā<PERSON>gt <PERSON>o bud<PERSON>.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimums", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Pie<PERSON>ērot dalībniekiem prasību izpildīt minimālo budžetu sava groza iesniegšanai (ievadiet \"0\", ja nevēlaties noteikt minimālo budžetu).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "vērsieties mūsu palīdzības centrā", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Kas ir projektu vad<PERSON>?", "app.containers.AdminPage.ProjectEdit.moreDetails": "<PERSON>īk<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Nepieciešama iedves<PERSON>? Izpētiet līdzīgus projektus citās pilsētās vietnē {inspirationHubLink}.", "app.containers.AdminPage.ProjectEdit.newContribution": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newIdea": "<PERSON><PERSON><PERSON> ideja", "app.containers.AdminPage.ProjectEdit.newInitiative": "Iniciatī<PERSON>", "app.containers.AdminPage.ProjectEdit.newIssue": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newPetition": "Pievienojiet lūgumrakstu", "app.containers.AdminPage.ProjectEdit.newProject": "Jauns projekts", "app.containers.AdminPage.ProjectEdit.newProposal": "Pievienot priekšlikumu", "app.containers.AdminPage.ProjectEdit.newQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Summa nav derīga", "app.containers.AdminPage.ProjectEdit.noFolder": "Nav mapes", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "- Nav mapes -", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Nav atrastas veidnes", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "<PERSON><PERSON><PERSON><PERSON>, ievadiet projekta virsrakstu", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "<PERSON><PERSON><PERSON> nav derīgs", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "<PERSON><PERSON><PERSON> tikai <PERSON>", "app.containers.AdminPage.ProjectEdit.optionNo": "Nē", "app.containers.AdminPage.ProjectEdit.optionYes": "Jā (izvēlieties mapi)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Līdzdal<PERSON><PERSON> lī<PERSON>", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Ko jūs vēlaties darīt?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kā lietotāji var piedalīties.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "<PERSON><PERSON><PERSON>, kas var veikt katru da<PERSON>, un uzdot papildu jautā<PERSON><PERSON>, lai i<PERSON><PERSON><PERSON> vair<PERSON> informā<PERSON>.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Pras<PERSON><PERSON> dalī<PERSON>niekiem un jautājumi", "app.containers.AdminPage.ProjectEdit.pendingReview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Piek<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Piek<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.phaseEmails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.pollTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Projekta kartes attēls", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "<PERSON><PERSON> attēls ir daļa no projekta kartes; karte<PERSON>, kas apkopo projektu un tiek parādīta, <PERSON><PERSON><PERSON><PERSON>, sāku<PERSON><PERSON>ā.\n\n    Plašāku informāciju par ieteicamo attēlu izšķirtspēju skatīt {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Mapes", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "<PERSON><PERSON> attēls ir redzams projekta lapas augšpusē.\n\n    Plašāku informāciju par ieteicamo attēlu izšķirtspēju skatīt {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Projekta kartes attēla alternatīvais teksts", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Sniedziet īsu attēla aprakstu lietotājiem ar redzes traucējumiem. Tas palīdz ekrāna las<PERSON>t<PERSON>em saprast, kas attēlā ir attēlots.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Projektu vadība", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Projektu vadītāji var rediģēt projektus, p<PERSON><PERSON><PERSON><PERSON>t ierakstus un sūtīt e-pastus dalībniekiem. Vairāk informācijas par projektu vadītājiem piešķirtajām tiesībām šeit {moderationInfoCenterLink}.", "app.containers.AdminPage.ProjectEdit.projectName": "Projekta no<PERSON><PERSON>ms", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Projekta tips", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Projektiem ar laika grafiku ir skaidri noteikts sākums un beigas, un tiem var būt da<PERSON>di posmi. Projekti bez laika grafika ir nepārtraukti.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Projekta tipu vēl<PERSON>k nevar <PERSON>t.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "<PERSON><PERSON><PERSON>, lai projekts būtu ne<PERSON>s noteiktiem lietot<PERSON>.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "<PERSON>jekta <PERSON>", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Vai meklējat projekta statusu? Tagad to jeb<PERSON><PERSON><PERSON> laikā varat mainīt tieši no projekta lapas galvenes.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Publicētie projekti ir redzami visiem vai grupas apakšgrupai, ja tā ir izvēlēta.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Publicēts", "app.containers.AdminPage.ProjectEdit.purposes": "Mērķi", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "<PERSON><PERSON><PERSON> datu at<PERSON>", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Saglabā<PERSON><PERSON>, notika k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "app.containers.AdminPage.ProjectEdit.saveProject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Veiksmīgi!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "<PERSON><PERSON><PERSON> veidlapa ir saglabāta!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "app.containers.AdminPage.ProjectEdit.selectGroups": "Izvēlēties grupu(-as)", "app.containers.AdminPage.ProjectEdit.setup": "Iestatīšana", "app.containers.AdminPage.ProjectEdit.shareInformation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Survey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Iestatīt un personalizēt projektu.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "apmeklējiet mūsu atbalsta centru", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Pievienojiet apsekojuma saturu", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Atcelt", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# izvēle} one {# izvēle} other {# izvēle}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "Jā, es gribu aizbraukt", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "Rediģēt", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Ir sākuši pienākt iesniegumi šai aptaujai. Apsekojuma izmaiņu rezultātā eksportētajos failos var tikt zaudēti dati un tie var būt nepilnīgi.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Atgriezties", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Importēt", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Importēt", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI kopsavilkumus ī<PERSON><PERSON><PERSON> atbil<PERSON>, gar<PERSON><PERSON> atbildes un pēcaptaujas j<PERSON>m, kas sa<PERSON><PERSON>ti ar <PERSON> skal<PERSON>, var apskatīt AI cilnē kreisajā sānjoslā.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "<PERSON><PERSON><PERSON><PERSON> skala", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrica", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Garā <PERSON>bilde", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Vairākas izvē<PERSON> - izvēlieties daudzus", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Attēlu izvēle - izvēlieties daudzus", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "<PERSON><PERSON><PERSON> i<PERSON>s", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Aptaujas atbildes vēl nav saņemtas", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Atvērts atbilžu saņemšanai", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Atvērts atbilžu saņemšanai", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Pēc izvēles", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Ja loģika nav pievienota, apsekojums norisināsies saskaņā ar parasto gaitu. Ja gan lapā, gan tās jautājumos ir loģika, priek<PERSON><PERSON>a tiks dota jautājumu loģikai. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka tas atbilst jūsu iecerētajai aptaujas plūsmai. <PERSON> i<PERSON> vair<PERSON>k inform<PERSON>, apmeklējiet vietni {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.survey.point": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Vai esat pārl<PERSON>, ka vēlaties doties prom?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Pašreizējās izmaiņas netiks saglabātas.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Reitings", "app.containers.AdminPage.ProjectEdit.survey.rating": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.required2": "Nepieciešams", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {atbildes} one {atbildes} other {atbildes}}atbildes ~ ~ atbildes", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# atbildes} one {# atbilde} other {# atbildes}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Vairākas izvēles - izvēlieties vienu", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Noskaņojuma lineārā skala", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> au<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON> sagla<PERSON>ta", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Aptaujas atbildes", "app.containers.AdminPage.ProjectEdit.survey.text2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "<PERSON><PERSON><PERSON> {count} atbildes", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Iestrādāt URL", "app.containers.AdminPage.ProjectEdit.surveyService": "<PERSON><PERSON>kal<PERSON>šana", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Vairāk informācijas par to, kā iestrā<PERSON><PERSON><PERSON>, varat atrast {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "š<PERSON>", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Apsekojums Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "<PERSON>o birku ne<PERSON>, jo tā tie<PERSON>, lai parādītu projektus nākamajā(-ās) pielāgotajā(-ās) lapā(-ēs). \nPirms tagu var dzēst, jums būs jā<PERSON><PERSON><PERSON> tags no lapas vai jāizdz<PERSON>š lapa.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Vispārīgi projekta iestatījumi", "app.containers.AdminPage.ProjectEdit.titleLabel": "Nosa<PERSON>ms", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> īsu, saisto<PERSON>u un skaidru virsrakstu. <PERSON>s būs redzams nolaižamajā pārskatā un projekta kartītēs sākumlapā.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Tagi", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Izvēlieties {topicsCopy} šim projektam. Lietotāji to<PERSON> var izmantot projektu filtrēšanai.", "app.containers.AdminPage.ProjectEdit.totalBudget": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Tendences", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Nepiešķirts", "app.containers.AdminPage.ProjectEdit.unlimited": "Neierobežots", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "<PERSON><PERSON><PERSON><PERSON><PERSON> projektu", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> darbs", "app.containers.AdminPage.ProjectEdit.voteTermError": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> j<PERSON><PERSON> b<PERSON>", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# grupas var apskatīt} one {# grupa var skatīt} other {# grupas var apskatīt}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.additionalInformation": "<PERSON><PERSON><PERSON><PERSON> informā<PERSON>", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adrese 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> norises vietas adrese", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adrese 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "piem., <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, num<PERSON>, <PERSON>ka", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Papildu informācija par adresi, kas varētu palīdzēt identificēt atrašanās vietu, pie<PERSON><PERSON><PERSON>, <PERSON><PERSON>, stāva numurs utt.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "app.containers.AdminPage.ProjectEvents.customButtonLink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Pievienojiet saiti uz ārējo URL (piem., <PERSON><PERSON><PERSON><PERSON><PERSON> dienesta vai biļešu pārdoša<PERSON> tīmekļa vietni). <PERSON><PERSON> to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tiks atcelta noklusējuma apmeklējuma pogas uzvedība.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Pielāgotais pogas teksts", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Iestatiet pogas tekstam vērt<PERSON>, kas nav \"Reģistrēties\", ja ir iestatīts ārējais URL.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Sā<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Beigas", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Vai esat pārl<PERSON>cināts, ka vēlaties dzēst šo notikumu? Šo darbību nav iespējams atcelt!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Notikuma apraksts", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Rediģēt", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Rediģēt notikumu", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "<PERSON> nosūtītu e-pastu reģistrētājiem tieši no platformas, administratoriem ir jāizveido lietotāju grupa cilnē {userTabLink} . {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "<PERSON>ik<PERSON><PERSON> datumi", "app.containers.AdminPage.ProjectEvents.eventImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> attēla alternatīvais teksts", "app.containers.AdminPage.ProjectEvents.eventLocation": "Pasā<PERSON><PERSON> vieta", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Eksporta reģistrētāji", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne vairāk kā 50 MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Pielikumi ir redzami zem notikuma apraksta.", "app.containers.AdminPage.ProjectEvents.locationLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēto personu skaits", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Izveidot jaunu notikumu", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Tiešsaistes saite uz pasākumu", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "<PERSON>a jūsu pasākums ir pieejams tiešsaistē, pievienojiet saiti uz to šeit.", "app.containers.AdminPage.ProjectEvents.preview": "Priekšskatījums", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Precizēt kartes atrašanās vietu", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Precizēt atrašanās vietu kartē", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "<PERSON><PERSON><PERSON>, kur ir redzams jūsu notikuma atrašanās vietas marķieris, noklikšķinot uz zemāk redzamās kartes.", "app.containers.AdminPage.ProjectEvents.register": "Reģistrēties", "app.containers.AdminPage.ProjectEvents.registerButton": "Reģistrēties poga", "app.containers.AdminPage.ProjectEvents.registrant": "reģistrētājs", "app.containers.AdminPage.ProjectEvents.registrants": "reģistrētāji", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Reģistrācijas ierobežojums", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "<PERSON><PERSON><PERSON>m saglab<PERSON>t jūsu veiktās i<PERSON>, l<PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Veiksmīgi!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "<PERSON><PERSON><PERSON> ve<PERSON> i<PERSON> ir saglab<PERSON>.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "<PERSON><PERSON><PERSON> viet<PERSON> me<PERSON>", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Sasaistiet gaidāmos pasākumus ar šo projektu un parādiet tos projekta notikumu lapā.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Virsraksts un datumi", "app.containers.AdminPage.ProjectEvents.titleEvents": "Projekta notikumi", "app.containers.AdminPage.ProjectEvents.titleLabel": "Not<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Motivācijas pogas sasaiste ar <PERSON><PERSON><PERSON>jo <PERSON>", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Pēc noklusējuma tiks parādīta platformā esošā notikuma reģistrācijas poga, kas ļauj lietotājiem reģistrēties notikumam. To var mainīt, lai tā vietā izveidotu saiti uz ārējo URL.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Ierobežot pasākuma dal<PERSON> skaitu", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Iestatiet maks<PERSON><PERSON><PERSON> notik<PERSON> dalībnieku skaitu. Ja limits tiks sasniegts, turpmākas reģistrācijas netiks pieņ<PERSON>.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "<PERSON><PERSON><PERSON> projektam", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Pievienojiet failus no šī saraksta savam projektam, posmiem un notikumiem.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Failu pie<PERSON> kā kontekstu programmai Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Pievienojiet failus savam Sensemaking projektam, lai sniegtu kontekstu un ieskatu.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "<PERSON><PERSON><PERSON><PERSON><PERSON> gaidāms", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Sinhronizējiet a<PERSON>, augšupielādējiet intervijas un ļaujiet mākslīgajam intelektam savienot visus jūsu datus.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "AI izmantošana failu analīzei", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> u. c.", "app.containers.AdminPage.ProjectFiles.addFiles": "<PERSON><PERSON><PERSON> failus", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> intelektu darbināms ieskats", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>, lai atkl<PERSON><PERSON> galvenos tematus.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Ļaujiet veikt šo failu pad<PERSON>, i<PERSON><PERSON><PERSON><PERSON> mākslīgā intelekta apstrādi.", "app.containers.AdminPage.ProjectFiles.askButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Kategorija", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Izvēlieties failus", "app.containers.AdminPage.ProjectFiles.close": "Aizvērt", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Apstipriniet un augšupielādējiet", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Vai esat pā<PERSON>, ka vēlaties dzēst šo failu?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "<PERSON><PERSON><PERSON> i<PERSON>t marķējuma failu.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Nevar ielādēt CSV priekšskatījumu.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "CSV priekšskatījumos tiek parādītas ne vairāk kā 50 rindas.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV fails ir pār<PERSON><PERSON> liels, lai veiktu priekšskatījumu.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "<PERSON><PERSON><PERSON><PERSON> failu", "app.containers.AdminPage.ProjectFiles.description": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.done": "Gatavs", "app.containers.AdminPage.ProjectFiles.downloadFile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pilnu failu", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Velciet un nometiet šeit jebkurus failus vai", "app.containers.AdminPage.ProjectFiles.editFile": "Rediģēt failu", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "<PERSON><PERSON>a nosauku<PERSON>ā nedrī<PERSON>t būt punkts.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "<PERSON><PERSON><PERSON> no<PERSON> ir obligāts.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Priekšskatījums", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "<PERSON><PERSON> <PERSON> netiks <PERSON>, jo tas pā<PERSON><PERSON><PERSON><PERSON> maks<PERSON><PERSON><PERSON> ierobežojumu - 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Visi faili augšupielādēti ve<PERSON>gi", "app.containers.AdminPage.ProjectFiles.generatingPreview": "Priekšskatījuma ģenerēšana...", "app.containers.AdminPage.ProjectFiles.info_sheet": "Informācija", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "<PERSON><PERSON><PERSON><PERSON>, WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Audio intervijas, rātsnama ieraksti", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Piemēram, PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, informatīvie dokumenti", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "Piemēram, PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.interview": "Intervija", "app.containers.AdminPage.ProjectFiles.maxFilesError": "V<PERSON><PERSON><PERSON> varat augšup<PERSON>d<PERSON>t ne vairāk kā {maxFiles} failus.", "app.containers.AdminPage.ProjectFiles.meeting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.noFilesFound": "<PERSON><PERSON>i nav atrasti.", "app.containers.AdminPage.ProjectFiles.other": "Citi", "app.containers.AdminPage.ProjectFiles.policy": "Politika", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Š<PERSON> faila tipa priekšskatīšana vēl nav atbalstīta.", "app.containers.AdminPage.ProjectFiles.report": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.retryUpload": "<PERSON><PERSON><PERSON><PERSON><PERSON> aug<PERSON>", "app.containers.AdminPage.ProjectFiles.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "<PERSON><PERSON>i ir ve<PERSON><PERSON>.", "app.containers.AdminPage.ProjectFiles.searchFiles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> faili", "app.containers.AdminPage.ProjectFiles.selectFileType": "Faila tips", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Stratēģiskais plāns", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "V<PERSON><PERSON><PERSON> varat augšup<PERSON>d<PERSON>t ne vairāk kā {maxFiles} failus.", "app.containers.AdminPage.ProjectFiles.unknown": "<PERSON><PERSON>inā<PERSON>", "app.containers.AdminPage.ProjectFiles.upload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# fails} other {# faili}} veik<PERSON><PERSON><PERSON> aug<PERSON>, {numberOfErrors, plural, one {# kļūda} other {# kļūdas}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "<PERSON><PERSON><PERSON><PERSON> failu", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "<PERSON><PERSON><PERSON><PERSON> visus laukus", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Lau<PERSON> a<PERSON>ksts", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "<PERSON><PERSON><PERSON> ve<PERSON> rediģēšana", "app.containers.AdminPage.ProjectIdeaForm.enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "<PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "<PERSON>ut kas nogāja greizi. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus laukus", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "<PERSON><PERSON><PERSON> datu veid<PERSON>a", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "<PERSON><PERSON><PERSON><PERSON>, kāda informācija ir j<PERSON>, pie<PERSON><PERSON><PERSON><PERSON> aprakstus vai nor<PERSON>, lai palī<PERSON><PERSON><PERSON><PERSON> da<PERSON><PERSON><PERSON><PERSON> sniegt at<PERSON>, un nor<PERSON><PERSON>t, vai katrs lauks ir izvēles vai obligāts.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "<PERSON><PERSON><PERSON><PERSON>, kāda informācija ir j<PERSON>, pie<PERSON><PERSON><PERSON><PERSON> aprakstus vai nor<PERSON>, kas pal<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sniegt at<PERSON>, un norādiet katram laukam, vai tas ir izvēles vai obligāts.", "app.containers.AdminPage.ProjectIdeaForm.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "<PERSON><PERSON> lauk<PERSON> ir j<PERSON>.", "app.containers.AdminPage.ProjectIdeaForm.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "<PERSON><PERSON><PERSON> ve<PERSON> i<PERSON> ir saglab<PERSON>.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Saglabāts!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automatizēti e-pasti", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Varat konfigurēt e-pasta zi<PERSON>, kas tiek aktivizēti fāžu lī<PERSON>.", "app.containers.AdminPage.ProjectTimeline.datesLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Veiciet aptauju", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Vai esat pārl<PERSON>, ka vēlaties dzēst šo posmu?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Posma apraksts", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Rediģēšanas posms", "app.containers.AdminPage.ProjectTimeline.endDate": "Beigu datums", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Beigu datums:", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne vairāk kā 50 MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Izveidot jaunu posmu", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "<PERSON><PERSON> posmam nav iepriekš noteikta beigu datuma.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "<PERSON><PERSON><PERSON> meto<PERSON>u rezultātu kop<PERSON> (<PERSON><PERSON><PERSON><PERSON>, bals<PERSON><PERSON><PERSON> rezultātu kopīgo<PERSON>na) netiks aktivizēta, kam<PERSON>r nebūs izvēlēts beigu datums.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "Tiklīdz pievienosiet posmu pēc <PERSON> posma, tam tiks pievienots beigu datums.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Ja nav izvēlēts beigu datums, tas nozīmē, ka:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Priekšskatījums", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Veidlapas i<PERSON>gša<PERSON> brīdī radā<PERSON>, l<PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Saglabāts!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "<PERSON><PERSON><PERSON> ve<PERSON> i<PERSON> ir saglab<PERSON>.", "app.containers.AdminPage.ProjectTimeline.startDate": "<PERSON><PERSON><PERSON><PERSON> datums", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "<PERSON><PERSON><PERSON><PERSON> datums", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "Poga", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Posma nosaukums", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminoloģija (sākumlapas filtrs)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Kā nosaukt tagi sākumlapas filtrā? Pie<PERSON><PERSON>ram, tagi, kate<PERSON><PERSON><PERSON>, nodaļas ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Tagus iespējams konfigurēt {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "š<PERSON>", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Viena taga apzīmējums (vienskaitlī)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tags", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Vairāku tagu a<PERSON>zī<PERSON>ēju<PERSON> (daudzskaitlī)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tagi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "<PERSON><PERSON><PERSON> jaunu reģistrācijas lauku", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Atbildes formāts", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "<PERSON><PERSON><PERSON><PERSON><PERSON> atbildes formātu", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Atbildes iespēja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atbil<PERSON> iespēju visā<PERSON> valo<PERSON>s", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Saglabāt atbildes iespēju", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Veiksmīgi saglabāta atbildes iespēja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Atbildes variantu izvēle", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Vel<PERSON>t un nometiet lauku<PERSON>, lai note<PERSON>, kādā secībā tie tiks parādīti pieteikšanās veidlap<PERSON>.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kas <PERSON>s zem lauka nosa<PERSON>ma piete<PERSON> ve<PERSON>.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Atbildes variantus dzīves<PERSON>tai var iestatīt sadaļā {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Rediģēt", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Atbildes rediģēšanas iespēja", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Norādiet lauka nosaukumu visā<PERSON> valo<PERSON>s", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Jā-nē (iz<PERSON><PERSON><PERSON> r<PERSON>ti<PERSON>)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Datums", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Garā <PERSON>bilde", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Vairākas izvē<PERSON> i<PERSON> (izvēlēties vairākas)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Skaitliskā vērtība", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "<PERSON>airā<PERSON> iz<PERSON> (izvēlēties vienu)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Ģeogrāfisko zonu cilne", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>s", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Vai uz jautā<PERSON><PERSON>m šajā laukā ir jāatbild obligāti?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON>not atbildes iespēju", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Atcelt", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Vai esat p<PERSON>rl<PERSON>cināts, ka vēlaties dzēst šo reģistrācijas jautājuma atbildes iespēju? Visi ieraksti, uz kuriem konkrēti lietotāji atbildēja ar šo opciju, tiks neatgriezeniski dzēsti. <PERSON>o darbību nevar atcelt.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Vai esat pārl<PERSON>cināts, ka vēlaties dzēst šo reģistrācijas jautājumu? Visas atbildes, ko lietotāji ir snieguši uz šo jautājumu, tiks neatgriezeniski dzē<PERSON>, un tas vairs netiks uzdots projektos vai priekšlikumos. Šo darbību nevar atcelt.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Lauks veiksmīgi sagla<PERSON>", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Divas ailes", "app.containers.AdminPage.SettingsPage.addAreaButton": "Pievienot ģeogrāfisko zonu", "app.containers.AdminPage.SettingsPage.addTopicButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Dzīvnieks - <PERSON><PERSON><PERSON><PERSON>, kaķis zilonis", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Lietotājs - <PERSON><PERSON><PERSON><PERSON>, Lietotājs 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuri administratori saņems paziņojumus par projektu apstiprināšanu. Mapju pārvaldnieki pēc noklusējuma ir visu projektu apstiprinātāji savās mapēs.", "app.containers.AdminPage.SettingsPage.approvalSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.approvalTitle": "Projekta apstiprināšanas iestatījumi", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst šo zonu?", "app.containers.AdminPage.SettingsPage.areaTerm": "Vienas zonas <PERSON> (vienskaitlī)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "zona", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Rediģēt", "app.containers.AdminPage.SettingsPage.areasTerm": "Vairāku zonu a<PERSON> (daudzskaitlī)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "zonas", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Izvēlieties vismaz vienu valodu.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Rā<PERSON><PERSON>t nereģistrētiem apmeklētājiem dalībnieku profila bildes un to skaitu", "app.containers.AdminPage.SettingsPage.bannerHeader": "Galvenes te<PERSON>", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Galvenes teksts nereģistrētiem apmeklētājiem", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "G<PERSON><PERSON><PERSON> pak<PERSON>ot<PERSON> iedaļas teksts nereģistrētiem apmeklētājiem", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> te<PERSON>", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Reklāmkaroga teksts", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>kš<PERSON>tījumu", "app.containers.AdminPage.SettingsPage.brandingDescription": "Pievienot logotipu un iestatīt platformas krāsas.", "app.containers.AdminPage.SettingsPage.brandingTitle": "<PERSON>as <PERSON>", "app.containers.AdminPage.SettingsPage.cancel": "Atcelt", "app.containers.AdminPage.SettingsPage.chooseLayout": "Izkārtojums", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON><PERSON><PERSON><PERSON><PERSON> krā<PERSON>", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> krāsa", "app.containers.AdminPage.SettingsPage.color_text": "Teks<PERSON>", "app.containers.AdminPage.SettingsPage.colorsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "Vai esat pā<PERSON>, ka vēlaties dzēst šo tagu?", "app.containers.AdminPage.SettingsPage.contentModeration": "Satura moderēšana", "app.containers.AdminPage.SettingsPage.ctaHeader": "Pogas", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapas galvene | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Pogas teksts", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Pogas saite", "app.containers.AdminPage.SettingsPage.defaultTopic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tiks dzē<PERSON> tags no visiem esošajiem ierakstiem. <PERSON><PERSON><PERSON> izma<PERSON>as tiks piemērotas visiem projektiem.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Pievienojiet un dzēsiet tagus, kurus vēlaties izmantot savā platformā ierakstu kategorizēšanai. Jūs varat pievienot tagus konkrētiem projektiem vietnē {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editFormTitle": "Rediģēt zonu", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Rediģēt", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Rediģēt tagu", "app.containers.AdminPage.SettingsPage.fieldDescription": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Šis apraksts ir paredzēts tikai iekšējai sadarbībai un nav redzams lietotājiem.", "app.containers.AdminPage.SettingsPage.fieldTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Norādiet zonas nosa<PERSON>mu visās valo<PERSON>s", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Katrai zonai izvēlēto nosaukumu var izmantot kā reģistrācijas lauka iespēju un projektu filtrēšanai sākumlapā.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Saglab<PERSON><PERSON> tagu", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Taga nosaukums", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Norādiet taga nosaukumu visās valod<PERSON>s", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Jūsu izvēlētais katra taga nosaukums būs redzams platformas lietotājiem.", "app.containers.AdminPage.SettingsPage.fixedRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Šis bannera veids vislabāk darbojas ar attēliem, kurus nevajadzētu apgriezt, piem<PERSON><PERSON>, attēliem ar tekstu, logotipu vai īpašiem elementiem, kas ir būtiski jūsu iedzīvotājiem. Kad lietotāji ir pierakstī<PERSON><PERSON>, šis baneris tiek aizstāts ar vienkrāsainu pamatkr<PERSON> lo<PERSON>. Šo krāsu var iestatīt vispārējos iestatījumos. Vairāk informācijas par ieteicamo attēlu izmantošanu var atrast mūsu vietnē {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> bā<PERSON>", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Pilna platuma rek<PERSON>", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "<PERSON>is baneris izstiepjas visā platumā, nod<PERSON><PERSON><PERSON><PERSON> lielisku vizuālo efektu. Attēls centīsies aizņemt pēc iespējas vairāk vietas, tāpēc tas ne vienmēr būs redzams visu laiku. <PERSON>o baneri var kombinēt ar jebkuras krāsa<PERSON> pārklājumu. Vairāk informācijas par ieteicamo attēla i<PERSON> var atrast mūsu vietnē {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> bā<PERSON>", "app.containers.AdminPage.SettingsPage.header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.headerDescription": "<PERSON><PERSON><PERSON><PERSON> reklāmkar<PERSON> attēlu un tekstu.", "app.containers.AdminPage.SettingsPage.header_bg": "Reklāmkaroga attēls", "app.containers.AdminPage.SettingsPage.helmetDescription": "Administrēt i<PERSON><PERSON>u", "app.containers.AdminPage.SettingsPage.helmetTitle": "Administrēt i<PERSON><PERSON>u", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Pievienot saturu pielāgojamā sadaļā sākumlapas apakšā.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Mā<PERSON><PERSON><PERSON> galvene | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON>", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Konstatēt nepiemērotu saturu", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Automātiski atpazīt platformā publicēto nepiemēroto saturu.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> ir <PERSON><PERSON><PERSON><PERSON>, da<PERSON><PERSON><PERSON>nieku publicētie ieguldījumi, prie<PERSON><PERSON><PERSON><PERSON> un komentāri tiks automātiski pārskatīti. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas tiks atzīmēti kā iespējami neat<PERSON> satura, netiks bloķēti, bet tiks izcelti, lai tos pārskatītu {linkToActivityPage} lapā.", "app.containers.AdminPage.SettingsPage.languages": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Varat izvēlēties vairākas valodas, kurās vēlaties piedāvāt savu platformu lietotājiem. Jums būs jāizveido saturs katrai izvēlētajai valodai.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Pasākums", "app.containers.AdminPage.SettingsPage.logo": "Logotips", "app.containers.AdminPage.SettingsPage.noHeader": "<PERSON><PERSON><PERSON><PERSON>, augšupielād<PERSON><PERSON>et galvenes attēlu", "app.containers.AdminPage.SettingsPage.no_button": "Nav pogas", "app.containers.AdminPage.SettingsPage.organizationName": "Pilsētas vai organizācijas nosaukums", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Norādiet organizācijas nosaukumu vai pilsētu visām valodām.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Pārk<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.phone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Platformas konfigurācija", "app.containers.AdminPage.SettingsPage.population": "Iedzīvotā<PERSON> s<PERSON>ts", "app.containers.AdminPage.SettingsPage.populationMinError": "Iedzīvot<PERSON><PERSON> skaitam jābūt pozitīvam skaitlim.", "app.containers.AdminPage.SettingsPage.populationTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> skaits jūsu teritorijā. <PERSON> <PERSON><PERSON><PERSON>, lai aprēķinātu līd<PERSON>l<PERSON><PERSON> līmeni. <PERSON><PERSON><PERSON><PERSON>, ja <PERSON>.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Necenzētu vārdu bloķētājs", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Bloķēt ievades datus, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un komentārus, kuro<PERSON> ir visbiež<PERSON> sa<PERSON> aizskaroš<PERSON> v<PERSON>.", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Šis teksts redzams sākumlapā virs projektiem.", "app.containers.AdminPage.SettingsPage.projectsSettings": "projekta iestatījumi", "app.containers.AdminPage.SettingsPage.projects_header": "Projektu galvene", "app.containers.AdminPage.SettingsPage.registrationFields": "Reģistrācijas lauki", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Reģistrācijas veidlapas augš<PERSON>ē sniedziet īsu aprakstu.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Reģistrācija", "app.containers.AdminPage.SettingsPage.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "Saglab<PERSON><PERSON> zonu", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "<PERSON>ut kas nogāja greizi. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Veiksmīgi!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "<PERSON><PERSON><PERSON> ve<PERSON> i<PERSON> ir saglab<PERSON>.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Aps<PERSON>rin<PERSON><PERSON><PERSON><PERSON> atlase", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "<PERSON><PERSON>, kas tiks rād<PERSON>, lai sekotu līdzi pēc reģistrācijas.", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tēmas, kas tiks rādītas lie<PERSON>, lai sekotu līdzi pēc reģistrācijas.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Nevarēja saglabāt. Mēģiniet vēlreiz mainīt iestatījumu.", "app.containers.AdminPage.SettingsPage.sign_up_button": "“Pieteikties”", "app.containers.AdminPage.SettingsPage.signed_in": "Poga reģistrētiem apmeklētājiem", "app.containers.AdminPage.SettingsPage.signed_out": "Poga nereģistrētiem apmeklētājiem", "app.containers.AdminPage.SettingsPage.signupFormText": "Reģistrācijas palīgteksts", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Pievienot ī<PERSON> apraks<PERSON> pieteik<PERSON> ve<PERSON> aug<PERSON>.", "app.containers.AdminPage.SettingsPage.statuses": "Statusi", "app.containers.AdminPage.SettingsPage.step1": "Solis: e-pasts un parole", "app.containers.AdminPage.SettingsPage.step1Tooltip": "<PERSON><PERSON> ir norād<PERSON>ts pirmās lapas augš<PERSON> piete<PERSON> veid<PERSON> (vārds, e-pasta adrese, parole).", "app.containers.AdminPage.SettingsPage.step2": "Solis: reģistrācijas j<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.step2Tooltip": "<PERSON><PERSON> nor<PERSON><PERSON><PERSON> piete<PERSON> veid<PERSON>as otrās lapas aug<PERSON> (papildu reģistrācijas lauki).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Nosakiet ģeogrāfiskās zonas, kuras vēlaties izmantot savai platformai, pie<PERSON><PERSON><PERSON>, ap<PERSON><PERSON>, rajonus vai novadus. Varat šīs ģeogrāfiskās zonas sasaistīt ar projektiem (var filtrēt mērķlapā) vai lūgt dalībniekus izvēlēties savu dzīvesvietas zonu reģistrācijas laukā, lai izveidotu viedās grupas un noteiktu piekļuves tiesī<PERSON>.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kā cilvēki redzēs jūsu organizācijas nosaukumu, izvēlieties platformas valodas un saiti uz jūsu tīmekļa vietni.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Norā<PERSON>ītais apakšvirsraksts pārs<PERSON><PERSON><PERSON> maks<PERSON><PERSON> atļauto rakstzīm<PERSON> skaitu (90 zīmes).", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminoloģija", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Iestatījumi ve<PERSON>.", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "Zī<PERSON>lvedība", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Ievades statusi", "app.containers.AdminPage.SettingsPage.tabPolicies": "Politi<PERSON>", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Projekta a<PERSON>", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Priekšlikumu statusi", "app.containers.AdminPage.SettingsPage.tabRegistration": "Reģistrācija", "app.containers.AdminPage.SettingsPage.tabSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "Tags", "app.containers.AdminPage.SettingsPage.tabWidgets": "Logrīks", "app.containers.AdminPage.SettingsPage.tablet": "Planšetdators", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "<PERSON><PERSON><PERSON>, kādu ģeogrāfisko vienību vēlaties izmantot saviem projektiem (piemēram, apkaimes, apgabali, rajoni utt.).", "app.containers.AdminPage.SettingsPage.titleAreas": "Ģeogrāfiskās zonas", "app.containers.AdminPage.SettingsPage.titleBasic": "Vispārīgie iestatījumi", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "<PERSON><PERSON><PERSON><PERSON><PERSON>s virsrak<PERSON> pā<PERSON><PERSON><PERSON><PERSON> ma<PERSON><PERSON><PERSON> atļauto rakstz<PERSON><PERSON><PERSON> (35 zīmes).", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tagu pārvaldnieks", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "<PERSON><PERSON> re<PERSON>mkar<PERSON>s ir <PERSON><PERSON><PERSON><PERSON> noder<PERSON>, ja attēli nedarbojas labi kopā ar virs<PERSON>, apa<PERSON><PERSON><PERSON>srak<PERSON> vai pogas tekstu. Šie elementi tiks izstumti zem banera. Vairāk informācijas par ieteicamo attēlu i<PERSON> var atrast mūsu vietnē {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> bā<PERSON>", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON>vas rindas", "app.containers.AdminPage.SettingsPage.urlError": "URL nav derīgs", "app.containers.AdminPage.SettingsPage.urlPatternError": "Ievadiet derīgu URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Tīmekļa vietne", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "<PERSON><PERSON><PERSON> varat pievienot saiti uz savu vietni. <PERSON><PERSON> saite tiks norādīta sāku<PERSON>as apakš<PERSON>.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kā lietotāji bez vārda profilā tiks parādīti platformā. <PERSON><PERSON> notiks, ja fāzes piekļuves tiesības iestatīsiet uz \"E-pasta apstiprinājums\". <PERSON><PERSON><PERSON> gad<PERSON>, piedaloties programm<PERSON>, lietot<PERSON><PERSON> varēs at<PERSON>n<PERSON>t profila vārdu, ko mēs viņiem automātiski izveidojām.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Lietotā<PERSON> vā<PERSON> r<PERSON> (tikai lietotājiem ar apstiprinātu e-pastu)", "app.containers.AdminPage.SideBar.administrator": "Administrators", "app.containers.AdminPage.SideBar.communityPlatform": "Kopienas platforma", "app.containers.AdminPage.SideBar.community_monitor": "<PERSON><PERSON><PERSON> uzraudz<PERSON>ā<PERSON>s", "app.containers.AdminPage.SideBar.customerPortal": "<PERSON><PERSON><PERSON> portā<PERSON>", "app.containers.AdminPage.SideBar.dashboard": "Informāci<PERSON>is", "app.containers.AdminPage.SideBar.emails": "E-pasti", "app.containers.AdminPage.SideBar.folderManager": "Mapju pārvaldnieks", "app.containers.AdminPage.SideBar.groups": "Grupas", "app.containers.AdminPage.SideBar.guide": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inputManager": "<PERSON><PERSON>des datu p<PERSON>", "app.containers.AdminPage.SideBar.insights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inspirationHub": "Iedvesmas centrs", "app.containers.AdminPage.SideBar.knowledgeBase": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.containers.AdminPage.SideBar.language": "Valoda", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Lapas un izvēlne", "app.containers.AdminPage.SideBar.messaging": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.moderation": "Pasākums", "app.containers.AdminPage.SideBar.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.processing": "<PERSON><PERSON> a<PERSON>", "app.containers.AdminPage.SideBar.projectManager": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "app.containers.AdminPage.SideBar.projects": "Projekti", "app.containers.AdminPage.SideBar.settings": "Iestatījumi", "app.containers.AdminPage.SideBar.signOut": "Izrakstīties", "app.containers.AdminPage.SideBar.support": "Atbalsts", "app.containers.AdminPage.SideBar.toPlatform": "Uz platformu", "app.containers.AdminPage.SideBar.tools": "Instrumenti", "app.containers.AdminPage.SideBar.user.myProfile": "Mans profils", "app.containers.AdminPage.SideBar.users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.workshops": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.browseTopics": "<PERSON><PERSON><PERSON><PERSON><PERSON> tagus", "app.containers.AdminPage.Topics.cancel": "Atcelt", "app.containers.AdminPage.Topics.confirmHeader": "Vai esat pā<PERSON>, ka vēlaties dzēst šo projekta tagu?", "app.containers.AdminPage.Topics.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.deleteTopicLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "<PERSON>o tagu vairs nevarēs pievienot jauniem ierakstiem šajā projektā.", "app.containers.AdminPage.Topics.inputForm": "<PERSON><PERSON><PERSON> datu veid<PERSON>a", "app.containers.AdminPage.Topics.lastTopicWarning": "Nepieciešams vismaz viens tags. Ja nevēlaties izmantot tagus, tos var atspējot cilnē {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "<PERSON><PERSON><PERSON> varat pievienot un dzēst tagus, kurus var piešķirt ierakstiem šajā projektā.", "app.containers.AdminPage.Topics.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.title": "Projekta tagi", "app.containers.AdminPage.Topics.topicManager": "Tagu pārvaldnieks", "app.containers.AdminPage.Topics.topicManagerInfo": "<PERSON>a vēlaties pievienot papildu projekta tagus, varat to i<PERSON><PERSON><PERSON><PERSON> {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "<PERSON><PERSON><PERSON> j<PERSON> grupu", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Grupas nosaukums", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "<PERSON><PERSON><PERSON><PERSON>t grupas nosaukumu", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Izveidot manuālu grupu", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "<PERSON><PERSON>da veida grupa jums ir nepiecieša<PERSON>?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Saglabāt grupu", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Izveidot manuālu grupu", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Izveidot viedo grupu", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par grupām", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Pārskatā varat atlasīt lietotājus un pievienot viņus šai grupai.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "<PERSON><PERSON><PERSON> defin<PERSON><PERSON>, un lie<PERSON><PERSON><PERSON><PERSON>, kas at<PERSON>st no<PERSON><PERSON>, tiek automātiski pievienoti šai grupai.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "<PERSON><PERSON><PERSON><PERSON><PERSON> grupa", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "<PERSON><PERSON><PERSON> grupa", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "<PERSON><PERSON><PERSON> grupā vēl nav nevienas personas", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Dodieties uz {allUsersLink}, lai manu<PERSON>li pievie<PERSON>u da<PERSON> lie<PERSON>.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "<PERSON>ū<PERSON> me<PERSON>lēšanai neatbilst neviens lietotājs", "app.containers.AdminPage.Users.GroupsPanel.select": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Eksportēt visu", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Eksportēt lietotājus grupā", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Izvēlēts eksportēt", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Vai esat pārl<PERSON>, ka vēlaties dzēst šo grupu?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Pievienojot lietot<PERSON><PERSON>, rad<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Noņemt no grupas", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Dzēst atlasītos lietotājus no šīs grupas?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> lietotājus no grupas, rad<PERSON><PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Lietotāju <PERSON>", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.add": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addAnswer": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addQuestion": "Demogrā<PERSON><PERSON> j<PERSON>", "app.containers.AdminPage.groups.permissions.answerChoices": "Atbildes variantu izvēle", "app.containers.AdminPage.groups.permissions.answerFormat": "Atbildes formāts", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Jānorāda vismaz viena izvēle", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "<PERSON>is lietotājs p<PERSON> mapi, k<PERSON><PERSON> ir šis projekts. <PERSON> atņemtu šī lietotāja moderatora tiesības šim projektam, varat vai nu atsaukt viņa mapes tiesības, vai pārvietot projektu uz citu mapi.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Izveidot jaunu j<PERSON>", "app.containers.AdminPage.groups.permissions.createAQuestion": "Izveidot j<PERSON>", "app.containers.AdminPage.groups.permissions.defaultField": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "<PERSON><PERSON><PERSON><PERSON>, norā<PERSON>t nosaukumu visām izvēlēm", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Jā-nē (iz<PERSON><PERSON><PERSON> r<PERSON>ti<PERSON>)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Datums", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Garā <PERSON>bilde", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Vairākas izvē<PERSON> i<PERSON> (izvēlieties vairākas)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Skaitliskā vērtība", "app.containers.AdminPage.groups.permissions.fieldType_select": "Vairā<PERSON> iz<PERSON> (izvēlieties vienu)", "app.containers.AdminPage.groups.permissions.fieldType_text": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Granulāro atļauju maiņa nav licences daļa. <PERSON><PERSON><PERSON><PERSON>, sazin<PERSON>ies ar savu Gov<PERSON><PERSON><PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> par to.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Vai esat pārl<PERSON>cināts, ka vēlaties šo grupu izņemt no projekta?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Izvēlēties vienu vai vairākas grupas", "app.containers.AdminPage.groups.permissions.members": "{count, plural, zero {}=0 {Nav dalībnieku} one {1 dalībnieks} other {{count} biedri}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "<PERSON><PERSON><PERSON><PERSON>, aizpild<PERSON> nosaukumu visās valod<PERSON>s", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Vai esat pārlie<PERSON>āts?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>ji nav atrasti", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "<PERSON><PERSON><PERSON> nav <PERSON>, jo <PERSON><PERSON><PERSON> projektā lietotājs nevar veikt nekādas da<PERSON>.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Tikai administratori var izve<PERSON>t jaunu j<PERSON>.", "app.containers.AdminPage.groups.permissions.option1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Neapstiprināts uzaicinājums", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Kas var pievienot anotācijas dokumentam?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "<PERSON><PERSON><PERSON> var pieteikties dalībai pasākumā?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Kas var komentēt ievades datus?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Kas var komentēt p<PERSON>kšlikumus?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Ka<PERSON> var iesniegt p<PERSON>kšlikumu?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Kas var reaģēt uz ievadītajiem datiem?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Ka<PERSON> var iesniegt ievades datus?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Kas var piedalīties aptaujā?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Kas var piedalīties aptaujā?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Kas var kļūt par brīvprātīgo?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Kas var balsot par prie<PERSON>š<PERSON>em?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Ka<PERSON> var balsot?", "app.containers.AdminPage.groups.permissions.questionDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "<PERSON>ut kas nogāja greizi. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Veiksmīgi!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "<PERSON><PERSON><PERSON> ve<PERSON> i<PERSON> ir saglab<PERSON>.", "app.containers.AdminPage.groups.permissions.select": "Atlasiet", "app.containers.AdminPage.groups.permissions.selectValueError": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties atbildes veidu", "app.containers.AdminPage.new.createAProject": "Izveidot projektu", "app.containers.AdminPage.new.fromScratch": "No nulles", "app.containers.AdminPage.phase.methodPicker.addOn1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> intelektu darbināms ieskats", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON> dalībniekiem atklāt vienošanos un domstarpības pa vienai idejai.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Atrodiet kopsaucēju", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Ievietojiet interaktīvu PDF failu un apkopojiet komentārus un atsauksmes, izmantojot Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Atsauksmes par dokumentu vākšana", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "<PERSON><PERSON><PERSON><PERSON><PERSON> puses a<PERSON>juma iestr<PERSON>", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Izmantojiet lietotāju kolektīvo intelektu. Ai<PERSON><PERSON> vi<PERSON> i<PERSON>, apsp<PERSON>t idejas un/vai sniegt atsauksmes publiskā forumā.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Publiski apkopot informāciju un atsauksmes", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Ko<PERSON><PERSON><PERSON><PERSON>et <PERSON>ju", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "trūkst ar mākslīgo intelektu darbināmu ieskatu platformā", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "trūkst platformas pārskatu, datu vizualizācijas un apstrādes.", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Saikne ar platformas pārskatu veidotāju", "app.containers.AdminPage.phase.methodPicker.logic1": "Loģika", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON> veidu k<PERSON>", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Ļaujiet dalībniekiem augš<PERSON>d<PERSON> idej<PERSON>, nosakot laika un balsošanas ierobežojumu.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lūgumraksti vai iniciatīvas", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "<PERSON><PERSON> a<PERSON>", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Izveidojiet ī<PERSON> anketu ar atbilžu variantiem.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Sniedziet informāciju <PERSON>, vizualizējiet citos posmos iegūtos rezultātus un veidojiet ar datiem bagātus pārskatus.", "app.containers.AdminPage.phase.methodPicker.survey1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Izprotiet lietotāju v<PERSON>dz<PERSON> un do<PERSON>, i<PERSON><PERSON><PERSON><PERSON> da<PERSON> privāto j<PERSON><PERSON><PERSON> veidus.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Aptaujas iespējas", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Izveidot apsekojumu", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Lūdziet lietotājus brīvprātīgi iesaistīties aktivitātēs un pasākumos vai atrast dalībniekus paneļdiskusijai.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Dalībnieku vai brīvprātīgo vervēšana", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Izvēlieties balsošanas metodi un ļaujiet lietotājiem noteikt prioritātes starp vairākām dažādām iespējām.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Veikt b<PERSON> vai prioritāš<PERSON>", "app.containers.AdminPage.projects.all.all": "Visi", "app.containers.AdminPage.projects.all.createProjectFolder": "Jauna mape", "app.containers.AdminPage.projects.all.existingProjects": "Esošie projekti", "app.containers.AdminPage.projects.all.homepageWarning1": "Izmantojiet š<PERSON> lap<PERSON>, lai iestatītu projektu secību navigācijas j<PERSON>las izlaižamajā logā \"Visi projekti\". <PERSON>a sākumlapā izmantojat logrīkus \"Publicētie projekti un mapes\" vai \"Projekti un mapes (vecie)\", a<PERSON><PERSON> logrīkos projektu secība tiks noteikta pēc šeit iestatītās secības.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Šeit tiks rād<PERSON>ti projekti, kuros esat projektu vadīt<PERSON>.", "app.containers.AdminPage.projects.all.noProjects": "Projekti nav atrasti.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Tikai administratori var izveidot projektu mapes.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projekti un mapes", "app.containers.AdminPage.projects.all.publishedTab": "Publicēts", "app.containers.AdminPage.projects.all.searchProjects": "<PERSON><PERSON><PERSON><PERSON><PERSON> projektus", "app.containers.AdminPage.projects.all.yourProjects": "<PERSON><PERSON><PERSON> projekti", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI analīze", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Precizitāte: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "<PERSON><PERSON> viet<PERSON>, lai ap<PERSON> datus, varat uzdot attiecīgus jautājumus par saviem datiem. Šī funkcija nav iekļauta jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "<PERSON><PERSON> ies<PERSON>s ietver <PERSON><PERSON> j<PERSON>:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst šo j<PERSON>?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst š<PERSON> kops<PERSON>?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "<PERSON><PERSON> tiks parā<PERSON><PERSON><PERSON> j<PERSON> te<PERSON>, bet pašlaik jums vēl nav neviena.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "<PERSON> sāktu darbu, noklikšķiniet uz augšpusē esošās poga<PERSON> Auto<PERSON>ā<PERSON> kopsavilkuma iz<PERSON>ide.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ievades", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Ja uzdodat jautājumus par mazāku ievades datu skaitu, tiek nodro<PERSON>in<PERSON>ta lielāka precizitāte. Samaziniet pašreizējo ievades datu atlasi, i<PERSON><PERSON><PERSON><PERSON> tag<PERSON>, me<PERSON><PERSON><PERSON><PERSON><PERSON> vai demogrāfiskos filtrus.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Jautājums par visiem ievades datiem", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Novērtējiet š<PERSON> ieskata kvalitāti", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Atjaunot filtrus", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Apkopojiet", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "<PERSON><PERSON><PERSON><PERSON><PERSON> par", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Kopsavilkums par visiem ievades datiem", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Paldies par atsauksmēm", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "Mākslīgais intelekts nespēj apstrādāt tik daudz ievades datu vienā reizē. Sadaliet tos mazāk<PERSON> grup<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Pašreizējā plānā vienlaicīgi varat apkopot ne vairāk kā 30 ievades datus. Sazinieties ar savu GovSuccess pārvald<PERSON> vai <PERSON>u, lai atkl<PERSON><PERSON> vairāk.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "<PERSON><PERSON> saprotu", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Mūsu platforma ļauj jums izpētīt galvenās tē<PERSON>, apko<PERSON> datus un izpētīt dažādas perspektīvas. Ja meklējat konkrētas atbildes vai atziņas, apsveriet iespēju izmantot funkciju \"Uzdot jautāju<PERSON>\", lai i<PERSON><PERSON><PERSON><PERSON> plaš<PERSON>k nekā kopsavilkum<PERSON>.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "Lai gan tas notiek reti, māks<PERSON><PERSON>gais intelekts dažkārt var ģenerēt informāciju, kas sākotnējā datu kopā nebija skaidri norād<PERSON>ta.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "Mākslīgais intelekts var uzsvērt noteiktas tēmas vai idejas vairāk nekā citas, t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON>, izk<PERSON>ļ<PERSON><PERSON><PERSON> kopējo interpretāciju.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Pārspīlējums:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "<PERSON><PERSON><PERSON> sistēma ir optimizēta 20-200 precīzi definētu ievades datu apstrādei, lai iegūtu visprecīzākos rezultātus. Datu apjomam palielinoties virs š<PERSON> diapazona, kopsavilkums var kļūt augsta līmeņa un vispārīgāks. <PERSON><PERSON>, ka mākslīgais intelekts kļūst \"mazāk precīzs\", bet gan to, ka tas koncentrēsies uz plašākām tendencēm un modeļiem. Lai iegūtu niansētāku ieskatu, iesak<PERSON>m izmantot (automātiskās) marķēšanas funkciju, lai segmentētu lielākas datu kopas mazākās, vieglāk pārvaldāmās apakškopās.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "<PERSON><PERSON> apjoms un precizitāte:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "<PERSON><PERSON><PERSON> i<PERSON>m izmantot mākslīgā intelekta ģenerētus kopsavilkumus kā sākumpunktu lielu datu kopu i<PERSON>pratnei, bet ne kā galīgo risinājumu.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "<PERSON><PERSON> strādāt ar mākslīgo intelektu", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "<PERSON><PERSON><PERSON> ievades datu pie<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "<PERSON><PERSON><PERSON>u", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "<PERSON><PERSON> funkcija nav iekļauta jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Visas ievades", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Visas ievades", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "Visas birkas", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "Nē, es to darīšu", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Vai vēlaties automātiski piešķirt ievades ievades tagam?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Ir <b><PERSON><PERSON><PERSON><PERSON> met<PERSON></b> , lai automātiski piešķirtu ievades tagiem.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "<PERSON><PERSON><PERSON><PERSON><PERSON> <b>automātisk<PERSON>s marķēšanas pogu</b> , lai palaistu vēlamo metodi.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Noklikšķiniet uz birkas, lai to piešķirtu pašreiz izvēlētajam ievades elementam.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Jā, automātiskā marķēšana", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Automātiskā marķēšana", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Automātiskos marķējumus automātiski nosaka dators. To<PERSON> jebkur<PERSON> laikā varat mainīt vai dzēst.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Automātiskā marķēšana", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "<PERSON><PERSON><PERSON>, kas jau ir sa<PERSON>ti ar <PERSON> tagie<PERSON>, netiks klasific<PERSON>ti at<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Klasifikācija ir balstīta tikai uz birkas nosaukumu. <PERSON> i<PERSON><PERSON> lab<PERSON> rezultā<PERSON>, izvēlieties atbilstošus atslēgvārdus.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tags: P<PERSON>c etiķetes", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "<PERSON><PERSON><PERSON> tagus un, <PERSON><PERSON><PERSON><PERSON>, manu<PERSON><PERSON> piešķirat dažus ievades datus, pār<PERSON><PERSON> datus piešķir dators.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tags: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kā \"Tags: pēc etiķetes\", bet ar lielā<PERSON> preci<PERSON>, jo sistēma tiek apmācīta ar labiem piemēriem.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "<PERSON><PERSON><PERSON>, dators piešķir ievades datus.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tags: P<PERSON>c etiķetes", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "<PERSON><PERSON> lab<PERSON>, ja jums ir i<PERSON><PERSON><PERSON><PERSON> definēts tagu kopums vai ja jūsu projektam ir ierobežota tagu darbības joma.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Atklājiet ievades datus ar iev<PERSON><PERSON><PERSON><PERSON>/patīk at<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Pretr<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Dzēst birku", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Vai esat pā<PERSON>, ka vēlaties dzēst šo birku?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Neparādiet to vēlreiz", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Rediģēt birku", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Izvēlieties maks<PERSON> 9 tagus, starp kuriem vēlaties sadalīt ievades.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Klasifikācija tiek veik<PERSON>, pamatojoties uz ievades datiem, kas pa<PERSON>laik ir piešķirti tagiem. Dators mēģinās sekot jūsu piemēram.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Tags: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Jums vēl nav nevienas pielāgotas birkas.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Dators automātiski nosaka tagus un piešķir tos ievadītajiem datiem.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Tags: Pilnībā automatizēta", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "<PERSON><PERSON>, ja jūsu projekti aptver plašu tagu klāstu. Laba vieta, kur sākt.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Kā vēlaties atzī<PERSON>ēt?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "<PERSON><PERSON><PERSON> bez tagiem", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "<PERSON><PERSON> ievades valo<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Valoda", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Palaist", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Nav aktīvo filtru", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag<PERSON>, lai sadalītu un filtrētu ievades datus, lai sagatavotu precīzākus vai mērķtiecīgākus kopsavilkumus.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "Citi", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Tags: Platformas birkas", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Piešķirt esoš<PERSON>s <PERSON>as tagus, ko autors izvēl<PERSON><PERSON>, public<PERSON><PERSON><PERSON> zi<PERSON>u", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> birku", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "Atcelt", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Nosa<PERSON>ms", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> birku", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Atlasiet visus", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Piešķirt pozitīvu vai negatīvu noskaņojumu katram ievadītajam tekstam, kas izriet no teksta.", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Noskaņojums", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Birkas noteikšana", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Pašreizējo filtru i<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "<PERSON><PERSON><PERSON> ievades datus vēlaties atzīmēt?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Autotagging uzdevums", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Pretr<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Beidzās pie", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Neveiksmīgs", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "Pašlaik izstrādes procesā", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "Pēc etiķetes", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Valoda", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP birka", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Nav nesen veiktu AI uzdevumu", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Platformas birka", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": ", kas atrodas rindā", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Noskaņojums", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "<PERSON><PERSON><PERSON><PERSON> ar", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Sekmēja", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Apkopošanas uzdevums", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Izraisa pie", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Virs", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Visi", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Autors", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Dzimšanas gads", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "No", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Dzimums", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Reak<PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "<PERSON><PERSON><PERSON> skaits", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Uz", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonīms ieguldījums", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "<PERSON><PERSON> p<PERSON> ve<PERSON>a", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Autori pēc dzīvesvietas", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Fona darbavietas", "app.containers.AdminPage.projects.project.analysis.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "<PERSON><PERSON>la diagramma ir pārāk <PERSON>la, lai to parād<PERSON>tu", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Atbildes", "app.containers.AdminPage.projects.project.analysis.end": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.filter": "<PERSON><PERSON><PERSON><PERSON><PERSON> tikai ievades ar šo vērtību", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Paslēpt atbildes bez atbildes", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "<PERSON><PERSON><PERSON> v<PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "Ir {count} <PERSON><PERSON><PERSON>.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Nepatīk", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Nāka<PERSON><PERSON> siltuma karte", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Tas nav statistiski nozīmīgs atklājums.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuro<PERSON> ir ma<PERSON>āk nekā 30 dalībnieku, nav pieejami automātiskie ieskati.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Iepriekšējais siltuma karte", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Iepriekšē<PERSON>is <PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "<PERSON><PERSON><PERSON> vērtības", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistiski nozīmīgs ieskats.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Apkopojiet", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Analīzes tagi", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "True", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus ieskatus", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Pārskatīt automātiskās ieskatu", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "<PERSON><PERSON><PERSON> bez tagiem", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "<PERSON>ika aug<PERSON>up<PERSON>d<PERSON>ts nederīgs shapefile, un to nevar parādīt.", "app.containers.AdminPage.projects.project.analysis.limit": "Ierobežojums", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "G<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.manageInput": "<PERSON><PERSON><PERSON> datu pā<PERSON>", "app.containers.AdminPage.projects.project.analysis.nextGraph": "<PERSON><PERSON><PERSON><PERSON><PERSON> gra<PERSON>", "app.containers.AdminPage.projects.project.analysis.noAnswer": "Nav atbildes", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "Atbilde nav sniegta.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Nav augšupielādēts shapefile.", "app.containers.AdminPage.projects.project.analysis.noInputs": "<PERSON>ūsu pašreizējiem filtriem neatbilst neviens ievades kods", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Iepriekšējais gra<PERSON>", "app.containers.AdminPage.projects.project.analysis.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Noņemt filtru", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Noņemt filtru", "app.containers.AdminPage.projects.project.analysis.search": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shapefa<PERSON> šeit tiek attēloti GeoJSON formātā. Tāpēc oriģinālā faila stils var netikt pareizi attēlots.", "app.containers.AdminPage.projects.project.analysis.start": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.supportArticle": "<PERSON><PERSON><PERSON> pants", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "<PERSON><PERSON>inā<PERSON>", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus j<PERSON>", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "<PERSON><PERSON><PERSON><PERSON><PERSON> atlas<PERSON>", "app.containers.AdminPage.projects.project.analysis.votes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.copied": "Kopēts starpliktuvē", "app.containers.AdminPage.widgets.copyToClipboard": "<PERSON><PERSON><PERSON><PERSON>o kodu", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Kopēt HTML kodu", "app.containers.AdminPage.widgets.fieldAccentColor": "<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Logrīka fona krāsa", "app.containers.AdminPage.widgets.fieldButtonText": "Pogas teksts", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Pievienojieties tagad", "app.containers.AdminPage.widgets.fieldFont": "Fonts", "app.containers.AdminPage.widgets.fieldFontDescription": "<PERSON> j<PERSON>t esošam fonta nosaukumam no {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "<PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "G<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "<PERSON><PERSON><PERSON> vied<PERSON> ir no<PERSON>", "app.containers.AdminPage.widgets.fieldHeaderText": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldHeight": "Augstums (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Ierakstu s<PERSON>ts", "app.containers.AdminPage.widgets.fieldProjects": "Projekti", "app.containers.AdminPage.widgets.fieldRelativeLink": "Saites uz", "app.containers.AdminPage.widgets.fieldShowFooter": "<PERSON><PERSON><PERSON><PERSON><PERSON> pogu", "app.containers.AdminPage.widgets.fieldShowHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>i", "app.containers.AdminPage.widgets.fieldShowLogo": "<PERSON><PERSON><PERSON><PERSON><PERSON> logotipu", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Vietnes fona krāsa", "app.containers.AdminPage.widgets.fieldSort": "<PERSON><PERSON><PERSON><PERSON> pēc", "app.containers.AdminPage.widgets.fieldTextColor": "Teks<PERSON>", "app.containers.AdminPage.widgets.fieldTopics": "Tagi", "app.containers.AdminPage.widgets.fieldWidth": "Platums", "app.containers.AdminPage.widgets.homepage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Šo HTML kodu jūs varat kopēt un ielīmēt tajā vietnes daļā, kurā vēlaties pievienot logrīku.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Logrīka HTML kods", "app.containers.AdminPage.widgets.previewTitle": "Priekšskatījums", "app.containers.AdminPage.widgets.settingsTitle": "Iestatījumi", "app.containers.AdminPage.widgets.sortNewest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortPopular": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortTrending": "Tendences", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Logrīks", "app.containers.AdminPage.widgets.titleDimensions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "G<PERSON><PERSON>e un kājene", "app.containers.AdminPage.widgets.titleInputSelection": "<PERSON>evades datu izv<PERSON>le", "app.containers.AdminPage.widgets.titleStyle": "Stils", "app.containers.AdminPage.widgets.titleWidgets": "Logrīks", "app.containers.ContentBuilder.Save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.PageTitle": "<PERSON><PERSON><PERSON>a", "app.containers.ContentBuilder.homepage.SaveError": "<PERSON>ut kas notika <PERSON>, sag<PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "<PERSON><PERSON> kolo<PERSON>", "app.containers.ContentBuilder.homepage.bannerImage": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerSubtext": "Bannera zemteksts", "app.containers.ContentBuilder.homepage.bannerText": "Bannera teksts", "app.containers.ContentBuilder.homepage.button": "Poga", "app.containers.ContentBuilder.homepage.chooseLayout": "Izkārtojums", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Pašreizējā licencē nav iekļauta citu iestatīju<PERSON>, iz<PERSON><PERSON>ot sākuma lapas banera attēlu un tekstu. Sazinieties ar savu GovS<PERSON><PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> vair<PERSON> par to.", "app.containers.ContentBuilder.homepage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Pogas teksts", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Pogas saite", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Tiek parādīti nākamie 3 gaidāmie notikumi jūsu platformā.", "app.containers.ContentBuilder.homepage.eventsDescription": "Tiek parādīti nākamie 3 gaidāmie notikumi jūsu platformā.", "app.containers.ContentBuilder.homepage.fixedRatio": "<PERSON><PERSON><PERSON><PERSON> ban<PERSON>s", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Šis bannera veids vislabāk darbojas ar attēliem, kurus nevajadzētu apgriezt, piem<PERSON><PERSON>, attēliem ar tekstu, logotipu vai īpašiem elementiem, kas ir būtiski jūsu iedzīvotājiem. Kad lietotāji ir pierakstī<PERSON><PERSON>, šis baneris tiek aizstāts ar vienkrāsainu pamatkr<PERSON> lo<PERSON>. Šo krāsu var iestatīt vispārējos iestatījumos. Vairāk informācijas par ieteicamo attēlu izmantošanu var atrast mūsu vietnē {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> bā<PERSON>", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "<PERSON>lna platuma baneris", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "<PERSON>is baneris izstiepjas visā platumā, nod<PERSON><PERSON><PERSON><PERSON> lielisku vizuālo efektu. Attēls centīsies aizņemt pēc iespējas vairāk vietas, tāpēc tas ne vienmēr būs redzams visu laiku. <PERSON>o baneri var kombinēt ar jebkuras krāsa<PERSON> pārklājumu. Vairāk informācijas par ieteicamo attēla i<PERSON> var atrast mūsu vietnē {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> bā<PERSON>", "app.containers.ContentBuilder.homepage.imageOverlayColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Nederīgs URL", "app.containers.ContentBuilder.homepage.no_button": "Nav pogas", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Nereģistrēti lietotāji", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Pārk<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.projectsDescription": "<PERSON>, kād<PERSON> secībā tiek rādīti projekti, mainiet to secību vietnē {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "<PERSON><PERSON><PERSON><PERSON> lapa", "app.containers.ContentBuilder.homepage.registeredUsersView": "Reģistrētie lietotāji", "app.containers.ContentBuilder.homepage.showAvatars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> dal<PERSON>nieku profila bildes un to skaitu nereģistrētiem apmeklētājiem", "app.containers.ContentBuilder.homepage.sign_up_button": "Reģistrējieties", "app.containers.ContentBuilder.homepage.signedInDescription": "<PERSON><PERSON><PERSON> baneri redz reģistrētie lietotāji.", "app.containers.ContentBuilder.homepage.signedOutDescription": "<PERSON><PERSON><PERSON><PERSON> veidā re<PERSON>u redz apmek<PERSON>t<PERSON>ji, kuri nav reģistrējušies platformā.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "<PERSON><PERSON> baneris ir <PERSON><PERSON><PERSON><PERSON>, ja attēli nav labi savienoti ar virs<PERSON>, apa<PERSON><PERSON><PERSON><PERSON>rak<PERSON> vai pogas tekstu. Šie elementi tiks izstumti zem banera. Vairāk informācijas par ieteicamo attēlu i<PERSON> var atrast mūsu vietnē {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> bā<PERSON>", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON>vas rindas", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Iekļ<PERSON><PERSON> augstums (pikseļi)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> v<PERSON>, lai iegultais saturs tiktu attēlots lap<PERSON> (pikseļos).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Īss ievietotā satura apraksts", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Šo informāciju ir lietderīgi sniegt lieto<PERSON>, kuri i<PERSON> ekr<PERSON>a las<PERSON>ju vai citu palīgtehnoloģiju.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Tīmekļa vietnes adrese", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "<PERSON><PERSON><PERSON> v<PERSON>, kuru vēlat<PERSON> iegult, URL.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Rādiet saturu no ārējās vietnes savā lapā HTML iFrame formātā. Ņemiet vērā, ka ne katru lapu var ievietot. Ja jums rodas problēmas ar kādas lapas iestrā<PERSON><PERSON><PERSON>, sazinieties ar lapas <PERSON>, vai tā ir konfigurēta tā, lai atļautu iestrādā<PERSON>nu.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Apmekl<PERSON><PERSON><PERSON> mūsu at<PERSON>sta lapu", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>, šo saturu nevarēja ievietot. {visitLinkMessage} , lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Ievadiet derīgu tīme<PERSON> ad<PERSON>, pie<PERSON><PERSON><PERSON>, https://example.com.", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultiloc": "Akordeons", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "<PERSON><PERSON><PERSON><PERSON> pēc no<PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Teksts", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "<PERSON>is ir paplašināms a<PERSON>ona saturs. To var rediģēt un formatēt, i<PERSON><PERSON><PERSON>t redaktoru panelī labajā pusē.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Nosa<PERSON>ms", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Akordeona nosaukums", "app.containers.admin.ContentBuilder.buttonMultiloc": "Poga", "app.containers.admin.ContentBuilder.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.error": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.errorMessage": "{locale} saturā ir kļūda. <PERSON><PERSON><PERSON><PERSON>, novē<PERSON><PERSON>o k<PERSON>, lai varētu saglabāt i<PERSON>.", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON> a<PERSON>", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "<PERSON><PERSON> ir ik ceturksni notiekoša aptauja, kurā tiek noskaid<PERSON>a jūsu attieksme pret pārvaldību un sabiedriskajiem pakalpojumiem.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Veiciet aptauju", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mums jūs <PERSON> a<PERSON>", "app.containers.admin.ContentBuilder.homepage.default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Aicinājums rīkoties", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> poga<PERSON> te<PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pogas URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pogas te<PERSON>ts", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Nosa<PERSON>ms", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ban<PERSON>", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ban<PERSON>", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Attēlu un teksta kartes", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 sleja", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projekti", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Iespējot priekšlikumus sadaļ<PERSON> \"Priekšlikumi\" administratora panelī, lai tos atbloķētu sākumlapā.", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Priekšlikumi", "app.containers.admin.ContentBuilder.imageMultiloc": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Īss attēla apraksts", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "<PERSON> padar<PERSON>tu jūsu platformu pieejamu <PERSON>, kas i<PERSON> e<PERSON><PERSON>, ir svar<PERSON>gi pievienot attēliem \"alt tekstu\".", "app.containers.admin.ContentBuilder.participationBox": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.textMultiloc": "Teksts", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 aile", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 aile", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 ailes ar at<PERSON><PERSON><PERSON><PERSON> 30% un 60% platumu", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 ailes ar at<PERSON><PERSON><PERSON><PERSON> 60% un 30% platumu", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 vien<PERSON>das ailes", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "<PERSON><PERSON><PERSON><PERSON><PERSON> daļa reaģēto ievades datu", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "<PERSON><PERSON><PERSON> f<PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "<PERSON><PERSON> projektam vai posmam nav pieejami ievades dati.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "<PERSON><PERSON><PERSON> datu skaits", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Nosa<PERSON>ms", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Salocīt garu tekstu", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "<PERSON><PERSON> projektam vai posmam nav pieejami ievades dati.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Izvēlieties fāzi", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Autors", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Nosa<PERSON>ms", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Reģistrācijas likme", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Reģistrācijas", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Dalībnieku grafiks", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ka da<PERSON><PERSON><PERSON> rād<PERSON><PERSON><PERSON><PERSON> var nebūt pilnīgi prec<PERSON>, jo daži dati tiek iegūti ārējā apta<PERSON>j<PERSON>, kuru mēs nese<PERSON>.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Di<PERSON>ram<PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Datumu diapazons", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Nosa<PERSON>ms", "app.containers.admin.ReportBuilder.charts.noData": "<PERSON>ūsu izvēlētajiem filtriem nav pieejami dati.", "app.containers.admin.ReportBuilder.charts.trafficSources": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.usersByAge": "Lietotā<PERSON> pēc ve<PERSON>a", "app.containers.admin.ReportBuilder.charts.usersByGender": "Lietotāji pēc d<PERSON>a", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Apmeklētā<PERSON> laika gra<PERSON>", "app.containers.admin.ReportBuilder.managerLabel1": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "app.containers.admin.ReportBuilder.periodLabel1": "Periods", "app.containers.admin.ReportBuilder.projectLabel1": "Projekts", "app.containers.admin.ReportBuilder.quarterReport1": "<PERSON><PERSON>nas uzraudzības ziņojums: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "<PERSON><PERSON><PERSON>", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Iegādājieties 1 papildu sēdvietu", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Apstipriniet", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Vai esat pārl<PERSON>cināts, ka vēlaties 1 personai piešķirt pārvaldnieka <PERSON>?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Piešķirt p<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "<PERSON>ūs esat sasniedzis plān<PERSON> iek<PERSON>auto vietu limitu, {noOfSeats} tiks pievienotas papildu {noOfSeats, plural, one {sēdvietas} other {sēdvietas}} sēdvietas.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Pievienot statusu", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Noklusēju<PERSON> statusus nevar d<PERSON>st.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Rediģēt", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Rediģēt statusu", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Dalībnieka ievades datiem šobrīd piešķirtos statusus nav iespējams dzēst. Jūs varat dzēst/mainīt statusu esošajiem ievades datiem cilnē {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Šo statusu nevar d<PERSON> vai pārvietot.", "app.containers.admin.ideaStatuses.all.manage": "Rediģēt", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Paš<PERSON>iz<PERSON><PERSON><PERSON> plānā nav iekļauta pielāgotu ievades statusu konfigurēšana. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "P<PERSON><PERSON><PERSON><PERSON> statusu, ko var piešķirt dalībnieka ievaddatiem projektā. Statuss ir publiski redzams un palīdz informēt dalībniekus.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "P<PERSON><PERSON>ld<PERSON> statusu, ko var piešķirt priekšlikumiem projekta ietvaros. Statuss ir publiski redzams un palīdz informēt dalībniekus.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Ievades statusu rediģēšana", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Rediģēt priekšlikumu statusus", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Izvēlēts īstenošanai vai turpmākajiem soļiem", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Sniegtas ofici<PERSON>las <PERSON>", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Atbildēja", "app.containers.admin.ideaStatuses.form.category": "Kategorija", "app.containers.admin.ideaStatuses.form.categoryDescription": "<PERSON><PERSON><PERSON><PERSON>, iz<PERSON><PERSON><PERSON><PERSON> ka<PERSON><PERSON><PERSON>, kas visla<PERSON><PERSON>k atbilst jūsu statusam. <PERSON><PERSON> izvēle palīdzēs mūsu analītiskajam rīkam precīzāk apstrādāt un analizēt ierakstus.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Neatbilst nevienai no pārējām iespējām", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Cits", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "Statusa apraksts", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Sniegt statusa aprakstu visās valod<PERSON>s.", "app.containers.admin.ideaStatuses.form.fieldTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Norādīt statusa nosaukumu visiem valodām", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "<PERSON><PERSON>ks<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Īstenots", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Priekšlikums nav atbilstīgs", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Neatbilstošs vai nav izvēlēts virzīšanai tālāk", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Nav izvēlēts", "app.containers.admin.ideaStatuses.form.saveStatus": "Saglabāt statusu", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Apsvērts īstenošanai vai turpmākajiem soļiem", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "<PERSON><PERSON> a<PERSON>", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bet vēl ne<PERSON>", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.inputManagerMetaDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ievades datus un to statusus.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Ievades datu pā<PERSON> | {orgName} dalības platforma", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "<PERSON><PERSON><PERSON>, pievie<PERSON> tagus un pārvietot datus no viena projekta uz citu.", "app.containers.admin.ideas.all.inputManagerPageTitle": "<PERSON><PERSON>des datu p<PERSON>", "app.containers.admin.ideas.all.tabOverview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.import.importInputs": "Import<PERSON><PERSON> ievades datus", "app.containers.admin.import.importNoLongerAvailable3": "Šī funkcija šeit vairs nav pieejama. Lai importētu ievaddatus ideju izstrādes posmā, dodieties uz posmu un izvēlieties \"Importēt\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 papildu administratora vieta} other {# papildu administratora vietas}} un {managerSeats, plural, one {1 papildu vadītāja vieta} other {# papildu menedžera vietas}} tiks pievienotas virs limita.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {Tiks pievienota 1 papildu administratora vieta virs limita} other {# papildu administratora vietas tiks pievienotas virs limita}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {Tiks pievienota 1 papildu vadītāja vieta virs limita} other {# tiks pievienotas papildu vadītāja vietas virs limita}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Apstipriniet un i<PERSON>ū<PERSON>t i<PERSON>ūgumus", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Apstipriniet ietekmi uz sēdekļ<PERSON>", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "<PERSON><PERSON><PERSON> plānā ir sasniegts pieejamo vietu limits.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Šī projekta administratori un vadītāji", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Tikai administratori un sadarbības partneri", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "<PERSON><PERSON> da<PERSON> var veikt tikai platformas administratori, mapju pārvaldnieki un projektu vadītāji.", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Piedalīties var ikviens, tostarp nereģistrēti lietotāji.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Atlase", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Piedalīties var lietotāji, kas pieder konkrētai(-ām) lietotāju grupai(-ām). Lietotāju grupas var pārvaldīt cilnē \"Lietotāji\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Ka<PERSON> var apskat<PERSON>t šo projektu?", "app.containers.phaseConfig.enableSimilarInputDetection": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON> ievades datu <PERSON>", "app.containers.phaseConfig.similarInputDetectionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ievades datu <PERSON>", "app.containers.phaseConfig.similarInputDetectionTooltip": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> līdz<PERSON>gus ievades datus, ka<PERSON><PERSON><PERSON> vi<PERSON>i r<PERSON>, lai izva<PERSON> no dublēša<PERSON>.", "app.containers.phaseConfig.similarityThresholdBody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ķermenis)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "<PERSON><PERSON>, cik līdzīgiem jābūt diviem aprakstiem, lai tie tiktu atzīmēti kā līdzīgi. Izmantojiet vērtību no 0 (stingri) līdz 1 (ma<PERSON>). Zemākas vērtības nod<PERSON>, bet precīzāku atbilstību.", "app.containers.phaseConfig.similarityThresholdTitle": "<PERSON>īd<PERSON><PERSON><PERSON> (nosaukums)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "<PERSON><PERSON>, cik līdzīgiem jābūt diviem nosaukumiem, lai tie tiktu atzīmēti kā līdzīgi. Izmantojiet vērtību no 0 (stingri) līdz 1 (ma<PERSON>). Zemākas vērtības nod<PERSON>, bet precīzāku atbilstību.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "<PERSON>ī funkcija ir pieejama kā daļa no agrīnās piekļuves piedāvājuma līdz 2025. gada 30. jūnijam. Ja vēlaties to turpināt izmantot arī pēc <PERSON>, sazinieties ar savu valdības veiksmes menedžeri vai administratoru, lai apspriestu aktivizēšanas iespējas.", "app.containers.survey.sentiment.noAnswers2": "Šobrīd atbildes nav saņemtas.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 komentāri} one {1 komentārs} other {# komentāri}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Dalī<PERSON>nie<PERSON> ir lietotāji vai apmeklētāji, kas ir piedalījušies projektā, publicējuš<PERSON> priekšlikumu vai sazinājušies ar to, vai apmeklējuši pasākumus.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lī<PERSON>", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> k<PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ts", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automatizētas kampaņas", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automatizēti e-pasta ziņojumi", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "No {quantity} kampaņām", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Pielāgotie e-pasta ziņ<PERSON>jumi", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "E-pasti", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Ko<PERSON><PERSON><PERSON><PERSON> nosūtīto e-pasta vēstuļu skaits", "app.modules.commercial.analytics.admin.components.Events.completed": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.events": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON> notikumu s<PERSON>ts", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Drī<PERSON>mā", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.pending": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON><PERSON><PERSON> s<PERSON>ts", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "<PERSON>et uz ievades pā<PERSON>ldnieku", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "<PERSON><PERSON><PERSON> dati", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "<PERSON><PERSON>ī<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "<PERSON><PERSON><PERSON><PERSON>, kas nav arhivēti un redzami mājaslapas tabulā 'Aktīvs'", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Arhivēts", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Projektu sagataves", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Šeit tiek uzskaitīti visi arhivētie un aktīvie laika līnijas projekti", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projekti", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Ko<PERSON><PERSON><PERSON><PERSON> projektu skaits", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Kopējais platformā redzamo projektu skaits", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Jaunas reģistrācijas", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Reģistrācijas rādītājs", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> k<PERSON> par reģistrētiem lie<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Reģistrācijas", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Kop<PERSON><PERSON><PERSON> reģistrāciju skaits", "app.modules.commercial.analytics.admin.components.Tab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 30 dienas:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 7 dienas:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Lapas skatījumu skaits vienā apmeklējumā", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Apmeklētāji\" ir un<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON> s<PERSON>. Ja persona apmeklē platformu vairākas reizes, tā tiek ieskaitīta tikai vienu reizi.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Apmeklē<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Apmeklējumi\" ir sesiju skaits. Ja persona apmeklē platformu vair<PERSON> reizes, tiek ieskait<PERSON>ts katrs apmeklējums.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "<PERSON><PERSON><PERSON>:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Count", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Valoda", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Apmeklē<PERSON><PERSON><PERSON> s<PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Apmek<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "klikšķiniet šeit", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "<PERSON> pilnu no<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> {referrerListButton}.", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Apmeklē<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.totalParticipants": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ts", "app.modules.commercial.analytics.admin.containers.visitors.noData": "<PERSON><PERSON><PERSON><PERSON><PERSON> nav apm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Esam mainīju<PERSON> veidu, kā vācam un rādām apmeklētāju datus. Rezu<PERSON><PERSON><PERSON><PERSON> apmeklētāju dati ir precīzāki un pieejami vairāk datu veidu, vien<PERSON>kus saglabājot atbilstību GDPR. <PERSON> gan apmeklētāju laika grafikā izmantotie dati ir senāki, datus \"Apmeklējuma ilgumam\", \"Apmeklējuma skatījumu\" un citiem grafikiem sākām vākt tikai 2024. gada novembr<PERSON>, tāpēc pirms tam dati nav pieejami. T<PERSON><PERSON><PERSON><PERSON>, ja izvēlaties datus pirms 2024. gada novembra, ņemiet vērā, ka dažas diagrammas var būt tukšas vai izskatīties dīvaini.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "E-pasta sūtī<PERSON>mi laika gaitā", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON> laika gaitā", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Reģistrācijas laika gaitā", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Datums", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistika", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "<PERSON><PERSON><PERSON><PERSON><PERSON> statistika", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Apmeklējumi un apmeklētāji laika gaitā", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "<PERSON><PERSON><PERSON> visā periodā", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Skaits", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Valoda", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Apmeklē<PERSON><PERSON><PERSON> s<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Apmek<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Atsauces vietnes", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Meklētājprogrammas", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO pāradresējumi", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Apmeklē<PERSON><PERSON> s<PERSON>ts", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Tīmekļ<PERSON> v<PERSON>", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Šo satura karodziņu varat noņemt, izvēloties šo elementu un noklikšķinot uz pogu Noņemt augšpusē. Pēc tam tas atkal parādīsies cilnēs Apskatīts vai Jauns.", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Nepiemērota satura automātiska note<PERSON>.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "<PERSON><PERSON><PERSON> nav iesniegusi nevienu ierakstu pārskatīšanai vai mūsu dabiskās valodas apstrādes sistēma nav atzī<PERSON><PERSON><PERSON><PERSON> to saturu kā nepie<PERSON>ē<PERSON>.", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Noņemt {numberOfItems, plural, one {content warning} other {# content warnings}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Platformas lietotājs par to ziņoja kā par nepiemērotu.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "<PERSON><PERSON> brī<PERSON>", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Na<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>as", "app.modules.navbar.admin.containers.addProject": "Projekta pievienošana navigācijas j<PERSON>lai", "app.modules.navbar.admin.containers.createCustomPageButton": "Izveidot pie<PERSON>ā<PERSON> lapu", "app.modules.navbar.admin.containers.deletePageConfirmation": "Vai esat pārl<PERSON>cināts, ka vēlaties dzēst šo lapu? Šo darbību nevar atsaukt. J<PERSON>s varat arī izņemt lapu no navigācijas jos<PERSON>, ja vēl neesat gatavs to izdzēst.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Navigācijas joslā var pievienot tikai līdz 5 vienībām.", "app.modules.navbar.admin.containers.pageHeader": "Lapas un izvēlne", "app.modules.navbar.admin.containers.pageSubtitle": "Navigācijas joslā papildus sākuma un projektu lapām var tikt parādītas līdz pat piecām lapām. J<PERSON>s varat pārdēvēt izvēlnes elementus, mainīt to secību un pievienot jaunas lapas ar savu saturu.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Izmantojiet zemāk redzamo ☰ ikonu, lai ievilktu AI saturu savā ziņojumā.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Nav pieejami AI ieskati. Tās varat izveidot savā projektā.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Iet uz projektu", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Izvēlē<PERSON> fāzi", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Atbloķējiet AI analīzi", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "<PERSON>elieciet mākslīgā intelekta radītos ieskatus savā pārskatā", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar mākslīgo intelektu", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "<PERSON><PERSON>ņ<PERSON>šana ar AI nav iekļauta jūsu pašreizējā plānā. <PERSON> at<PERSON>lē<PERSON>u <PERSON>, sazinieties ar savu valdības veiksmes menedžeri.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Tas nav iekļ<PERSON>s jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Grupēšana pēc reģistrācijas lauka", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Grupēt pēc a<PERSON> j<PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Grupas režīms", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Aptaujas atbilžu grupēšana pēc reģistrācijas laukiem (dzimums, atrašanās vieta, vecums utt.) vai citiem aptaujas jautājumiem.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "Nav", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Reģistrācijas lauks", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Apsekojuma posms", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Vai esat pārl<PERSON>, ka vēlaties to dzēst?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "Atcelt", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "Rediģēt", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Publicējiet savu komentāru", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Rakstiet savu koment<PERSON><PERSON>eit", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Noklikšķiniet uz pogas z<PERSON>, lai sekotu vai atceltu sekot. Projektu skaits ir norādīts iekavās.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "Gatavs", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Sekojiet preferencēm", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "<PERSON><PERSON><PERSON><PERSON> nav aktīvu projektu, <PERSON><PERSON><PERSON> vēr<PERSON> jū<PERSON> sekot <PERSON>.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "<PERSON><PERSON><PERSON> logrīkā tiek rādīti projekti, kas saistīti ar lietotāja sekojamajām \"jomām\". Ņemiet vērā, ka jūsu platformā \"apgabaliem\" var izmantot citu nosaukumu - skatiet platformas iestatījumu cilni \"Apgabali\". Ja lietotājs vēl neseko nevienai jomai, logrīks parādīs pieejam<PERSON> joma<PERSON>, kurām sekot. Šajā gadījumā logrīks rādīs ne vairāk kā 100 apgabalus.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Nav pieejami publicēti projekti vai mapes", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Publicētie projekti un mapes", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "<PERSON><PERSON> logrīks attēlo pašlaik publicētos projektus un mapes, ievērojot projektu lapā noteikto secību. <PERSON><PERSON> uzvedība ir tāda pati kā \"veco\" projektu logrīka cilnē \"aktīvais\".", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Nav atlasīti projekti vai mapes", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Atlasiet projektus vai mapes", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Atlasītie projekti un mapes", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "<PERSON><PERSON><PERSON><PERSON><PERSON>, varat izvēlēties un noteikt kārtību, kād<PERSON> vēlat<PERSON>, lai projekti vai mapes tiktu rādītas lietotājiem.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projekti", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "apmeklējiet mūsu atbalsta centru", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Plašāku informāciju par ieteicamo attēlu izšķirtspēju skatiet vietnē {supportPageLink}."}