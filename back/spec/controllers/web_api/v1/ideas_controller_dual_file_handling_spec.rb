# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WebApi::V1::IdeasController, type: :controller do
  let(:user) { create(:user) }
  let(:project) { create(:single_phase_ideation_project) }
  let(:phase) { project.phases.first }
  let!(:custom_form) { create(:custom_form, participation_context: project) }
  let!(:file_upload_field) do
    create(
      :custom_field,
      resource: custom_form,
      input_type: 'file_upload',
      key: 'file_field',
      enabled: true,
      title_multiloc: { 'en' => 'Upload a file' }
    )
  end

  before do
    request.headers['Content-Type'] = 'application/json'
    token = AuthToken::AuthToken.new(payload: user.to_token_payload).token
    request.headers['Authorization'] = "Bearer #{token}"
  end

  describe '#update_file_upload_fields' do
    context 'when idea has legacy FileUpload records' do
      let(:idea) { create(:idea, author: user, project: project) }
      let!(:existing_file_upload) { create(:file_upload, idea: idea) }

      it 'uses legacy FileUpload system for existing file reference' do
        params = {
          'custom_field_values' => {
            'file_field' => {
              'id' => existing_file_upload.id
            }
          }
        }

        patch :update, params: { id: idea.id, idea: { custom_field_values: params['custom_field_values'] } }

        expect(response).to have_http_status(:ok)
        expect(idea.reload.custom_field_values['file_field']).to eq({
          'id' => existing_file_upload.id,
          'name' => existing_file_upload.name
        })
      end

      it 'uses legacy FileUpload system for new file upload' do
        file_content = 'data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO4CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQlQKL0YxIDEyIFRmCjcyIDcyMCBUZAooSGVsbG8gV29ybGQpIFRqCkVUCmVuZHN0cmVhbQplbmRvYmoKCjMgMCBvYmoKNDQKZW5kb2JqCgoxIDAgb2JqCjw8Ci9UeXBlIC9QYWdlCi9QYXJlbnQgNCAwIFIKL1Jlc291cmNlcyA8PAovRm9udCA8PAovRjEgNSAwIFIKPj4KPj4KL01lZGlhQm94IFswIDAgNjEyIDc5Ml0KL0NvbnRlbnRzIDIgMCBSCj4+CmVuZG9iagoKNCAwIG9iago8PAovVHlwZSAvUGFnZXMKL0tpZHMgWzEgMCBSXQovQ291bnQgMQo+PgplbmRvYmoKCjUgMCBvYmoKPDwKL1R5cGUgL0ZvbnQKL1N1YnR5cGUgL1R5cGUxCi9CYXNlRm9udCAvSGVsdmV0aWNhCj4+CmVuZG9iagoKNiAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgNCAwIFIKPj4KZW5kb2JqCgp4cmVmCjAgNwowMDAwMDAwMDAwIDY1NTM1IGYgCjAwMDAwMDAwNzMgMDAwMDAgbiAKMDAwMDAwMDAxOSAwMDAwMCBuIAowMDAwMDAwMTc4IDAwMDAwIG4gCjAwMDAwMDAzNjQgMDAwMDAgbiAKMDAwMDAwMDQ2NiAwMDAwMCBuIAowMDAwMDAwNTY5IDAwMDAwIG4gCnRyYWlsZXIKPDwKL1NpemUgNwovUm9vdCA2IDAgUgo+PgpzdGFydHhyZWYKNjI1CiUlRU9G'
        params = {
          'custom_field_values' => {
            'file_field' => {
              'content' => file_content,
              'name' => 'test.pdf'
            }
          }
        }

        expect {
          patch :update, params: { id: idea.id, idea: { custom_field_values: params['custom_field_values'] } }
        }.to change(FileUpload, :count).by(1)

        expect(response).to have_http_status(:ok)
        new_file_upload = FileUpload.last
        expect(new_file_upload.name).to eq('test.pdf')
        expect(idea.reload.custom_field_values['file_field']).to eq({
          'id' => new_file_upload.id,
          'name' => 'test.pdf'
        })
      end
    end

    context 'when idea has no legacy FileUpload records' do
      let(:idea) { create(:idea, author: user, project: project) }

      it 'uses Files::FileAttachment system for existing file reference' do
        # Create a Files::FileAttachment for the idea
        files_file = create(:file, uploader: user)
        files_file.files_projects.create!(project: project)
        file_attachment = Files::FileAttachment.create!(
          file: files_file,
          attachable: idea,
          position: 1
        )

        params = {
          'custom_field_values' => {
            'file_field' => {
              'id' => file_attachment.id
            }
          }
        }

        patch :update, params: { id: idea.id, idea: { custom_field_values: params['custom_field_values'] } }

        expect(response).to have_http_status(:ok)
        expect(idea.reload.custom_field_values['file_field']).to eq({
          'id' => file_attachment.id,
          'name' => files_file.name
        })
      end

      it 'uses Files::FileAttachment system for new file upload' do
        file_content = 'data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO4CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQlQKL0YxIDEyIFRmCjcyIDcyMCBUZAooSGVsbG8gV29ybGQpIFRqCkVUCmVuZHN0cmVhbQplbmRvYmoKCjMgMCBvYmoKNDQKZW5kb2JqCgoxIDAgb2JqCjw8Ci9UeXBlIC9QYWdlCi9QYXJlbnQgNCAwIFIKL1Jlc291cmNlcyA8PAovRm9udCA8PAovRjEgNSAwIFIKPj4KPj4KL01lZGlhQm94IFswIDAgNjEyIDc5Ml0KL0NvbnRlbnRzIDIgMCBSCj4+CmVuZG9iagoKNCAwIG9iago8PAovVHlwZSAvUGFnZXMKL0tpZHMgWzEgMCBSXQovQ291bnQgMQo+PgplbmRvYmoKCjUgMCBvYmoKPDwKL1R5cGUgL0ZvbnQKL1N1YnR5cGUgL1R5cGUxCi9CYXNlRm9udCAvSGVsdmV0aWNhCj4+CmVuZG9iagoKNiAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgNCAwIFIKPj4KZW5kb2JqCgp4cmVmCjAgNwowMDAwMDAwMDAwIDY1NTM1IGYgCjAwMDAwMDAwNzMgMDAwMDAgbiAKMDAwMDAwMDAxOSAwMDAwMCBuIAowMDAwMDAwMTc4IDAwMDAwIG4gCjAwMDAwMDAzNjQgMDAwMDAgbiAKMDAwMDAwMDQ2NiAwMDAwMCBuIAowMDAwMDAwNTY5IDAwMDAwIG4gCnRyYWlsZXIKPDwKL1NpemUgNwovUm9vdCA2IDAgUgo+PgpzdGFydHhyZWYKNjI1CiUlRU9G'
        params = {
          'custom_field_values' => {
            'file_field' => {
              'content' => file_content,
              'name' => 'test.pdf'
            }
          }
        }

        expect {
          patch :update, params: { id: idea.id, idea: { custom_field_values: params['custom_field_values'] } }
        }.to change(Files::FileAttachment, :count).by(1)
          .and change(Files::File, :count).by(1)

        expect(response).to have_http_status(:ok)
        new_file_attachment = Files::FileAttachment.last
        expect(new_file_attachment.file.name).to eq('test.pdf')
        expect(idea.reload.custom_field_values['file_field']).to eq({
          'id' => new_file_attachment.id,
          'name' => 'test.pdf'
        })
      end
    end
  end
end
